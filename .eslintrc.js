module.exports = {
    extends: ['eslint:recommended', 'plugin:vue/recommended'],
    parserOptions: {
        ecmaVersion: 2020,
        sourceType: 'module',
    },
    env: {
        amd: true,
        browser: true,
        es6: true,
    },
    rules: {
        indent: ['warn', 4],
        quotes: ['warn', 'single'],
        semi: ['warn', 'never'],
        'no-unused-vars': ['error', {vars: 'all', args: 'after-used', ignoreRestSiblings: true}],
        'comma-dangle': ['warn', 'always-multiline'],
        'vue/max-attributes-per-line': 'off',
        'vue/require-default-prop': 'off',
        'vue/singleline-html-element-content-newline': 'off',
        'vue/html-self-closing': [
            'warn',
            {
                html: {
                    void: 'always',
                    normal: 'always',
                    component: 'always',
                },
            },
        ],
        'vue/html-indent': ['warn', 4],
    },
}
