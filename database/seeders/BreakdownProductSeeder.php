<?php

namespace Database\Seeders;

use App\Models\BreakdownProduct;
use Illuminate\Database\Seeder;

class BreakdownProductSeeder extends Seeder
{
    protected $products = [
        [
            'name' => 'Protego Rescue Recovery',
            'period' => 36,
        ],
        [
            'name' => 'Protego Rescue Recovery',
            'period' => 24,
        ],
        [
            'name' => 'Protego Rescue Recovery',
            'period' => 18,
        ],
        [
            'name' => 'Protego Rescue Recovery',
            'period' => 15,
        ],
        [
            'name' => 'Protego Rescue Recovery',
            'period' => 12,
        ],
        [
            'name' => 'Protego Rescue Recovery',
            'period' => 6,
        ],
        [
            'name' => 'Protego Rescue Recovery',
            'period' => 3,
        ],
    ];

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        foreach ($this->products as $index => $product) {
            BreakdownProduct::query()->updateOrCreate([
                'period' => $product['period'],
            ], [
                'name' => $product['name'],
                'position' => $index + 1,
            ]);
        }
    }
}
