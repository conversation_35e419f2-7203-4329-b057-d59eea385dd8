<?php

namespace Database\Seeders;

use App\Models\ServiceType;
use Illuminate\Database\Seeder;

class ServiceTypeSeeder extends Seeder
{
    protected $types = [
        'Minor Service',
        'Major Service',
        'MOT',
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        foreach ($this->types as $index => $type) {
            ServiceType::updateOrCreate([
                'name' => $type,
                'position' => $index + 1,
            ]);
        }
    }
}
