<?php

namespace Database\Seeders;

use App\Models\AccountWarrantyProduct;
use App\Models\Warranty;
use Illuminate\Database\Seeder;

class WarrantyAnnualMileageLimitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Warranty::query()
            ->each(function (Warranty $warranty) {
                $accountProduct = AccountWarrantyProduct::query()
                    ->where('account_id', $warranty->account_id)
                    ->where('product_id', $warranty->product_id)
                    ->firstOrFail();
                $warranty->annual_mileage_limit = $accountProduct->annual_mileage_limit;
                $warranty->save();
            });
    }
}
