<?php

namespace Database\Seeders;

use App\Models\Document;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class DocumentSeeder extends Seeder
{
    protected $filesToProcess = [
        'warranty_cover/e_v_gold.md',
        'warranty_cover/e_v_platinum.md',
        'warranty_cover/gold.md',
        'warranty_cover/hybrid_gold.md',
        'warranty_cover/hybrid_platinum.md',
        'warranty_cover/platinum.md',
        'warranty_cover/silver.md',
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        collect(File::allFiles(resource_path('markdown')))
            ->filter(fn (\SplFileInfo $file) => $file->getExtension() === 'md')
            ->filter(fn (\SplFileInfo $file) => in_array($file->getRelativePathname(), $this->filesToProcess))
            ->each(function (\SplFileInfo $file) {
                $slug = Str::of($file->getRelativePathname())->before('.md')->replace('/', '-')->slug();
                $title = $slug->replace('-', ' ')->title();
                $content = $file->getContents();

                $document = Document::query()->updateOrCreate([
                    'slug' => $slug,
                ], [
                    'title' => $title,
                ]);

                $document->versions()->create([
                    'content' => $content,
                    'version_number' => 1,
                    'created_at' => $document->versions()->exists() ? now() : '2022-01-01 00:00:00',
                ]);
            });
    }
}
