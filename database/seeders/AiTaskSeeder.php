<?php

namespace Database\Seeders;

use App\Services\AI\WarrantyClaimAssessorTask;
use App\Services\AI\WarrantyClaimRejectionMessageWriterTask;
use Illuminate\Database\Seeder;

class AiTaskSeeder extends Seeder
{
    protected $tasks = [
        [
            'name' => 'Warranty Claim Assessor',
            'processor_class' => WarrantyClaimAssessorTask::class,
            'prompt' => [
                'model' => 'claude-3-7-sonnet-latest',
                'version_number' => 1,
                'change_notes' => 'First',
                'prompt' => <<<'EOT'
                    You are a warranty claims assessor for a motor vehicle warranty company. Your job is to evaluate claims based on the provided warranty coverage document and determine if the claim should be approved or rejected. Always provide a clear decision and justify it using specific clauses or terms from the warranty.

                        Focus on:
                        1. Identifying whether the claimed issue and part are covered.
                        2. Determining if the failure is due to a sudden or unexpected mechanical or electrical fault.
                        3. Applying exclusions such as wear and tear or any other limitations described in the warranty.
                    EOT,
            ],
        ],
        [
            'name' => 'Warranty Claim Rejection Message Writer',
            'processor_class' => WarrantyClaimRejectionMessageWriterTask::class,
            'prompt' => [
                'model' => 'claude-3-7-sonnet-latest',
                'version_number' => 1,
                'change_notes' => 'First',
                'prompt' => <<<'EOT'
                    You are a warranty claims assessor for a motor vehicle warranty company. You have the task of writing to a customer and explaining why the claim has not been approved. Your tone should be sympathetic
                    but firm and unwavering.

                        Focus on:
                        1. Focus on the reason given in the rejection
                        2. The notes given in the reason.
                    EOT,
            ],
        ],
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        foreach ($this->tasks as $task) {
            $taskModel = \App\Models\AiTask::updateOrCreate([
                'processor_class' => $task['processor_class'],
            ], [
                'name' => $task['name'],
            ]);
            $taskModel->prompt()->updateOrCreate(
                ['version_number' => $task['prompt']['version_number']],
                $task['prompt']
            );
        }
    }
}
