<?php

namespace Database\Seeders;

use App\Models\Account;
use Illuminate\Database\Seeder;

class AccountServiceCredentialsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (app()->isProduction()) {
            abort(403, 'This seeder can not be run in production');
        }

        Account::first()->serviceCredentials()->create([
            'provider' => 'payment_assist',
            'is_live' => false,
            'credentials' => [
                'api_key' => env('PAYMENT_ASSIST_API_KEY'),
                'api_secret' => env('PAYMENT_ASSIST_API_SECRET'),
            ],
        ]);
    }
}
