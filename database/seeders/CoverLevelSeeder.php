<?php

namespace Database\Seeders;

use App\Enums\VehicleType;
use App\Models\CoverLevel;
use App\Models\Document;
use Illuminate\Database\Seeder;

class CoverLevelSeeder extends Seeder
{
    protected $coverLevels = [
        [
            'name' => 'Platinum',
            'vehicle_type' => VehicleType::CAR->value,
            'is_ice' => true,
            'is_ev' => false,
            'document' => 'warranty-cover-platinum',
        ],
        [
            'name' => 'Gold',
            'vehicle_type' => VehicleType::CAR->value,
            'is_ice' => true,
            'is_ev' => false,
            'document' => 'warranty-cover-gold',
        ],
        [
            'name' => 'Silver',
            'vehicle_type' => VehicleType::CAR->value,
            'is_ice' => true,
            'is_ev' => false,
            'document' => 'warranty-cover-silver',
        ],
        [
            'name' => 'EV Platinum',
            'vehicle_type' => VehicleType::CAR->value,
            'is_ice' => false,
            'is_ev' => true,
            'document' => 'warranty-cover-e-v-platinum',
        ],
        [
            'name' => 'EV Gold',
            'vehicle_type' => VehicleType::CAR->value,
            'is_ice' => false,
            'is_ev' => true,
            'document' => 'warranty-cover-e-v-gold',
        ],
        [
            'name' => 'Hybrid Platinum',
            'vehicle_type' => VehicleType::CAR->value,
            'is_ice' => true,
            'is_ev' => true,
            'document' => 'warranty-cover-hybrid-platinum',
        ],
        [
            'name' => 'Hybrid Gold',
            'vehicle_type' => VehicleType::CAR->value,
            'is_ice' => true,
            'is_ev' => true,
            'document' => 'warranty-cover-hybrid-gold',
        ],
        [
            'name' => 'Campervan / Motorhome Gold',
            'vehicle_type' => VehicleType::MOTORHOME->value,
            'is_ice' => true,
            'is_ev' => false,
            'document' => 'warranty-cover-gold',
        ],
        [
            'name' => 'Platinum Light Commercial',
            'vehicle_type' => VehicleType::VAN->value,
            'is_ice' => true,
            'is_ev' => false,
            'document' => 'warranty-cover-platinum',
        ],
        [
            'name' => 'Gold Light Commercial',
            'vehicle_type' => VehicleType::VAN->value,
            'is_ice' => true,
            'is_ev' => false,
            'document' => 'warranty-cover-gold',
        ],
        [
            'name' => 'Silver Light Commercial',
            'vehicle_type' => VehicleType::VAN->value,
            'is_ice' => true,
            'is_ev' => false,
            'document' => 'warranty-cover-silver',
        ],
    ];

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        foreach ($this->coverLevels as $coverLevel) {
            $document = Document::where('slug', $coverLevel['document'] ?? null)->first();
            CoverLevel::query()->updateOrCreate([
                'name' => $coverLevel['name'],
            ], [
                'vehicle_type' => $coverLevel['vehicle_type'],
                'document_id' => $document?->id,
                'is_ice' => $coverLevel['is_ice'],
                'is_ev' => $coverLevel['is_ev'],
            ]);
        }
    }
}
