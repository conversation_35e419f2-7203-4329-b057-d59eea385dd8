<?php

namespace Database\Seeders;

use App\Models\FaultType;
use Illuminate\Database\Seeder;

class FaultTypeSeeder extends Seeder
{
    protected $faultTypes = [
        'Engine',
        'Clutch',
        'Transmission',
        'Drivetrain',
        'Fuel System',
        'Hybrid/Electric Components',

        // Chassis Systems
        'Suspension',
        'Steering',
        'Braking System',

        // Vehicle Systems
        'Cooling System',
        'Electrical System',
        'Exhaust System',
        'AC System',
        'Lighting System',

        // Body & Interior
        'Exterior Body',
        'Interior Components',
        'Safety Systems',
        'Infotainment System',
        'Accessories',

        // Other
        'Fluid Leaks',
        'Unusual Noises',
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        foreach ($this->faultTypes as $index => $faultType) {
            FaultType::updateOrCreate([
                'name' => $faultType,
            ], [
                'position' => $index + 1,
            ]);
        }
    }
}
