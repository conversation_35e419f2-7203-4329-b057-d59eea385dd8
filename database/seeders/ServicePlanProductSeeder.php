<?php

namespace Database\Seeders;

use App\Models\ServicePlanProduct;
use Illuminate\Database\Seeder;

class ServicePlanProductSeeder extends Seeder
{
    protected $products = [
        [
            'name' => 'Membership Club',
            'is_pure_electric' => false,
        ],
        [
            'name' => 'Service Plan',
            'is_pure_electric' => false,
        ],
        [
            'name' => 'EV Membership Club',
            'is_pure_electric' => true,
        ],
        [
            'name' => 'EV Service Plan',
            'is_pure_electric' => true,
        ],
    ];

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $position = 1;
        foreach ([false, true] as $isRecurring) {
            foreach ($this->products as $product) {
                ServicePlanProduct::query()->updateOrCreate([
                    'name' => $product['name'],
                    'is_pure_electric' => $product['is_pure_electric'],
                    'is_recurring' => $isRecurring,
                ], [
                    'position' => $position++,
                ]);
            }
        }
    }
}
