<?php

namespace Database\Seeders;

use App\Models\VehicleComponent;
use App\Models\VehicleComponentCategory;
use Illuminate\Database\Seeder;

class VehicleComponentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // seed from CSV vehicle-components.csv using PHP class based file handling

        $file = new \SplFileObject(database_path('data/vehicle-components.csv'));
        $file->setFlags(\SplFileObject::READ_CSV);
        $rows = [];
        $file->seek(1);

        while (! $file->eof()) {
            $row = $file->fgetcsv();
            if (! empty($row[0])) {
                $rows[] = $row;
            }
        }

        VehicleComponentCategory::upsert(
            collect($rows)
                ->pluck(1)
                ->unique()
                ->values()
                ->map(fn ($name) => ['name' => $name])
                ->toArray(),
            ['name']
        );

        $categories = VehicleComponentCategory::pluck('id', 'name');

        $upsert = collect($rows)
            ->map(function ($row) use ($categories) {
                return [
                    'vehicle_component_category_id' => $categories[$row[1]],
                    'name' => $row[2],
                    'applicable_to_ice' => $row[3] === 'TRUE',
                    'applicable_to_ev' => $row[4] === 'TRUE',
                ];
            });

        VehicleComponent::upsert($upsert->toArray(), ['vehicle_component_category_id', 'name']);

    }
}
