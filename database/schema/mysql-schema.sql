/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `account_breakdown_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `account_breakdown_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `breakdown_product_id` bigint unsigned NOT NULL,
  `max_age` int unsigned DEFAULT NULL,
  `max_mileage` int unsigned DEFAULT NULL,
  `max_engine_capacity` int unsigned DEFAULT NULL,
  `provision` decimal(7,2) NOT NULL,
  `selling_price` decimal(7,2) NOT NULL,
  `admin_fee` decimal(7,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_breakdown_product_account_id_foreign` (`account_id`),
  KEY `account_breakdown_product_breakdown_product_id_foreign` (`breakdown_product_id`),
  CONSTRAINT `account_breakdown_product_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `account_breakdown_product_breakdown_product_id_foreign` FOREIGN KEY (`breakdown_product_id`) REFERENCES `breakdown_products` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `account_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `account_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `product_id` bigint unsigned NOT NULL,
  `cost_price` decimal(10,2) unsigned DEFAULT NULL,
  `selling_price` decimal(10,2) unsigned DEFAULT NULL,
  `dealer_commission` decimal(10,2) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_products_account_id_foreign` (`account_id`),
  KEY `account_products_product_id_foreign` (`product_id`),
  CONSTRAINT `account_products_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_products_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `account_service_credentials`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `account_service_credentials` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `provider` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_live` tinyint(1) NOT NULL DEFAULT '0',
  `credentials` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_service_credentials_account_id_provider_is_live_unique` (`account_id`,`provider`,`is_live`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `account_service_plan_product_service_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `account_service_plan_product_service_type` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_service_plan_product_id` bigint unsigned NOT NULL,
  `service_type_id` bigint unsigned NOT NULL,
  `limit` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_service_plan_product_foreign` (`account_service_plan_product_id`),
  KEY `service_type_foreign` (`service_type_id`),
  CONSTRAINT `account_service_plan_product_foreign` FOREIGN KEY (`account_service_plan_product_id`) REFERENCES `account_service_plan_products` (`id`) ON DELETE CASCADE,
  CONSTRAINT `service_type_foreign` FOREIGN KEY (`service_type_id`) REFERENCES `service_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `account_service_plan_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `account_service_plan_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `service_plan_product_id` bigint unsigned NOT NULL,
  `max_engine_capacity` int unsigned DEFAULT NULL,
  `duration_years` tinyint unsigned DEFAULT NULL,
  `selling_price` decimal(7,2) NOT NULL,
  `admin_fee` decimal(7,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_service_plan_product_account_id_foreign` (`account_id`),
  KEY `account_service_plan_product_service_plan_product_id_foreign` (`service_plan_product_id`),
  CONSTRAINT `account_service_plan_product_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`),
  CONSTRAINT `account_service_plan_product_service_plan_product_id_foreign` FOREIGN KEY (`service_plan_product_id`) REFERENCES `service_plan_products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `account_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `account_user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_user_account_id_user_id_unique` (`account_id`,`user_id`),
  KEY `account_user_user_id_foreign` (`user_id`),
  CONSTRAINT `account_user_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `account_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `account_warranty_breakdown_product_bundles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `account_warranty_breakdown_product_bundles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_warranty_product_id` bigint unsigned NOT NULL,
  `breakdown_product_id` bigint unsigned NOT NULL,
  `provision` decimal(7,2) unsigned NOT NULL,
  `admin_fee` decimal(7,2) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `warranty_breakdown_bundle_warranty_product_foreign` (`account_warranty_product_id`),
  KEY `warranty_breakdown_bundle_breakdown_product_foreign` (`breakdown_product_id`),
  CONSTRAINT `warranty_breakdown_bundle_breakdown_product_foreign` FOREIGN KEY (`breakdown_product_id`) REFERENCES `breakdown_products` (`id`),
  CONSTRAINT `warranty_breakdown_bundle_warranty_product_foreign` FOREIGN KEY (`account_warranty_product_id`) REFERENCES `account_warranty_products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `account_warranty_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `account_warranty_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `product_id` bigint unsigned NOT NULL,
  `max_engine_capacity` int unsigned DEFAULT NULL,
  `max_mileage` int unsigned DEFAULT NULL,
  `annual_mileage_limit` int unsigned DEFAULT NULL,
  `max_age` int unsigned DEFAULT NULL,
  `provision` decimal(7,2) NOT NULL,
  `selling_price` decimal(7,2) NOT NULL,
  `admin_fee` decimal(7,2) NOT NULL,
  `monthly_selling_price` decimal(7,2) unsigned DEFAULT NULL,
  `monthly_admin_fee` decimal(7,2) unsigned DEFAULT NULL,
  `individual_claim_limit` decimal(7,2) NOT NULL,
  `total_claim_limit` decimal(7,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_aggregate_unique` (`account_id`,`product_id`,`max_engine_capacity`,`max_mileage`,`max_age`,`annual_mileage_limit`),
  KEY `account_warranty_products_product_id_foreign` (`product_id`),
  CONSTRAINT `account_warranty_products_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`),
  CONSTRAINT `account_warranty_products_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `warranty_products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `accounting_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accounting_contacts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `accounting_software_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_line_1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_line_2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_line_3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_line_4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_region` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_postcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `accounting_contacts_accounting_software_id_unique` (`accounting_software_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `short_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `authorisation_contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `max_hourly_labour_rate` decimal(5,2) NOT NULL,
  `warranty_self_funded` tinyint(1) NOT NULL,
  `breakdown_self_funded` tinyint(1) NOT NULL,
  `send_contract_emails` tinyint(1) NOT NULL DEFAULT '1',
  `logo_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `claim_handling_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ai_prompts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ai_prompts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ai_task_id` bigint unsigned NOT NULL,
  `version_number` int unsigned NOT NULL,
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `change_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ai_prompts_ai_task_id_foreign` (`ai_task_id`),
  CONSTRAINT `ai_prompts_ai_task_id_foreign` FOREIGN KEY (`ai_task_id`) REFERENCES `ai_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ai_request_scores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ai_request_scores` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ai_request_id` bigint unsigned NOT NULL,
  `rating` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ai_request_scores_ai_request_id_unique` (`ai_request_id`),
  CONSTRAINT `ai_request_scores_ai_request_id_foreign` FOREIGN KEY (`ai_request_id`) REFERENCES `ai_requests` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ai_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ai_requests` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ai_prompt_id` bigint unsigned NOT NULL,
  `subject_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject_id` bigint unsigned NOT NULL,
  `completion_model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `finish_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `prompt_tokens` int unsigned DEFAULT NULL,
  `completion_tokens` int unsigned DEFAULT NULL,
  `processing_ms` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ai_requests_ai_prompt_id_foreign` (`ai_prompt_id`),
  KEY `ai_requests_subject_type_subject_id_index` (`subject_type`,`subject_id`),
  CONSTRAINT `ai_requests_ai_prompt_id_foreign` FOREIGN KEY (`ai_prompt_id`) REFERENCES `ai_prompts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ai_tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ai_tasks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `processor_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ai_votes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ai_votes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ai_request_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `rating` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ai_votes_ai_request_id_foreign` (`ai_request_id`),
  KEY `ai_votes_user_id_foreign` (`user_id`),
  CONSTRAINT `ai_votes_ai_request_id_foreign` FOREIGN KEY (`ai_request_id`) REFERENCES `ai_requests` (`id`) ON DELETE CASCADE,
  CONSTRAINT `ai_votes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `authorised_services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `authorised_services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `service_plan_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `service_type_id` bigint unsigned NOT NULL,
  `authorisation_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `authorised_services_authorisation_code_unique` (`authorisation_code`),
  KEY `authorised_services_service_plan_id_foreign` (`service_plan_id`),
  KEY `authorised_services_user_id_foreign` (`user_id`),
  KEY `authorised_services_service_type_id_foreign` (`service_type_id`),
  CONSTRAINT `authorised_services_service_plan_id_foreign` FOREIGN KEY (`service_plan_id`) REFERENCES `service_plans` (`id`) ON DELETE CASCADE,
  CONSTRAINT `authorised_services_service_type_id_foreign` FOREIGN KEY (`service_type_id`) REFERENCES `service_types` (`id`),
  CONSTRAINT `authorised_services_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `billing_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `billing_requests` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `provider_customer_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `provider_billing_request_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mandate_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mandate_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `visited_at` timestamp NULL DEFAULT NULL,
  `mandate_activated_at` timestamp NULL DEFAULT NULL,
  `direct_debit_reference` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `account_holder_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort_code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `account_number` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `billing_requests_provider_customer_id_unique` (`provider_customer_id`),
  UNIQUE KEY `billing_requests_provider_billing_request_id_unique` (`provider_billing_request_id`),
  UNIQUE KEY `billing_requests_mandate_id_unique` (`mandate_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `breakdown_claims`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `breakdown_claims` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `breakdown_plan_id` bigint unsigned NOT NULL,
  `entered_by_id` bigint unsigned NOT NULL,
  `failure_date` date NOT NULL,
  `failure_mileage` int unsigned NOT NULL,
  `claim_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `reference` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `cause` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `cost` decimal(8,2) unsigned NOT NULL,
  `vat` decimal(7,2) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `breakdown_claims_breakdown_policy_id_foreign` (`breakdown_plan_id`),
  KEY `breakdown_claims_entered_by_id_foreign` (`entered_by_id`),
  CONSTRAINT `breakdown_claims_breakdown_policy_id_foreign` FOREIGN KEY (`breakdown_plan_id`) REFERENCES `breakdown_plans` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `breakdown_claims_entered_by_id_foreign` FOREIGN KEY (`entered_by_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `breakdown_plans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `breakdown_plans` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `sale_id` bigint unsigned NOT NULL,
  `breakdown_product_id` bigint unsigned NOT NULL,
  `is_self_funded` tinyint(1) NOT NULL,
  `provision` decimal(7,2) unsigned NOT NULL,
  `selling_price` decimal(7,2) unsigned NOT NULL,
  `admin_fee` decimal(7,2) unsigned NOT NULL,
  `vat` decimal(7,2) unsigned NOT NULL,
  `sales_vat` decimal(7,2) unsigned NOT NULL,
  `end_date` date NOT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `breakdown_policies_account_id_foreign` (`account_id`),
  KEY `breakdown_policies_breakdown_product_id_foreign` (`breakdown_product_id`),
  KEY `breakdown_policies_sale_id_foreign` (`sale_id`),
  CONSTRAINT `breakdown_plans_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE CASCADE,
  CONSTRAINT `breakdown_policies_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `breakdown_policies_breakdown_product_id_foreign` FOREIGN KEY (`breakdown_product_id`) REFERENCES `breakdown_products` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `breakdown_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `breakdown_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `position` tinyint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `period` tinyint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cache` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cache_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cache_locks` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `claim_authorisations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `claim_authorisations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `estimate_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `invoice_id` bigint unsigned DEFAULT NULL,
  `work_done` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `authorised_net` decimal(7,2) unsigned DEFAULT NULL,
  `authorised_gross` decimal(7,2) unsigned DEFAULT NULL,
  `settled_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `claim_authorisations_estimate_id_unique` (`estimate_id`),
  KEY `claim_authorisations_user_id_foreign` (`user_id`),
  KEY `claim_authorisations_invoice_id_foreign` (`invoice_id`),
  CONSTRAINT `claim_authorisations_estimate_id_foreign` FOREIGN KEY (`estimate_id`) REFERENCES `claim_estimates` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `claim_authorisations_invoice_id_foreign` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `claim_authorisations_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `claim_estimate_line_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `claim_estimate_line_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `claim_estimate_id` bigint unsigned NOT NULL,
  `vehicle_component_id` bigint unsigned DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `quantity` decimal(5,2) unsigned NOT NULL DEFAULT '1.00',
  `amount` decimal(7,2) unsigned NOT NULL,
  `vat` decimal(7,2) unsigned NOT NULL,
  `customer_contribution` decimal(7,2) unsigned DEFAULT NULL,
  `net` decimal(7,2) GENERATED ALWAYS AS (((`quantity` * `amount`) - coalesce(`customer_contribution`,0))) STORED COMMENT 'The net cost of the line item, excluding VAT',
  `total` decimal(7,2) unsigned GENERATED ALWAYS AS (((`quantity` * (`amount` + `vat`)) - coalesce(`customer_contribution`,0))) STORED,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `claim_estimate_line_items_claim_estimate_id_foreign` (`claim_estimate_id`),
  KEY `claim_estimate_line_items_vehicle_component_id_foreign` (`vehicle_component_id`),
  CONSTRAINT `claim_estimate_line_items_claim_estimate_id_foreign` FOREIGN KEY (`claim_estimate_id`) REFERENCES `claim_estimates` (`id`) ON DELETE CASCADE,
  CONSTRAINT `claim_estimate_line_items_vehicle_component_id_foreign` FOREIGN KEY (`vehicle_component_id`) REFERENCES `vehicle_components` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `claim_estimates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `claim_estimates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `claim_id` bigint unsigned NOT NULL,
  `repairer_id` bigint unsigned DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `workshop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `workshop_contact` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `workshop_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `workshop_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `workshop_address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `reference` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `estimate_booking_date` date DEFAULT NULL,
  `drop_off_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `estimate_completed_at` timestamp NULL DEFAULT NULL,
  `repair_date` date DEFAULT NULL,
  `work_required` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `current_mileage` int unsigned DEFAULT NULL,
  `is_charged_internally` tinyint(1) NOT NULL DEFAULT '0',
  `is_invoicing_dealer_direct` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `claim_estimates_claim_id_foreign` (`claim_id`),
  KEY `claim_estimates_user_id_foreign` (`user_id`),
  KEY `claim_estimates_repairer_id_foreign` (`repairer_id`),
  CONSTRAINT `claim_estimates_claim_id_foreign` FOREIGN KEY (`claim_id`) REFERENCES `claims` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `claim_estimates_repairer_id_foreign` FOREIGN KEY (`repairer_id`) REFERENCES `repairers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `claim_estimates_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `claim_rejections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `claim_rejections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `claim_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `claim_rejections_claim_id_foreign` (`claim_id`),
  KEY `claim_rejections_user_id_foreign` (`user_id`),
  CONSTRAINT `claim_rejections_claim_id_foreign` FOREIGN KEY (`claim_id`) REFERENCES `claims` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `claim_rejections_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `claims`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `claims` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `warranty_id` bigint unsigned NOT NULL,
  `fault_type_id` bigint unsigned NOT NULL,
  `entered_by_id` bigint unsigned DEFAULT NULL,
  `customer_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `customer_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reference` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failure_date` date NOT NULL,
  `current_mileage` int unsigned DEFAULT NULL,
  `vehicle_location` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `fault_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `terms_accepted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `claims_warranty_id_foreign` (`warranty_id`),
  KEY `claims_fault_type_id_foreign` (`fault_type_id`),
  KEY `claims_entered_by_id_foreign` (`entered_by_id`),
  CONSTRAINT `claims_entered_by_id_foreign` FOREIGN KEY (`entered_by_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `claims_fault_type_id_foreign` FOREIGN KEY (`fault_type_id`) REFERENCES `fault_types` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `claims_warranty_id_foreign` FOREIGN KEY (`warranty_id`) REFERENCES `warranties` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `comments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `subject_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject_id` bigint unsigned NOT NULL,
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `comments_user_id_foreign` (`user_id`),
  KEY `comments_related_type_related_id_index` (`subject_type`,`subject_id`),
  CONSTRAINT `comments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cover_level_vehicle_component`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cover_level_vehicle_component` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `cover_level_id` bigint unsigned NOT NULL,
  `vehicle_component_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `covered_components_unique` (`cover_level_id`,`vehicle_component_id`),
  KEY `cover_level_vehicle_component_vehicle_component_id_foreign` (`vehicle_component_id`),
  CONSTRAINT `cover_level_vehicle_component_cover_level_id_foreign` FOREIGN KEY (`cover_level_id`) REFERENCES `cover_levels` (`id`),
  CONSTRAINT `cover_level_vehicle_component_vehicle_component_id_foreign` FOREIGN KEY (`vehicle_component_id`) REFERENCES `vehicle_components` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cover_levels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cover_levels` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `document_id` bigint unsigned DEFAULT NULL,
  `vehicle_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_ice` tinyint(1) NOT NULL DEFAULT '0',
  `is_ev` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cover_levels_name_unique` (`name`),
  KEY `cover_levels_document_id_foreign` (`document_id`),
  CONSTRAINT `cover_levels_document_id_foreign` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `accounting_contact_id` bigint unsigned DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `address_1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `address_2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `county` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `postcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customers_account_id_foreign` (`account_id`),
  KEY `customers_accounting_contact_id_foreign` (`accounting_contact_id`),
  CONSTRAINT `customers_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `customers_accounting_contact_id_foreign` FOREIGN KEY (`accounting_contact_id`) REFERENCES `accounting_contacts` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `dealership_repairer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dealership_repairer` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `dealership_id` bigint unsigned NOT NULL,
  `repairer_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `dealership_repairer_dealership_id_repairer_id_unique` (`dealership_id`,`repairer_id`),
  KEY `dealership_repairer_repairer_id_foreign` (`repairer_id`),
  CONSTRAINT `dealership_repairer_dealership_id_foreign` FOREIGN KEY (`dealership_id`) REFERENCES `dealerships` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dealership_repairer_repairer_id_foreign` FOREIGN KEY (`repairer_id`) REFERENCES `repairers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `dealerships`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dealerships` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `accounting_contact_id` bigint unsigned DEFAULT NULL,
  `billing_request_id` bigint unsigned DEFAULT NULL,
  `no_direct_debit` tinyint(1) NOT NULL DEFAULT '0',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `short_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_1` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_2` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postcode` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `dealerships_account_id_index` (`account_id`),
  KEY `dealerships_accounting_contact_id_foreign` (`accounting_contact_id`),
  KEY `dealerships_billing_request_id_foreign` (`billing_request_id`),
  CONSTRAINT `dealerships_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dealerships_accounting_contact_id_foreign` FOREIGN KEY (`accounting_contact_id`) REFERENCES `accounting_contacts` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `dealerships_billing_request_id_foreign` FOREIGN KEY (`billing_request_id`) REFERENCES `billing_requests` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `document_versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `document_versions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `document_id` bigint unsigned NOT NULL,
  `version_number` int unsigned NOT NULL,
  `change_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `document_versions_document_id_foreign` (`document_id`),
  CONSTRAINT `document_versions_document_id_foreign` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `documents` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `documents_slug_unique` (`slug`),
  UNIQUE KEY `documents_title_unique` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `fault_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `fault_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `position` int unsigned DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fault_types_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `files` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned DEFAULT NULL,
  `inbound_email_id` bigint unsigned DEFAULT NULL,
  `related_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `related_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `mime_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `size` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `files_account_id_foreign` (`account_id`),
  KEY `files_related_type_related_id_index` (`related_type`,`related_id`),
  KEY `files_inbound_email_id_foreign` (`inbound_email_id`),
  CONSTRAINT `files_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `files_inbound_email_id_foreign` FOREIGN KEY (`inbound_email_id`) REFERENCES `inbound_emails` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `inbound_emails`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inbound_emails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `message_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `timestamp` timestamp NOT NULL,
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `destinations` json NOT NULL,
  `s3_bucket_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `s3_object_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `inbound_emails_message_id_unique` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `invoice_line_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_line_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `invoice_id` bigint unsigned NOT NULL,
  `sale_id` bigint unsigned NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `account_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `accounting_software_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `quantity` decimal(5,2) unsigned NOT NULL,
  `unit_amount` decimal(7,2) NOT NULL,
  `tax` decimal(7,2) NOT NULL,
  `total` decimal(7,2) GENERATED ALWAYS AS ((`quantity` * (`unit_amount` + `tax`))) STORED,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `invoice_line_items_invoice_id_foreign` (`invoice_id`),
  KEY `invoice_line_items_sale_id_foreign` (`sale_id`),
  CONSTRAINT `invoice_line_items_invoice_id_foreign` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `invoice_line_items_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoices` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `invoiceable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `invoiceable_id` bigint unsigned NOT NULL,
  `date` date NOT NULL,
  `due_date` date NOT NULL,
  `period_start` date DEFAULT NULL,
  `period_end` date DEFAULT NULL,
  `reference` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `invoice_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `accounting_software_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `accounting_software_payment_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `emailed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `invoices_account_id_foreign` (`account_id`),
  KEY `invoices_invoiceable_type_invoiceable_id_index` (`invoiceable_type`,`invoiceable_id`),
  CONSTRAINT `invoices_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint unsigned NOT NULL,
  `reserved_at` int unsigned DEFAULT NULL,
  `available_at` int unsigned NOT NULL,
  `created_at` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `manufacturer_surcharges`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `manufacturer_surcharges` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `manufacturer_id` bigint unsigned NOT NULL,
  `warranty_admin_fee_percentage` decimal(4,1) DEFAULT NULL,
  `warranty_provision_percentage` decimal(4,1) DEFAULT NULL,
  `warranty_selling_price_percentage` decimal(4,1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `manufacturer_surcharges_account_id_foreign` (`account_id`),
  KEY `manufacturer_surcharges_manufacturer_id_foreign` (`manufacturer_id`),
  CONSTRAINT `manufacturer_surcharges_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `manufacturer_surcharges_manufacturer_id_foreign` FOREIGN KEY (`manufacturer_id`) REFERENCES `manufacturers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `manufacturers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `manufacturers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `manufacturers_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_has_roles` (
  `role_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mot_test_defects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mot_test_defects` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `mot_test_id` bigint unsigned NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `dangerous` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mot_test_defects_mot_test_id_foreign` (`mot_test_id`),
  CONSTRAINT `mot_test_defects_mot_test_id_foreign` FOREIGN KEY (`mot_test_id`) REFERENCES `mot_tests` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mot_tests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mot_tests` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sale_id` bigint unsigned NOT NULL,
  `test_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `completed_at` timestamp NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `odometer_reading` int unsigned DEFAULT NULL,
  `odometer_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `data_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mot_tests_sale_id_test_number_unique` (`sale_id`,`test_number`),
  CONSTRAINT `mot_tests_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_id` bigint unsigned NOT NULL,
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `one_time_passcodes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `one_time_passcodes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `otp_authenticatable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `otp_authenticatable_id` bigint unsigned NOT NULL,
  `passcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `otp_index` (`otp_authenticatable_type`,`otp_authenticatable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `owner_changes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `owner_changes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sale_id` bigint unsigned NOT NULL,
  `start_of_ownership` date NOT NULL,
  `previous_owners` tinyint unsigned NOT NULL,
  `last_checked_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `owner_changes_unique` (`sale_id`,`start_of_ownership`),
  CONSTRAINT `owner_changes_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `pay_later_agreements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pay_later_agreements` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `pay_later_plan_id` bigint unsigned DEFAULT NULL,
  `payable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payable_id` bigint unsigned NOT NULL,
  `is_approved` tinyint(1) NOT NULL,
  `dealer_payment` decimal(7,2) unsigned DEFAULT NULL,
  `loan_amount` decimal(7,2) unsigned DEFAULT NULL,
  `commission_rate` decimal(4,2) DEFAULT NULL,
  `commission_fixed_fee` decimal(8,2) DEFAULT NULL,
  `commission_rate_margin` decimal(8,2) DEFAULT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `provider_reference` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `pay_later_agreements_pay_later_plan_id_foreign` (`pay_later_plan_id`),
  KEY `pay_later_agreements_payable_type_payable_id_index` (`payable_type`,`payable_id`),
  CONSTRAINT `pay_later_agreements_pay_later_plan_id_foreign` FOREIGN KEY (`pay_later_plan_id`) REFERENCES `pay_later_plans` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `pay_later_plans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pay_later_plans` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `provider_plan_id` int unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `instalments` int unsigned NOT NULL,
  `deposit` tinyint(1) NOT NULL,
  `apr` decimal(5,3) NOT NULL,
  `frequency` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `commission_rate` decimal(4,2) DEFAULT NULL,
  `commission_rate_margin` decimal(4,2) DEFAULT NULL,
  `min_amount` int unsigned DEFAULT NULL,
  `max_amount` int unsigned NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `pay_later_plans_account_id_provider_plan_id_unique` (`account_id`,`provider_plan_id`),
  CONSTRAINT `pay_later_plans_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `pay_later_services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pay_later_services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `customer_id` bigint unsigned NOT NULL,
  `vrm` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `private_plate` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vin` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vehicle_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vehicle_make` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `vehicle_model` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `vehicle_derivative` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `engine_capacity` int unsigned DEFAULT NULL,
  `vehicle_colour` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `fuel_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `transmission_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `registration_date` date NOT NULL,
  `invoice_amount` decimal(8,2) unsigned DEFAULT NULL,
  `dealer_payment` decimal(8,2) unsigned DEFAULT NULL,
  `dealer_amount` decimal(8,2) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `pay_later_services_account_id_foreign` (`account_id`),
  KEY `pay_later_services_customer_id_foreign` (`customer_id`),
  CONSTRAINT `pay_later_services_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`),
  CONSTRAINT `pay_later_services_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `payment_line_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_line_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` bigint unsigned NOT NULL,
  `payable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payable_id` bigint unsigned DEFAULT NULL,
  `account_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `unit_amount` decimal(7,2) unsigned NOT NULL,
  `tax` decimal(7,2) unsigned NOT NULL,
  `total` decimal(7,2) unsigned GENERATED ALWAYS AS ((`unit_amount` + `tax`)) STORED,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `payment_line_items_payment_id_foreign` (`payment_id`),
  KEY `payment_line_items_payable_type_payable_id_index` (`payable_type`,`payable_id`),
  CONSTRAINT `payment_line_items_payment_id_foreign` FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `billing_request_id` bigint unsigned DEFAULT NULL,
  `payable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payable_id` bigint unsigned DEFAULT NULL,
  `payout_id` bigint unsigned DEFAULT NULL,
  `provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `period_start` date DEFAULT NULL,
  `amount` decimal(8,2) DEFAULT NULL,
  `processor_payment_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `accounting_software_bank_transfer_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `charge_date` date DEFAULT NULL,
  `upcoming_payment_notification_sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payments_processor_payment_id_unique` (`processor_payment_id`),
  KEY `payments_billing_request_id_foreign` (`billing_request_id`),
  KEY `payments_payout_id_foreign` (`payout_id`),
  CONSTRAINT `payments_billing_request_id_foreign` FOREIGN KEY (`billing_request_id`) REFERENCES `billing_requests` (`id`),
  CONSTRAINT `payments_payout_id_foreign` FOREIGN KEY (`payout_id`) REFERENCES `payouts` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `payout_line_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payout_line_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `payout_id` bigint unsigned NOT NULL,
  `payment_id` bigint unsigned DEFAULT NULL,
  `processor_payment_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(7,2) NOT NULL,
  `tax` decimal(7,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `payout_line_items_payout_id_foreign` (`payout_id`),
  KEY `payout_line_items_payment_id_foreign` (`payment_id`),
  CONSTRAINT `payout_line_items_payment_id_foreign` FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `payout_line_items_payout_id_foreign` FOREIGN KEY (`payout_id`) REFERENCES `payouts` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `payouts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payouts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `processor_payout_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `reference` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `arrival_date` date NOT NULL,
  `amount` decimal(7,2) unsigned NOT NULL,
  `deducted_fees` decimal(7,2) unsigned NOT NULL,
  `accounting_software_transfer_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `accounting_software_from_transaction_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `accounting_software_to_transaction_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payouts_processor_payout_id_unique` (`processor_payout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `phone_call_transcripts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `phone_call_transcripts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `phone_call_id` bigint unsigned NOT NULL,
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `processing_ms` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `phone_call_transcripts_phone_call_id_unique` (`phone_call_id`),
  CONSTRAINT `phone_call_transcripts_phone_call_id_foreign` FOREIGN KEY (`phone_call_id`) REFERENCES `phone_calls` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `phone_calls`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `phone_calls` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `customer_id` bigint unsigned DEFAULT NULL,
  `dealership_id` bigint unsigned DEFAULT NULL,
  `call_log_segment_id` int unsigned NOT NULL,
  `call_log_call_id` int unsigned NOT NULL,
  `started_at` timestamp NOT NULL,
  `recording_id` int unsigned DEFAULT NULL,
  `source_type` smallint unsigned DEFAULT NULL,
  `source_dn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source_caller_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source_display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `destination_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `destination_dn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `destination_caller_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `destination_display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action_type` smallint unsigned DEFAULT NULL,
  `action_dn_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action_dn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action_dn_caller_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action_dn_display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ringing_duration` decimal(8,2) DEFAULT NULL,
  `talking_duration` decimal(8,2) DEFAULT NULL,
  `recording_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `answered` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `phone_calls_call_log_segment_id_unique` (`call_log_segment_id`),
  KEY `phone_calls_user_id_foreign` (`user_id`),
  KEY `phone_calls_customer_id_foreign` (`customer_id`),
  KEY `phone_calls_dealership_id_foreign` (`dealership_id`),
  CONSTRAINT `phone_calls_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `phone_calls_dealership_id_foreign` FOREIGN KEY (`dealership_id`) REFERENCES `dealerships` (`id`) ON DELETE SET NULL,
  CONSTRAINT `phone_calls_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_groups` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_group_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `cost_price` decimal(10,2) unsigned DEFAULT NULL,
  `selling_price` decimal(10,2) unsigned DEFAULT NULL,
  `dealer_commission` decimal(10,2) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `products_product_group_id_foreign` (`product_group_id`),
  CONSTRAINT `products_product_group_id_foreign` FOREIGN KEY (`product_group_id`) REFERENCES `product_groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `repairers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `repairers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `contact` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `website` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_1` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_2` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postcode` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vat_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_sort_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_account_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `place_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `role_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_internal` tinyint(1) NOT NULL DEFAULT '0',
  `guard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sale_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sale_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sale_id` bigint unsigned NOT NULL,
  `product_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `cost_price` decimal(10,2) unsigned DEFAULT NULL,
  `selling_price` decimal(10,2) unsigned DEFAULT NULL,
  `dealer_commission` decimal(10,2) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sale_products_sale_id_foreign` (`sale_id`),
  KEY `sale_products_product_id_foreign` (`product_id`),
  CONSTRAINT `sale_products_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `sale_products_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sales`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sales` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `dealership_id` bigint unsigned NOT NULL,
  `manufacturer_id` bigint unsigned DEFAULT NULL,
  `customer_id` bigint unsigned NOT NULL,
  `billing_request_id` bigint unsigned DEFAULT NULL,
  `sales_person_id` bigint unsigned DEFAULT NULL,
  `sold_by_id` bigint unsigned NOT NULL,
  `vrm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `private_plate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vehicle_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vehicle_make` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `vehicle_model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `vehicle_derivative` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `engine_capacity` int unsigned DEFAULT NULL,
  `vehicle_colour` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `fuel_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `transmission_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `registration_date` date NOT NULL,
  `delivery_mileage` int unsigned NOT NULL,
  `vehicle_price_paid` int unsigned NOT NULL,
  `funding_method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `start_date` date NOT NULL,
  `last_service_date` date DEFAULT NULL,
  `last_service_mileage` int unsigned DEFAULT NULL,
  `mot_last_checked_at` timestamp NULL DEFAULT NULL,
  `confirmed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sales_account_id_foreign` (`account_id`),
  KEY `sales_dealership_id_foreign` (`dealership_id`),
  KEY `sales_customer_id_foreign` (`customer_id`),
  KEY `sales_sold_by_id_foreign` (`sold_by_id`),
  KEY `sales_sales_person_id_foreign` (`sales_person_id`),
  KEY `sales_billing_request_id_foreign` (`billing_request_id`),
  KEY `sales_manufacturer_id_foreign` (`manufacturer_id`),
  CONSTRAINT `sales_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `sales_billing_request_id_foreign` FOREIGN KEY (`billing_request_id`) REFERENCES `billing_requests` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `sales_dealership_id_foreign` FOREIGN KEY (`dealership_id`) REFERENCES `dealerships` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `sales_manufacturer_id_foreign` FOREIGN KEY (`manufacturer_id`) REFERENCES `manufacturers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_sales_person_id_foreign` FOREIGN KEY (`sales_person_id`) REFERENCES `sales_people` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_sold_by_id_foreign` FOREIGN KEY (`sold_by_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sales_lead_call_outcomes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sales_lead_call_outcomes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sales_lead_id` bigint unsigned NOT NULL,
  `phone_call_id` bigint unsigned DEFAULT NULL,
  `outcome` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `callback_date` datetime DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sales_lead_call_outcomes_sales_lead_id_foreign` (`sales_lead_id`),
  KEY `sales_lead_call_outcomes_phone_call_id_foreign` (`phone_call_id`),
  CONSTRAINT `sales_lead_call_outcomes_phone_call_id_foreign` FOREIGN KEY (`phone_call_id`) REFERENCES `phone_calls` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_lead_call_outcomes_sales_lead_id_foreign` FOREIGN KEY (`sales_lead_id`) REFERENCES `sales_leads` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sales_leads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sales_leads` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `upselling_sale_id` bigint unsigned NOT NULL,
  `assigned_to_user_id` bigint unsigned DEFAULT NULL,
  `resulting_sale_id` bigint unsigned DEFAULT NULL,
  `generator_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sales_leads_upselling_sale_id_unique` (`upselling_sale_id`),
  KEY `sales_leads_assigned_to_user_id_foreign` (`assigned_to_user_id`),
  KEY `sales_leads_resulting_sale_id_foreign` (`resulting_sale_id`),
  CONSTRAINT `sales_leads_assigned_to_user_id_foreign` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `sales_leads_resulting_sale_id_foreign` FOREIGN KEY (`resulting_sale_id`) REFERENCES `sales` (`id`),
  CONSTRAINT `sales_leads_upselling_sale_id_foreign` FOREIGN KEY (`upselling_sale_id`) REFERENCES `sales` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sales_offers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sales_offers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `sales_lead_id` bigint unsigned NOT NULL,
  `warranty_product_id` bigint unsigned DEFAULT NULL,
  `breakdown_product_id` bigint unsigned DEFAULT NULL,
  `service_plan_product_id` bigint unsigned DEFAULT NULL,
  `warranty_selling_price` decimal(7,2) DEFAULT NULL,
  `breakdown_selling_price` decimal(7,2) DEFAULT NULL,
  `service_plan_selling_price` decimal(7,2) DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sales_offers_sales_lead_id_foreign` (`sales_lead_id`),
  KEY `sales_offers_warranty_product_id_foreign` (`warranty_product_id`),
  KEY `sales_offers_breakdown_product_id_foreign` (`breakdown_product_id`),
  KEY `sales_offers_service_plan_product_id_foreign` (`service_plan_product_id`),
  CONSTRAINT `sales_offers_breakdown_product_id_foreign` FOREIGN KEY (`breakdown_product_id`) REFERENCES `breakdown_products` (`id`),
  CONSTRAINT `sales_offers_sales_lead_id_foreign` FOREIGN KEY (`sales_lead_id`) REFERENCES `sales_leads` (`id`),
  CONSTRAINT `sales_offers_service_plan_product_id_foreign` FOREIGN KEY (`service_plan_product_id`) REFERENCES `service_plan_products` (`id`),
  CONSTRAINT `sales_offers_warranty_product_id_foreign` FOREIGN KEY (`warranty_product_id`) REFERENCES `warranty_products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sales_people`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sales_people` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sales_people_account_id_name_unique` (`account_id`,`name`),
  KEY `sales_people_user_id_foreign` (`user_id`),
  CONSTRAINT `sales_people_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sales_people_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `service_plan_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `service_plan_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `position` tinyint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_pure_electric` tinyint(1) NOT NULL DEFAULT '0',
  `is_recurring` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `service_plan_service_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `service_plan_service_type` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `service_plan_id` bigint unsigned NOT NULL,
  `service_type_id` bigint unsigned NOT NULL,
  `limit` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_plan_service_type_unique` (`service_plan_id`,`service_type_id`),
  KEY `service_plan_service_type_service_type_id_foreign` (`service_type_id`),
  CONSTRAINT `service_plan_service_type_service_plan_id_foreign` FOREIGN KEY (`service_plan_id`) REFERENCES `service_plans` (`id`) ON DELETE CASCADE,
  CONSTRAINT `service_plan_service_type_service_type_id_foreign` FOREIGN KEY (`service_type_id`) REFERENCES `service_types` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `service_plans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `service_plans` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `sale_id` bigint unsigned NOT NULL,
  `service_plan_product_id` bigint unsigned NOT NULL,
  `duration_years` tinyint unsigned DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `selling_price` decimal(7,2) unsigned NOT NULL,
  `admin_fee` decimal(7,2) unsigned NOT NULL,
  `vat` decimal(7,2) unsigned NOT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `service_plans_account_id_foreign` (`account_id`),
  KEY `service_plans_sale_id_foreign` (`sale_id`),
  KEY `service_plans_service_plan_product_id_foreign` (`service_plan_product_id`),
  CONSTRAINT `service_plans_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`),
  CONSTRAINT `service_plans_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE CASCADE,
  CONSTRAINT `service_plans_service_plan_product_id_foreign` FOREIGN KEY (`service_plan_product_id`) REFERENCES `service_plan_products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `service_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `service_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `position` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessions` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `support_tickets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `support_tickets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `inbound_email_id` bigint unsigned DEFAULT NULL,
  `created_by_id` bigint unsigned DEFAULT NULL,
  `assigned_to_id` bigint unsigned DEFAULT NULL,
  `customer_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `channel` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `priority` tinyint unsigned NOT NULL DEFAULT '2',
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `support_tickets_inbound_email_id_foreign` (`inbound_email_id`),
  KEY `support_tickets_created_by_id_foreign` (`created_by_id`),
  KEY `support_tickets_assigned_to_id_foreign` (`assigned_to_id`),
  KEY `support_tickets_customer_id_foreign` (`customer_id`),
  CONSTRAINT `support_tickets_assigned_to_id_foreign` FOREIGN KEY (`assigned_to_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `support_tickets_created_by_id_foreign` FOREIGN KEY (`created_by_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `support_tickets_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `support_tickets_inbound_email_id_foreign` FOREIGN KEY (`inbound_email_id`) REFERENCES `inbound_emails` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_invites`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_invites` (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `user_invites_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned DEFAULT NULL,
  `first_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_internal` tinyint(1) NOT NULL DEFAULT '0',
  `photo_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_deleted_at_unique` (`email`,`deleted_at`),
  KEY `users_account_id_index` (`account_id`),
  CONSTRAINT `users_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vehicle_component_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vehicle_component_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vehicle_components`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vehicle_components` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vehicle_component_category_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `applicable_to_ice` tinyint(1) NOT NULL DEFAULT '0',
  `applicable_to_ev` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vehicle_components_vehicle_component_category_id_foreign` (`vehicle_component_category_id`),
  CONSTRAINT `vehicle_components_vehicle_component_category_id_foreign` FOREIGN KEY (`vehicle_component_category_id`) REFERENCES `vehicle_component_categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `voip_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `voip_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `voip_users_user_id_unique` (`user_id`),
  CONSTRAINT `voip_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `warranties`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `warranties` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `sale_id` bigint unsigned NOT NULL,
  `product_id` bigint unsigned NOT NULL,
  `is_self_funded` tinyint(1) NOT NULL,
  `provision` decimal(7,2) unsigned NOT NULL,
  `selling_price` decimal(7,2) unsigned NOT NULL,
  `admin_fee` decimal(7,2) unsigned NOT NULL,
  `monthly_selling_price` decimal(7,2) unsigned DEFAULT NULL,
  `monthly_admin_fee` decimal(7,2) unsigned DEFAULT NULL,
  `monthly_provision` decimal(7,2) unsigned DEFAULT NULL,
  `vat` decimal(7,2) unsigned NOT NULL,
  `sales_vat` decimal(7,2) unsigned NOT NULL,
  `annual_mileage_limit` int unsigned DEFAULT NULL,
  `individual_claim_limit` decimal(7,2) unsigned NOT NULL,
  `total_claim_limit` decimal(7,2) unsigned NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `warranties_account_id_foreign` (`account_id`),
  KEY `warranties_product_id_foreign` (`product_id`),
  KEY `warranties_sale_id_foreign` (`sale_id`),
  CONSTRAINT `warranties_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `warranties_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `warranty_products` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `warranties_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `warranty_burn_rates_view`;
/*!50001 DROP VIEW IF EXISTS `warranty_burn_rates_view`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `warranty_burn_rates_view` AS SELECT 
 1 AS `id`,
 1 AS `account_id`,
 1 AS `sale_id`,
 1 AS `product_id`,
 1 AS `start_date`,
 1 AS `end_date`,
 1 AS `cancelled_at`,
 1 AS `is_self_funded`,
 1 AS `admin_fee`,
 1 AS `provision`,
 1 AS `selling_price`,
 1 AS `monthly_admin_fee`,
 1 AS `monthly_provision`,
 1 AS `monthly_selling_price`,
 1 AS `claims_count`,
 1 AS `claims_total_net`,
 1 AS `days_on_cover`,
 1 AS `total_days`,
 1 AS `earned_revenue`,
 1 AS `burn_rate`*/;
SET character_set_client = @saved_cs_client;
DROP TABLE IF EXISTS `warranty_product_variants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `warranty_product_variants` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` bigint unsigned NOT NULL,
  `max_engine_capacity` int unsigned DEFAULT NULL,
  `max_mileage` int unsigned DEFAULT NULL,
  `max_age` int unsigned DEFAULT NULL,
  `total_claim_limit` decimal(7,2) NOT NULL,
  `individual_claim_limit` decimal(7,2) NOT NULL,
  `provision` decimal(7,2) NOT NULL,
  `selling_price` decimal(7,2) NOT NULL,
  `admin_fee` decimal(7,2) NOT NULL,
  `monthly_selling_price` decimal(7,2) unsigned DEFAULT NULL,
  `monthly_admin_fee` decimal(7,2) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_variants_product_id_foreign` (`product_id`),
  CONSTRAINT `product_variants_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `warranty_products` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `warranty_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `warranty_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `cover_level_id` bigint unsigned NOT NULL,
  `position` smallint unsigned NOT NULL,
  `period` tinyint unsigned NOT NULL,
  `is_recurring` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `products_cover_level_id_foreign` (`cover_level_id`),
  CONSTRAINT `products_cover_level_id_foreign` FOREIGN KEY (`cover_level_id`) REFERENCES `cover_levels` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50001 DROP VIEW IF EXISTS `warranty_burn_rates_view`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `warranty_burn_rates_view` AS select `warranties`.`id` AS `id`,`warranties`.`account_id` AS `account_id`,`warranties`.`sale_id` AS `sale_id`,`warranties`.`product_id` AS `product_id`,`warranties`.`start_date` AS `start_date`,`warranties`.`end_date` AS `end_date`,`warranties`.`cancelled_at` AS `cancelled_at`,`warranties`.`is_self_funded` AS `is_self_funded`,`warranties`.`admin_fee` AS `admin_fee`,`warranties`.`provision` AS `provision`,`warranties`.`selling_price` AS `selling_price`,`warranties`.`monthly_admin_fee` AS `monthly_admin_fee`,`warranties`.`monthly_provision` AS `monthly_provision`,`warranties`.`monthly_selling_price` AS `monthly_selling_price`,(select count(0) from `claims` where (`warranties`.`id` = `claims`.`warranty_id`)) AS `claims_count`,coalesce((select sum(`claim_authorisations`.`authorised_net`) from `claim_authorisations` where exists(select 1 from `claim_estimates` where ((`claim_authorisations`.`estimate_id` = `claim_estimates`.`id`) and exists(select 1 from `claims` where ((`claim_estimates`.`claim_id` = `claims`.`id`) and (`claims`.`warranty_id` = `warranties`.`id`)))))),0) AS `claims_total_net`,greatest(0,(to_days(least(coalesce(`warranties`.`end_date`,curdate()),curdate())) - to_days(`warranties`.`start_date`))) AS `days_on_cover`,(to_days(coalesce(`warranties`.`end_date`,`warranties`.`start_date`)) - to_days(`warranties`.`start_date`)) AS `total_days`,if((`warranties`.`monthly_provision` is null),((greatest(0,(to_days(least(coalesce(`warranties`.`end_date`,curdate()),curdate())) - to_days(`warranties`.`start_date`))) / (to_days(coalesce(`warranties`.`end_date`,`warranties`.`start_date`)) - to_days(`warranties`.`start_date`))) * `warranties`.`provision`),(((greatest(0,(to_days(least(coalesce(`warranties`.`end_date`,curdate()),curdate())) - to_days(`warranties`.`start_date`))) * `warranties`.`monthly_provision`) * 12) / 365)) AS `earned_revenue`,round((coalesce((coalesce((select sum(`claim_authorisations`.`authorised_net`) from `claim_authorisations` where exists(select 1 from `claim_estimates` where ((`claim_authorisations`.`estimate_id` = `claim_estimates`.`id`) and exists(select 1 from `claims` where ((`claim_estimates`.`claim_id` = `claims`.`id`) and (`claims`.`warranty_id` = `warranties`.`id`)))))),0) / if((`warranties`.`monthly_provision` is null),((greatest(0,(to_days(least(coalesce(`warranties`.`end_date`,curdate()),curdate())) - to_days(`warranties`.`start_date`))) / (to_days(coalesce(`warranties`.`end_date`,`warranties`.`start_date`)) - to_days(`warranties`.`start_date`))) * `warranties`.`provision`),(((greatest(0,(to_days(least(coalesce(`warranties`.`end_date`,curdate()),curdate())) - to_days(`warranties`.`start_date`))) * `warranties`.`monthly_provision`) * 12) / 365))),0) * 100),0) AS `burn_rate` from `warranties` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (1,'2020_01_01_000001_create_password_resets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (2,'2020_01_01_000002_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (3,'2020_01_01_000003_create_accounts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (4,'2020_01_01_000004_create_dealerships_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (5,'2020_01_01_000005_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (6,'2021_11_05_130030_create_cover_levels_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (7,'2021_11_05_134159_create_products_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (8,'2021_11_05_135109_create_account_product_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (9,'2021_11_08_143320_create_permission_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (10,'2021_11_09_080506_create_fault_types_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (11,'2021_11_10_145257_create_warranties_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (12,'2021_11_10_153442_create_claims_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (13,'2021_11_10_153810_create_claim_estimates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (14,'2022_01_13_133542_create_claim_authorisations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (15,'2022_04_05_082756_alter_products_add_extra_price_columns',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (16,'2022_04_05_091645_alter_account_product_add_extra_price_columns',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (17,'2022_04_05_103215_alter_warranties_table_add_extra_price_columns',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (18,'2022_04_05_114756_alter_warranties_add_last_service_columns',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (19,'2022_04_05_144044_create_user_invites_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (20,'2022_04_28_183119_rename_cover_levels',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (21,'2022_05_05_092352_alter_products_setup_vehicle_based_pricing_tiers',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (22,'2022_05_05_092610_create_product_variants_table',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (23,'2022_05_05_125106_alter_account_product_table_add_limit_columns',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (24,'2022_05_26_115612_alter_claims_remove_unused_approved_at_column',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (25,'2022_05_26_115712_alter_claims_remove_unused_status_column',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (26,'2022_05_26_115812_alter_claim_estimates_remove_unused_approved_at_column',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (27,'2022_05_30_092842_create_files_table',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (28,'2022_06_20_172507_alter_accounts_table_add_breakdown_self_funded_column',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (29,'2022_06_20_172607_alter_warranties_table_add_breakdown_self_funded_column',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (30,'2022_06_20_173225_create_breakdown_products_table',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (31,'2022_06_20_173423_create_account_breakdown_product_table',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (32,'2022_06_21_102022_create_breakdown_policies_table',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (33,'2022_06_24_132841_create_breakdown_claims_table',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (34,'2022_06_28_100903_seed_breakdown_products',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (35,'2022_06_29_111004_alter_breakdown_policies_table_add_self_funded_column',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (36,'2022_06_29_111104_alter_warranties_table_remove_breakdown_self_funded_column',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (37,'2022_07_05_085133_seed_fault_types',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (38,'2022_07_05_104302_alter_claim_estimates_table_add_vat_column',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (39,'2022_07_05_124809_alter_claim_authorisations_table_add_vat_column',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (40,'2022_07_06_105309_create_repairers_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (41,'2022_07_06_134548_create_claim_rejections_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (42,'2022_07_07_083956_alter_warranties_table_add_sales_vat_column',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (43,'2022_07_07_084056_alter_breakdown_policies_table_add_sales_vat_column',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (44,'2022_07_07_084912_alter_claims_table_drop_unused_columns',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (45,'2022_07_07_085110_alter_claim_estimates_table_increase_decimal_sizes',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (46,'2022_07_07_085210_alter_claim_authorisations_table_increase_decimal_sizes',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (47,'2022_07_28_100903_seed_breakdown_products',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (48,'2022_11_16_144535_alter_breakdown_claims_add_vat_column',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (49,'2022_11_16_170310_alter_account_product_table_add_annual_mileage_limit',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (50,'2022_11_16_170712_alter_warranties_table_add_annual_mileage_limit',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (51,'2022_11_18_120442_alter_claim_authorisations_add_gross_net_amounts',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (52,'2022_11_18_123616_alter_warranties_table_remove_paid_at_column',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (53,'2023_06_17_151259_alter_users_table_make_account_id_nullable',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (54,'2019_12_14_000001_create_personal_access_tokens_table',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (55,'2023_06_21_094639_create_accounting_contacts_table',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2023_06_21_220537_alter_dealerships_add_accounting_software_id',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2023_06_21_222146_create_invoices_table',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2023_06_21_222605_create_invoice_line_items_table',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2023_06_23_155103_alter_breakdown_policies_remove_cancelled_at_column',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2023_07_03_092616_delete_blank_invoice',13);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2023_07_11_123517_create_customers_table',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2023_07_11_123525_create_sales_table',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2023_07_11_131210_drop_normalised_columns_from_warranties_table',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2023_07_11_164046_alter_breakdown_policies_add_policy_id_column',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2023_07_13_094502_rename_breakdown_policies_to_breakdown_plans',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2023_07_13_144339_alter_claim_authorisations_table_add_invoice_id_column',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2023_07_13_144836_alter_invoices_table_make_period_start_period_end_nullable',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2023_07_17_101235_alter_invoice_line_items_table_drop_warranty_id_add_sale_id',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2023_11_16_151726_alter_product_variants_table_add_monthly_columns',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2023_11_16_152626_alter_account_product_table_add_monthly_columns',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2023_11_16_160705_alter_warranties_table_add_monthly_columns',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'2023_11_17_091302_alter_invoices_table_change_dealership_id_to_morphs',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2023_11_17_102804_alter_customers_table_add_accounting_software_id_column',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (74,'2023_11_20_142024_create_customer_billing_requests_table',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2023_11_29_091130_delete_unsynced_invoice_line_items',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2023_11_29_100722_seed_products_table_with_monthly_products',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2023_12_13_133354_alter_customer_billing_requests_make_provider_customer_id_nullable',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2024_01_22_094141_rename_customer_billing_requests_table',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (79,'2024_01_22_094637_alter_billing_requests_add_sale_relation_remove_customer_relation',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (80,'2024_03_12_145422_seed_products_table_with_monthly_products',18);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (81,'2024_02_02_163712_alter_invoices_table_add_gocardless_payment_id_column',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (82,'2024_03_15_132140_create_payouts_table',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (83,'2024_03_15_133532_create_payments_table',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (84,'2024_03_16_083224_alter_payouts_table_add_accounting_software_id',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (85,'2024_03_16_094934_create_payment_line_items_table',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (86,'2024_03_18_095637_create_payout_line_items_table',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (87,'2024_04_05_134051_alter_billing_requests_add_mandate_activated_at_timestamp',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (88,'2024_05_02_125953_alter_accounts_add_send_contract_emails_boolean',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (89,'2024_05_02_132825_alter_claim_estimates_table_make_some_fields_nullable',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (90,'2024_05_02_134654_alter_sales_table_add_funding_method',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (91,'2024_05_02_140253_alter_claim_estimates_table_add_customer_contribution',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (92,'2024_05_02_140553_alter_claim_authorisations_table_add_customer_contribution',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (93,'2024_05_03_105153_alter_invoice_line_items_allow_negative_values',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (94,'2024_05_03_113944_create_comments_table',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (95,'2024_05_15_102320_alter_claims_make_current_mileage_nullable',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (96,'2024_05_15_133411_alter_claim_authorisations_add_reason_column',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (97,'2024_05_15_141532_alter_accounts_table_add_claim_handling_notes',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (98,'2024_05_15_144144_alter_products_remove_unique_constraint',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (99,'2024_05_15_144144_alter_products_table_add_is_recurring_column',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (100,'2024_05_15_144722_seed_products_table_with_new_products',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (101,'2024_05_15_160835_alter_sales_add_private_plate_column',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (102,'2024_05_23_144401_alter_invoices_table_add_reference',23);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (103,'2024_05_30_165347_alter_invoice_line_items_make_description_unlimited_length',23);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (104,'2024_06_05_143307_create_account_user_pivot_table',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (105,'2024_05_23_193954_alter_product_variants_add_foreign_key_constraint',25);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (106,'2024_06_06_103215_alter_cover_levels_add_vehicle_type_column',25);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (107,'2024_06_06_135122_seed_products_table_with_new_products',26);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (108,'2024_06_10_114138_alter_sales_make_engine_capacity_nullable',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (109,'2024_07_10_142958_alter_claim_estimates_add_invoicing_dealer_direct_boolean',28);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (110,'2024_07_10_151858_alter_claim_estimates_add_vat_charged_boolean',28);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (111,'2024_08_23_073525_cancel_andrews_monthly_warranties',29);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (112,'2024_08_23_083525_fix_missing_payments',29);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (113,'2024_08_23_085433_alter_warranties_allow_null_end_date',29);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (114,'2024_09_20_084530_alter_dealerships_add_billing_columns',30);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (115,'2024_09_20_130813_alter_payments_make_fields_nullable',30);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (116,'2024_09_20_131237_alter_payments_add_invoice_id_column',30);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (117,'2024_09_20_170039_alter_billing_requests_add_status_column',30);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (118,'2024_10_01_114052_create_jobs_table',31);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (119,'2024_10_01_124408_seed_roles_and_permissions',31);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (120,'2024_10_02_104400_create_mot_tests_table',31);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (121,'2024_10_02_104600_create_mot_test_defects_table',31);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (122,'2024_10_02_111905_alter_sales_add_mot_last_checked_at_column',31);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (123,'2024_10_02_121933_create_sales_people_table',32);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (124,'2024_10_02_124859_alter_sales_add_sales_person_id',32);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (125,'2024_10_02_141308_seed_roles_and_permissions',32);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (126,'2024_10_03_101546_alter_users_table_make_email_unique_composite',32);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (127,'2024_10_03_153116_alter_dealerships_add_account_id_foreign_key',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (128,'2024_10_03_153216_alter_repairers_add_account_id_foreign_key',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (129,'2024_10_22_000000_rename_password_resets_table',34);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (130,'2024_10_25_095945_alter_invoices_add_payment_accounting_software_id',35);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (131,'2024_10_28_085642_alter_payouts_add_from_to_bank_transaction_payment_ids',35);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (132,'2024_10_28_100635_alter_payments_add_accounting_software_bank_transfer_id',35);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (133,'2024_10_31_152331_alter_payment_line_items_add_payable_morph_columns',36);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (134,'2024_11_13_132239_seed_breakdown_products',37);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (135,'2024_11_13_155550_seed_breakdown_products',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (136,'2024_11_12_155550_seed_breakdown_products',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (137,'2024_11_13_142239_create_service_plan_products_table',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (138,'2024_11_13_143323_create_account_service_plan_product_table',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (139,'2024_11_15_112005_create_service_plans_table',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (140,'2024_11_29_133723_alter_payments_make_amount_nullable',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (141,'2024_11_29_143448_make_account_product_subscription_prices_no_vat',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (142,'2024_11_29_143450_update_monthly_warranties_add_vat',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (143,'2025_01_01_130556_enable_maintenance_mode',40);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (144,'2025_01_13_101632_alter_sales_table_add_confirmed_at_column',41);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (145,'2025_10_14_1200_seed_roles_and_permissions',42);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (146,'2025_01_16_103200_seed_roles_and_permissions',43);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (147,'2025_01_16_104703_alter_claim_estimates_set_customer_contribution_nullable',43);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (148,'2025_01_16_104803_alter_claim_authorisations_set_customer_contribution_nullable',43);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (149,'2025_01_16_161750_alter_breakdown_plans_make_sale_id_cascade',44);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (150,'2025_01_17_092527_rename_comments_table_columns',45);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (151,'2025_01_17_120000_seed_roles_and_permissions',46);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (152,'2025_01_21_164914_alter_service_plans_make_end_date_a_date',47);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (153,'2025_01_20_163253_rename_products_to_warranty_products',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (154,'2025_01_20_163353_rename_product_variants_to_warranty_product_variants',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (155,'2025_01_20_163453_rename_account_product_to_account_warranty_product',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (156,'2025_01_20_163553_rename_account_breakdown_product_to_account_breakdown_products',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (157,'2025_01_20_163653_rename_account_service_plan_product_to_account_service_plan_products',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (158,'2025_01_20_164653_create_product_groups_table',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (159,'2025_01_20_164853_create_products_table',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (160,'2025_01_20_180400_create_account_products_table',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (161,'2025_01_21_103446_create_sale_products_table',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (162,'2025_01_21_114500_seed_product_groups',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (163,'2025_01_22_083701_create_warranty_burn_rates_view',49);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (164,'2025_01_21_130825_create_documents_table',50);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (165,'2025_01_21_135430_create_document_versions_table',50);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (166,'2025_01_23_145628_seed_documents',50);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (167,'2025_01_23_150000_seed_roles_and_permissions',51);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (168,'2025_01_23_152400_seed_roles_and_permissions',52);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (169,'2025_01_25_064700_seed_roles_and_permissions',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (170,'2025_01_27_132324_create_notifications_table',54);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (171,'2025_01_28_114900_seed_roles_and_permissions',55);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (172,'2025_01_29_102600_seed_roles_and_permissions',56);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (173,'2025_01_28_164754_create_one_time_passcodes_table',57);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (174,'2025_01_29_160210_alter_claims_make_entered_by_id_nullable',57);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (175,'2025_01_29_160230_alter_claim_estimates_make_user_id_nullable',57);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (176,'2025_02_02_160200_seed_roles_and_permissions',58);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (177,'2025_02_03_194808_create_account_warranty_breakdown_product_bundles_table',59);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (178,'2025_02_04_160056_create_service_plan_redemptions_table',60);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (179,'2025_02_04_171000_seed_roles_and_permissions',60);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (180,'2025_02_05_072812_create_ai_tasks_table',61);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (181,'2025_02_05_072939_create_ai_prompts_table',61);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (182,'2025_02_05_073340_create_ai_requests_table',61);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (183,'2025_02_05_155101_create_ai_votes_table',61);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (184,'2025_02_05_172100_seed_ai_tasks',61);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (185,'2025_02_06_114528_create_ai_request_scores_table',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (186,'2025_02_07_124649_alter_cover_levels_add_document_id_column',63);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (187,'2025_02_11_124240_alter_claim_estimates_rename_booleans',64);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (188,'2025_02_13_131401_alter_dealerships_add_no_direct_debit_column',65);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (189,'2025_02_24_164859_create_cache_table',66);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (190,'2025_02_24_165001_create_sessions_table',66);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (191,'2025_02_08_160724_alter_sales_add_vehicle_type_column',67);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (192,'2025_03_24_161954_alter_fault_types_add_position_column',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (193,'2025_03_25_082614_alter_breakdown_claims_add_cause_column',69);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (194,'2025_03_26_094920_create_dealership_repairer_table',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (195,'2025_03_26_095352_alter_repairers_add_google_places_id_and_website',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (196,'2025_03_28_142549_alter_files_add_mime_type',71);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (197,'2025_03_26_141602_alter_repairers_add_contact',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (198,'2025_03_06_160225_create_vehicle_component_categories_table',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (199,'2025_03_06_160314_create_vehicle_components_table',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (200,'2025_03_07_091523_alter_cover_levels_add_ice_ev_booleans',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (201,'2025_03_07_131743_alter_account_warranty_products_fix_aggregate_unique',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (202,'2025_03_18_174004_create_cover_level_vehicle_component_table',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (203,'2025_03_24_135543_alter_claims_add_customer_email_and_customer_phone_columns',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (204,'2025_03_25_131218_create_owner_changes_table',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (205,'2025_03_26_143941_alter_claim_estimates_add_repairer_id',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (206,'2025_03_26_144109_create_claim_estimate_line_items_table',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (207,'2025_03_27_102247_alter_invoice_line_items_change_quantity_to_decimal',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (208,'2025_03_27_193930_alter_repairers_add_company_and_banking_columns',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (209,'2025_03_28_165156_alter_files_make_account_id_and_morph_columns_nullable',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (210,'2025_03_31_100645_alter_claims_add_terms_accepted_at_timestamp',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (211,'2025_03_31_115305_create_inbound_emails_table',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (212,'2025_04_01_133129_alter_claim_estimates_change_is_charged_internally_default_to_false',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (213,'2025_04_02_073206_alter_claim_estimates_add_estimate_date_and_repair_date_columns',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (214,'2025_04_04_114326_alter_claim_estimates_add_columns',74);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (215,'2025_04_10_110833_alter_claim_estimate_line_items_make_description_text_column',75);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (216,'2025_04_10_114930_alter_mot_tests_make_mileage_nullable',76);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (217,'2025_04_13_134743_create_service_types_table',77);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (218,'2025_04_13_145744_create_account_service_plan_product_service_type',77);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (219,'2025_04_14_100253_create_service_plan_service_type',77);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (220,'2025_04_14_154912_drop_service_plan_redemptions',77);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (221,'2025_04_14_154930_create_authorised_services',77);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (222,'2025_04_16_122702_alter_files_add_inbound_email_id',78);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (223,'2025_04_17_092930_alter_claim_estimates_change_estimate_completed_date_to_date_time_and_rename',79);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (224,'2025_04_23_081011_add_short_name_column_to_accounts_table',80);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (225,'2025_04_23_090316_add_short_name_column_to_dealerships_table',80);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (226,'2025_04_24_091457_alter_claim_estimate_line_items_add_net_column',81);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (227,'2025_04_23_123156_add_is_internal_column_to_roles_table',82);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (228,'2025_04_23_123157_add_is_internal_column_to_users_table',82);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (229,'2025_04_07_190443_alter_billing_requests_setup_access_paysuite',83);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (230,'2025_04_07_193424_alter_payments_add_provider',83);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (231,'2025_04_07_194011_alter_invoices_remove_gocardless_payment_id',83);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (232,'2025_04_09_092401_alter_payments_add_upcoming_payment_notification_sent_at_column',83);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (233,'2025_04_30_063113_alter_billing_requests_add_bank_details_fields',84);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (234,'2025_04_30_063200_alter_billing_requests_drop_completed_at',84);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (235,'2025_05_06_094924_alter_claim_estimates_remove_money_columns',85);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (236,'2025_05_06_095124_alter_claim_authorisations_remove_money_columns',85);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (237,'2025_04_29_131844_create_voip_users_table',86);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (238,'2025_04_30_145805_create_phone_calls_table',86);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (239,'2025_05_09_101458_alter_payments_remove_amount_refunded_column',87);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (240,'2025_05_09_110031_alter_payments_set_billing_request_id_constraint_nullable',87);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (241,'2025_05_13_065015_alter_sales_add_billing_request_id_column',88);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (242,'2025_05_13_103054_alter_billing_requests_drop_sale_id_column',88);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (243,'2025_05_15_092618_alter_claim_estimates_make_repairer_id_null_on_delete',89);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (244,'2025_06_14_124136_alter_payments_add_payable_morphs',90);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (245,'2025_06_16_085801_alter_billing_requests_make_columns_unique',91);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (246,'2025_05_07_103002_create_sales_leads_table',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (247,'2025_05_08_092308_create_manufacturers_table',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (248,'2025_05_08_092746_alter_sales_add_manufacturer_id_column',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (249,'2025_05_09_152315_create_phone_call_transcripts_table',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (250,'2025_05_14_095242_create_sales_offers_table',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (251,'2025_05_29_081503_create_pay_later_plans_table',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (252,'2025_05_29_081844_create_pay_later_agreements_table',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (253,'2025_05_29_081845_alter_pay_later_agreements_add_commission_margin',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (254,'2025_05_29_082234_create_account_pay_later_plan_pivot_table',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (255,'2025_05_29_082235_alter_account_pay_later_plan_add_commission_rate_margin',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (256,'2025_06_09_105517_alter_pay_later_agreements_add_description_column',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (257,'2025_06_09_132132_create_sales_lead_call_outcomes_table',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (259,'2025_06_10_130703_alter_sales_offers_add_sent_at_timestamp_column',93);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (260,'2025_06_13_081844_create_pay_later_agreements_table',94);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (261,'2025_06_13_085700_create_pay_later_services_table',94);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (262,'2025_06_12_153023_create_account_service_credentials_table',95);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (264,'2025_06_19_103000_create_pay_later_plans_table',96);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (265,'2025_06_19_123700_create_pay_later_agreements_table',97);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (266,'2025_06_19_130200_create_pay_later_services_table',98);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (267,'2025_06_20_140621_create_support_tickets_table',99);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (268,'2025_06_25_083507_alter_pay_later_agreements_rename_deposit_amount_to_dealer_payment',100);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (269,'2025_06_25_083607_alter_pay_later_services_rename_deposit_amount_to_dealer_payment',100);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (270,'2025_06_26_090919_create_manufacturer_surcharges_table',101);
