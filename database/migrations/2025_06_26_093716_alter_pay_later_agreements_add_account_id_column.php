<?php

use App\Models\Account;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pay_later_agreements', function (Blueprint $table) {
            $table->foreignIdFor(Account::class)->after('id')->default(1)->constrained();
        });
        Schema::table('pay_later_agreements', function (Blueprint $table) {
            $table->foreignIdFor(Account::class)->after('id')->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pay_later_agreements', function (Blueprint $table) {
            $table->dropForeignIdFor(Account::class);
            $table->dropColumn('account_id');
        });
    }
};
