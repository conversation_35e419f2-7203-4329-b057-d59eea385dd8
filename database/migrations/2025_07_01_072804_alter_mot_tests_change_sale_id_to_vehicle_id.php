<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mot_tests', function (Blueprint $table) {
            $table->dropForeign(['sale_id']);
            $table->dropUnique(['sale_id', 'test_number']);
            $table->renameColumn('sale_id', 'vehicle_id');
            $table->foreign('vehicle_id')->references('id')->on('vehicles');
            $table->unique(['vehicle_id', 'test_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mot_tests', function (Blueprint $table) {
            $table->dropForeign(['vehicle_id']);
            $table->dropUnique(['vehicle_id', 'test_number']);
            $table->renameColumn('vehicle_id', 'sale_id');
            $table->foreign('sale_id')->references('id')->on('sales');
            $table->unique(['sale_id', 'test_number']);
        });
    }
};
