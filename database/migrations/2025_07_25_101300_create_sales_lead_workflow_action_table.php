<?php

use App\Models\Workflow;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_lead_workflow_action', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\SalesLead::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(\App\Models\WorkflowAction::class)->constrained()->cascadeOnDelete();
            $table->timestamp('executed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_lead_workflow_action');
    }
};
