<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offers', function (Blueprint $table) {
            $table->id();
            $table->string('name', 200)->comment('The admin name of the offer');
            $table->string('subject', 200)->comment('The email subject line');
            $table->string('button_text', 50)->comment('The text on the button in the email');
            $table->text('body')->comment('The email body');
            $table->foreignIdFor(\App\Models\WarrantyProduct::class)->nullable()->constrained();
            $table->foreignIdFor(\App\Models\BreakdownProduct::class)->nullable()->constrained();
            $table->foreignIdFor(\App\Models\ServicePlanProduct::class)->nullable()->constrained();
            $table->decimal('warranty_selling_price', 7, 2)->nullable();
            $table->decimal('breakdown_selling_price', 7, 2)->nullable();
            $table->decimal('service_plan_selling_price', 7, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offers');
    }
};
