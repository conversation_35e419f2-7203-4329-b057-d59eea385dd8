<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('breakdown_plans', function (Blueprint $table) {
            $table->unsignedTinyInteger('fund_type')->after('is_self_funded');
        });
        \Illuminate\Support\Facades\DB::statement('UPDATE breakdown_plans SET fund_type = is_self_funded + 1');
        Schema::table('breakdown_plans', function (Blueprint $table) {
            $table->dropColumn('is_self_funded');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('breakdown_plans', function (Blueprint $table) {
            $table->boolean('is_self_funded')->after('fund_type');
        });
        \Illuminate\Support\Facades\DB::statement('UPDATE breakdown_plans SET is_self_funded = fund_type - 1');
        Schema::table('breakdown_plans', function (Blueprint $table) {
            $table->dropColumn('fund_type');
        });
    }
};
