<?php

use App\Models\Account;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Account::class)->constrained();
            $table->foreignIdFor(\OvonGroup\ManufacturerLogos\Models\Manufacturer::class)->nullable()->constrained();
            $table->string('vrm', 8);
            $table->string('private_plate', 8)->nullable();
            $table->string('vin')->nullable();
            $table->string('make')->nullable();
            $table->string('model')->nullable();
            $table->string('derivative')->nullable();
            $table->string('body_type')->nullable();
            $table->string('fuel_type')->nullable();
            $table->string('transmission_type')->nullable();
            $table->string('colour')->nullable();
            $table->date('registration_date')->nullable();
            $table->unsignedInteger('engine_capacity')->nullable();
            $table->timestamp('mot_last_checked_at')->nullable();
            $table->timestamp('vehicle_lookup_last_checked_at')->nullable();
            $table->timestamps();
        });

        \Illuminate\Support\Facades\DB::statement(
            'INSERT INTO vehicles (
                id, account_id, manufacturer_id, vrm, private_plate, vin, make, model, derivative, fuel_type, transmission_type, body_type, colour, registration_date, engine_capacity, mot_last_checked_at, vehicle_lookup_last_checked_at, created_at, updated_at
            )
            SELECT
                id, account_id, manufacturer_id, vrm, private_plate, vin, vehicle_make, vehicle_model, vehicle_derivative, fuel_type, transmission_type, vehicle_type, vehicle_colour, registration_date, engine_capacity, mot_last_checked_at, vehicle_lookup_last_checked_at, created_at, updated_at
            FROM sales
            '
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
