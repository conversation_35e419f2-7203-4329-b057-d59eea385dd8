<?php

use App\Models\SalesLead;
use App\Models\Workflow;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_lead_workflow', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(SalesLead::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Workflow::class)->constrained()->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_lead_workflow');
    }
};
