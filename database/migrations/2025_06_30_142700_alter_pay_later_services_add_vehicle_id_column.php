<?php

use App\Models\Vehicle;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pay_later_services', function (Blueprint $table) {
            $table->foreignIdFor(Vehicle::class)->after('account_id')->nullable()->constrained();
        });

        $maxVehicleId = Vehicle::max('id');

        \Illuminate\Support\Facades\DB::statement(
            'INSERT INTO vehicles (
                account_id, vrm, private_plate, vin, make, model, derivative, fuel_type, transmission_type, body_type, colour, registration_date, engine_capacity, created_at, updated_at
            )
            SELECT
                account_id, vrm, private_plate, vin, vehicle_make, vehicle_model, vehicle_derivative, fuel_type, transmission_type, vehicle_type, vehicle_colour, registration_date, engine_capacity, created_at, updated_at
            FROM pay_later_services
            ');

        \Illuminate\Support\Facades\DB::statement('UPDATE pay_later_services SET vehicle_id = id + ?', [$maxVehicleId]);
    }
};
