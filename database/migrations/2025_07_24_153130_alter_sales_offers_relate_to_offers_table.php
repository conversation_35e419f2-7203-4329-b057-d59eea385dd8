<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        \App\Models\SalesOffer::truncate();
        Schema::table('sales_offers', function (Blueprint $table) {
            $table->foreignIdFor(\App\Models\Offer::class )->after('id')->constrained();
            $table->dropConstrainedForeignId('warranty_product_id');
            $table->dropConstrainedForeignId('breakdown_product_id');
            $table->dropConstrainedForeignId('service_plan_product_id');
            $table->dropColumn('warranty_selling_price');
            $table->dropColumn('breakdown_selling_price');
            $table->dropColumn('service_plan_selling_price');
        });
    }
};
