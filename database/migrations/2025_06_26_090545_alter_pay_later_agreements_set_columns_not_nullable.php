<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pay_later_agreements', function (Blueprint $table) {
            $table->decimal('commission_rate', 4, 2)->nullable()->change();
            $table->decimal('commission_rate_margin', 4, 2)->nullable()->change();

            $table->dropColumn('commission_fixed_fee');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pay_later_agreements', function (Blueprint $table) {
            $table->decimal('commission_fixed_fee', 8, 2)->nullable()->after('commission_rate_margin');
        });
    }
};
