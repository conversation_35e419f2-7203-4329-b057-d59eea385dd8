<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('UPDATE pay_later_plans SET min_amount = 0 WHERE min_amount IS NULL');

        Schema::table('pay_later_plans', function (Blueprint $table) {
            $table->unsignedInteger('min_amount')->nullable(false)->default(0)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pay_later_plans', function (Blueprint $table) {
            $table->unsignedInteger('min_amount')->nullable()->change();
        });
    }
};
