<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->dropForeign(['manufacturer_id']);
            $table->dropColumn([
                'manufacturer_id',
                'vrm',
                'private_plate',
                'vin',
                'vehicle_make',
                'vehicle_model',
                'vehicle_derivative',
                'vehicle_type',
                'engine_capacity',
                'vehicle_colour',
                'fuel_type',
                'transmission_type',
                'registration_date',
                'mot_last_checked_at',
                'vehicle_lookup_last_checked_at',
            ]);
        });
    }
};
