<?php

use App\Models\Vehicle;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->foreignIdFor(Vehicle::class)->after('account_id')->nullable()->constrained();
        });

        Illuminate\Support\Facades\DB::statement('UPDATE sales SET vehicle_id = id WHERE vehicle_id IS NULL');

        Schema::table('sales', function (Blueprint $table) {
            $table->foreignIdFor(Vehicle::class)->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->dropForeign(['vehicle_id']);
            $table->dropColumn('vehicle_id');
        });
    }
};
