<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('owner_changes', function (Blueprint $table) {
            $table->dropForeign(['sale_id']);
            $table->dropUnique('owner_changes_unique');
            $table->renameColumn('sale_id', 'vehicle_id');
            $table->foreign('vehicle_id')->references('id')->on('vehicles');
            $table->unique(['vehicle_id', 'start_of_ownership']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('owner_changes', function (Blueprint $table) {
            $table->dropForeign(['vehicle_id']);
            $table->dropUnique(['vehicle_id', 'start_of_ownership']);
            $table->renameColumn('vehicle_id', 'sale_id');
            $table->foreign('sale_id')->references('id')->on('sales');
            $table->unique(['sale_id', 'start_of_ownership']);
        });
    }
};
