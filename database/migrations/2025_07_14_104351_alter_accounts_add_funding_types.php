<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->unsignedTinyInteger('warranty_fund_type')->after('warranty_self_funded');
            $table->unsignedTinyInteger('breakdown_fund_type')->after('breakdown_self_funded');
        });
        \Illuminate\Support\Facades\DB::statement('UPDATE accounts SET warranty_fund_type = warranty_self_funded + 1, breakdown_fund_type = breakdown_self_funded + 1');
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropColumn('warranty_self_funded');
            $table->dropColumn('breakdown_self_funded');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->boolean('warranty_self_funded')->after('warranty_fund_type');
            $table->boolean('breakdown_self_funded')->after('breakdown_fund_type');
        });
        \Illuminate\Support\Facades\DB::statement('UPDATE accounts SET warranty_self_funded = warranty_fund_type - 1, breakdown_self_funded = breakdown_fund_type - 1');
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropColumn('warranty_fund_type');
            $table->dropColumn('breakdown_fund_type');
        });
    }
};
