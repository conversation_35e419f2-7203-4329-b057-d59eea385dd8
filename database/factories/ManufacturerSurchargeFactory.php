<?php

namespace Database\Factories;

use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;
use OvonGroup\ManufacturerLogos\Models\Manufacturer;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ManufacturerSurcharge>
 */
class ManufacturerSurchargeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'manufacturer_id' => Manufacturer::factory(),
            'warranty_admin_fee_percentage' => 100,
            'warranty_provision_percentage' => 75,
            'warranty_selling_price_percentage' => 50,
        ];
    }
}
