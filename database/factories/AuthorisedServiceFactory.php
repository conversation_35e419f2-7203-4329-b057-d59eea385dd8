<?php

namespace Database\Factories;

use App\Enums\AuthorisedServiceStatus;
use App\Models\AuthorisedService;
use App\Models\ServicePlan;
use App\Models\ServiceType;
use Illuminate\Database\Eloquent\Factories\Factory;

class AuthorisedServiceFactory extends Factory
{
    protected $model = AuthorisedService::class;

    public function definition(): array
    {
        return [
            'service_plan_id' => ServicePlan::factory(),

            'service_type_id' => ServiceType::factory(),
            'status' => AuthorisedServiceStatus::BOOKED,
            'authorisation_code' => '123456',
        ];
    }
}
