<?php

namespace Database\Factories;

use App\Enums\PaymentProvider;
use App\Models\BillingRequest;
use App\Models\Payment;
use App\Models\Sale;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaymentFactory extends Factory
{
    protected $model = Payment::class;

    public function definition(): array
    {
        return [
            'provider' => PaymentProvider::ACCESS_PAYSUITE,
            'period_start' => now(),
            'amount' => $this->faker->numberBetween(1_000, 10_000) / 100,
            'processor_payment_id' => $this->faker->uuid(),
            'accounting_software_bank_transfer_id' => $this->faker->uuid(),
            'status' => Payment::STATUS_PENDING_SUBMISSION,
            'charge_date' => null,

            'billing_request_id' => BillingRequest::factory(),
        ];
    }

    public function goCardless(): Factory
    {
        return $this->state([
            'provider' => PaymentProvider::GO_CARDLESS,
        ]);
    }

    public function customer(): Factory
    {
        return $this->for(Sale::factory(), 'payable');
    }
}
