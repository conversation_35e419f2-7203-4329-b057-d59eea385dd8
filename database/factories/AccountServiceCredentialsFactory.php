<?php

namespace Database\Factories;

use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AccountServiceCredentials>
 */
class AccountServiceCredentialsFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'is_live' => false,
            'credentials' => [
                'api_key' => 'payment-assist-api-key',
                'api_secret' => 'payment-assist-api-secret',
            ],
        ];
    }

    public function paymentAssist()
    {
        return $this->state([
            'provider' => 'payment_assist',
        ]);
    }
}
