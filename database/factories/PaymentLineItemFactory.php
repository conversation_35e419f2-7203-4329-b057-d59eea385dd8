<?php

namespace Database\Factories;

use App\Models\Payment;
use App\Models\PaymentLineItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaymentLineItemFactory extends Factory
{
    protected $model = PaymentLineItem::class;

    public function definition(): array
    {
        return [
            'payable_type' => $this->faker->word(),
            'payable_id' => $this->faker->randomNumber(),
            'account_code' => $this->faker->word(),
            'description' => $this->faker->text(),
            'unit_amount' => $this->faker->randomFloat(),
            'tax' => $this->faker->randomFloat(),
            'total' => $this->faker->randomFloat(),

            'payment_id' => Payment::factory(),
        ];
    }
}
