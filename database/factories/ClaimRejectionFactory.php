<?php

namespace Database\Factories;

use App\Models\Claim;
use App\Models\ClaimRejection;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClaimRejectionFactory extends Factory
{
    protected $model = ClaimRejection::class;

    public function definition(): array
    {
        return [
            'reason' => $this->faker->word(),
            'notes' => $this->faker->word(),

            'claim_id' => Claim::factory(),
            'user_id' => User::factory(),
        ];
    }
}
