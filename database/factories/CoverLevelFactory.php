<?php

namespace Database\Factories;

use App\Enums\VehicleType;
use App\Models\Document;
use Illuminate\Database\Eloquent\Factories\Factory;

class CoverLevelFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement(['Silver', 'Gold', 'Platinum']),
            'document_id' => Document::factory(),
            'vehicle_type' => VehicleType::CAR->value,
            'is_ice' => true,
            'is_ev' => false,
        ];
    }
}
