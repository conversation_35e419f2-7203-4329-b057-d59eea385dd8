<?php

namespace Database\Factories;

use App\Models\Account;
use App\Models\BreakdownProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

class AccountBreakdownProductFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'breakdown_product_id' => BreakdownProduct::factory(),
            'provision' => 12,
            'admin_fee' => 22,
            'selling_price' => 109,
        ];
    }
}
