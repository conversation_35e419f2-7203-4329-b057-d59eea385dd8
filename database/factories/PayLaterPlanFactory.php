<?php

namespace Database\Factories;

use App\Models\Account;
use App\Models\PayLaterPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PayLaterPlan>
 */
class PayLaterPlanFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),

            'provider_plan_id' => fake()->unique()->numberBetween(1, 1000),
            'name' => '9 Month Interest Free',
            'instalments' => 9,
            'deposit' => true,
            'apr' => 0,
            'frequency' => 'monthly',
            'commission_rate' => 8.50,
            'commission_rate_margin' => 1.05,
            'min_amount' => 100,
            'max_amount' => 1000,
        ];
    }

    /**
     * Configure the model factory.
     */
    public function configure(): static
    {
        return $this->afterMaking(function (PayLaterPlan $plan) {
            // Set deposit to true for short-term plans (3, 6, 9 instalments)
            if (in_array($plan->instalments, [3, 6, 9])) {
                $plan->deposit = true;
                $plan->apr = '0.000';
            }
        });
    }

    public function interestBearing()
    {
        return $this->state([
            'deposit' => false,
            'apr' => 10.9,
        ]);
    }
}
