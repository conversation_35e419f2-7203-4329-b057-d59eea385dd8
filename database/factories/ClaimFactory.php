<?php

namespace Database\Factories;

use App\Models\ClaimEstimate;
use App\Models\FaultType;
use App\Models\User;
use App\Models\Warranty;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClaimFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'warranty_id' => Warranty::factory(),
            'fault_type_id' => FaultType::factory(),
            'entered_by_id' => User::factory(),
            'reference' => fn ($attributes) => $attributes['warranty_id'].'-'.$this->faker->numberBetween(1, 5),
            'failure_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'current_mileage' => 50000,
            'vehicle_location' => $this->faker->address,
            'fault_description' => $this->faker->sentence,
        ];
    }

    public function authorised(): self
    {
        return $this->has(ClaimEstimate::factory()->hasAuthorisation(), 'estimates');
    }
}
