<?php

namespace Database\Factories;

use App\Models\Account;
use App\Models\Sale;
use App\Models\ServicePlanProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ServicePlan>
 */
class ServicePlanFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'sale_id' => Sale::factory(),
            'service_plan_product_id' => ServicePlanProduct::factory(),
            'duration_years' => 7,
            'end_date' => now()->addYears(7)->subDay(),
            'admin_fee' => 0,
            'selling_price' => 0,
            'vat' => 0,
        ];
    }
}
