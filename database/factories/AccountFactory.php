<?php

namespace Database\Factories;

use App\Enums\FundType;
use App\Models\AccountServiceCredentials;
use App\Models\PayLaterPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

class AccountFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company(),
            'authorisation_contact' => fake()->name(),
            'warranty_fund_type' => FundType::MANAGED,
            'breakdown_fund_type' => FundType::MANAGED,
            'send_contract_emails' => true,
            'max_hourly_labour_rate' => 50,
        ];
    }

    public function notSendingContractEmails()
    {
        return $this->state([
            'send_contract_emails' => false,
        ]);
    }

    public function dealerFunded()
    {
        return $this->state([
            'warranty_fund_type' => FundType::DEALER,
            'breakdown_fund_type' => FundType::DEALER,
        ]);
    }

    public function managedFund()
    {
        return $this->state([
            'warranty_fund_type' => FundType::MANAGED,
            'breakdown_fund_type' => FundType::MANAGED,
        ]);
    }

    public function withPayLater()
    {
        return $this
            ->has(AccountServiceCredentials::factory()->paymentAssist(), 'serviceCredentials')
            ->has(PayLaterPlan::factory()->state([
                'commission_rate_margin' => 1.23,
                'deposit' => true,
            ]));
    }
}
