<?php

namespace Database\Factories;

use App\Enums\PaymentProvider;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BillingRequest>
 */
class BillingRequestFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'provider' => PaymentProvider::ACCESS_PAYSUITE,
            'provider_billing_request_id' => fake()->unique()->uuid(),
            'provider_customer_id' => fake()->unique()->uuid(),
            'mandate_id' => fake()->unique()->uuid(),
            'status' => 'active',
            'mandate_activated_at' => now(),
        ];
    }

    public function goCardless(): Factory
    {
        return $this->state([
            'provider' => PaymentProvider::GO_CARDLESS,
        ]);
    }

    public function accessPaysuite(): Factory
    {
        return $this->state([
            'provider' => PaymentProvider::ACCESS_PAYSUITE,
        ]);
    }

    public function pending(): Factory
    {
        return $this->state([
            'mandate_activated_at' => null,
            'status' => 'pending',
        ]);
    }

    public function completed(): Factory
    {
        return $this->state([
            'mandate_activated_at' => now(),
            'status' => 'active',
        ]);
    }
}
