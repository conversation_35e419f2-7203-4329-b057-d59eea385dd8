<?php

namespace Database\Factories;

use App\Enums\FundType;
use App\Models\Account;
use App\Models\AccountWarrantyProduct;
use App\Models\Sale;
use Illuminate\Database\Eloquent\Factories\Factory;

class WarrantyFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'sale_id' => Sale::factory(),
            'product_id' => AccountWarrantyProduct::factory(),
            'fund_type' => FundType::MANAGED,
            'provision' => 100,
            'admin_fee' => 20,
            'selling_price' => 199,
            'vat' => 0,
            'sales_vat' => 0,
            'individual_claim_limit' => 1000,
            'total_claim_limit' => 10000,
            'annual_mileage_limit' => 12000,
            'end_date' => now()->addYear()->subDay(),
        ];
    }

    public function subscription()
    {
        return $this->state([
            'selling_price' => 0,
            'monthly_selling_price' => 35.99,
            'monthly_admin_fee' => 2,
        ]);
    }
}
