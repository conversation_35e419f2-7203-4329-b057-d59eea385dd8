<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class RepairerFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company(),
            'email' => fake()->companyEmail(),
            'phone' => fake()->phoneNumber(),
            'address_1' => fake()->streetAddress(),
            'address_2' => fake()->secondaryAddress(),
            'city' => fake()->city(),
            'county' => fake()->city(),
            'country' => 'UK',
            'postcode' => strtoupper(fake()->bothify('??## ???')),
        ];
    }
}
