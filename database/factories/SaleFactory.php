<?php

namespace Database\Factories;

use App\Models\Account;
use App\Models\BillingRequest;
use App\Models\Customer;
use App\Models\Dealership;
use App\Models\PayLaterAgreement;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\Warranty;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Sale>
 */
class SaleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'vehicle_id' => Vehicle::factory(),
            'dealership_id' => Dealership::factory(),
            'customer_id' => Customer::factory(),
            'sold_by_id' => User::factory(),
            'delivery_mileage' => 40000,
            'vehicle_price_paid' => 20000,
            'funding_method' => 'Cash',
            'start_date' => now(),
            'last_service_date' => now()->subMonths(6),
            'last_service_mileage' => 38000,
        ];
    }

    public function confirmed(?Account $account = null): Factory
    {
        return $this
            ->recycle($account ?: Account::factory()->create())
            ->state([
                'confirmed_at' => now(),
            ]);
    }

    public function confirmedWithProducts()
    {
        return $this->confirmed()
            ->hasWarranty()
            ->hasBreakdownPlan()
            ->hasServicePlan();
    }

    public function subscription()
    {
        return $this->confirmed()
            ->state(['billing_request_id' => BillingRequest::factory()->completed()])
            ->has(Warranty::factory()->subscription());
    }

    public function pending(): Factory
    {
        return $this->state([
            'start_date' => now()->addDay(),
        ]);
    }

    public function preApprovedForPayLater(bool $approved = true)
    {
        return $this
            ->has(PayLaterAgreement::factory()->approved($approved));
    }

    public function withPayLaterAgreementSelected()
    {
        return $this
            ->has(PayLaterAgreement::factory()->approvedAndPlanSelected());
    }

    public function withPayLaterAgreementCompleted()
    {
        return $this
            ->has(PayLaterAgreement::factory()->completed());
    }

    public function withDirectDebitMandatePending()
    {
        return $this->for(BillingRequest::factory()->pending());
    }

    public function withDirectDebitMandateCompleted()
    {
        return $this->for(BillingRequest::factory()->completed());
    }
}
