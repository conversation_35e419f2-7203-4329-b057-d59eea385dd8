<?php

namespace Database\Factories;

use App\Models\Account;
use App\Models\Dealership;
use App\Models\Invoice;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class InvoiceFactory extends Factory
{
    protected $model = Invoice::class;

    public function definition(): array
    {
        return [
            'invoiceable_type' => 'dealership',
            'invoiceable_id' => Dealership::factory(),
            'date' => Carbon::now(),
            'due_date' => Carbon::now()->addDay(),
            'period_start' => Carbon::now()->startOfMonth(),
            'period_end' => Carbon::now()->endOfMonth(),
            'status' => Invoice::STATUS_PENDING,
            'description' => $this->faker->text(),
            'invoice_number' => 'INV-'.$this->faker->unique()->numberBetween(1000, 9999),

            'account_id' => Account::factory(),
        ];
    }
}
