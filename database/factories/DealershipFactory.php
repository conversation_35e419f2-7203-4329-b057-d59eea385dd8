<?php

namespace Database\Factories;

use App\Models\Account;
use App\Models\AccountingContact;
use App\Models\BillingRequest;
use App\Models\Dealership;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

class DealershipFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'accounting_contact_id' => AccountingContact::factory(),
            'billing_request_id' => BillingRequest::factory(),
            'contact_first_name' => fake()->firstName(),
            'contact_last_name' => fake()->lastName(),
            'name' => fake()->company(),
            'email' => fake()->companyEmail(),
            'phone' => fake()->phoneNumber(),
            'address_1' => fake()->streetAddress(),
            'address_2' => fake()->city(),
            'city' => fake()->city(),
            'county' => fake()->country(),
            'country' => 'US',
            'postcode' => fake()->postcode(),
        ];
    }

    public function create($attributes = [], ?Model $parent = null)
    {
        return Dealership::withoutEvents(fn () => parent::create($attributes, $parent));
    }
}
