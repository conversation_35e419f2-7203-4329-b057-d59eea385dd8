<?php

namespace Database\Factories;

use App\Models\Account;
use App\Models\AccountServicePlanProduct;
use App\Models\ServicePlanProduct;
use App\Models\ServiceType;
use Illuminate\Database\Eloquent\Factories\Factory;

class AccountServicePlanProductFactory extends Factory
{
    protected $model = AccountServicePlanProduct::class;

    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'service_plan_product_id' => ServicePlanProduct::factory(),
            'admin_fee' => 99,
            'selling_price' => 299,
        ];
    }

    public function withAllServiceTypes()
    {
        return $this->hasAttached(
            ServiceType::factory()->times(3)->sequence(
                ['name' => 'Major Service'],
                ['name' => 'Minor Service'],
                ['name' => 'MOT'],
            ),
            function () {
                static $limit = 0;
                $limit++;

                return ['limit' => $limit];
            }
        );

    }
}
