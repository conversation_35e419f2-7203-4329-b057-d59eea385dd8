<?php

namespace Database\Factories;

use App\Enums\VehicleType;
use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Vehicle>
 */
class VehicleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'manufacturer_id' => \OvonGroup\ManufacturerLogos\Models\Manufacturer::factory(),

            'vrm' => strtoupper($this->faker->bothify('??## ???')),
            'vin' => strtoupper($this->faker->unique()->bothify('?????????????????')),
            'make' => 'Audi',
            'model' => 'A3',
            'derivative' => 'Sportback',
            'engine_capacity' => 2000,
            'colour' => 'Black',
            'body_type' => VehicleType::CAR,
            'fuel_type' => 'Petrol',
            'transmission_type' => 'Manual',
            'registration_date' => $this->faker->dateTimeBetween('-6 years', '-1 year'),
        ];
    }
}
