<?php

namespace Database\Factories;

use App\Models\Claim;
use App\Models\ClaimEstimate;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClaimEstimateFactory extends Factory
{
    protected $model = ClaimEstimate::class;

    public function definition(): array
    {
        return [
            'workshop_name' => $this->faker->company(),
            'workshop_contact' => $this->faker->word(),
            'workshop_phone' => $this->faker->phoneNumber(),
            'workshop_email' => $this->faker->unique()->safeEmail(),
            'workshop_address' => $this->faker->address(),
            'work_required' => $this->faker->word(),
            'is_charged_internally' => false,
            'is_invoicing_dealer_direct' => false,

            'claim_id' => Claim::factory(),
            'user_id' => User::factory(),
        ];
    }

    public function internal(): self
    {
        return $this->state([
            'is_charged_internally' => true,
            'is_invoicing_dealer_direct' => false,
        ]);
    }

    public function directInvoice(): self
    {
        return $this->state([
            'is_charged_internally' => false,
            'is_invoicing_dealer_direct' => true,
        ]);
    }
}
