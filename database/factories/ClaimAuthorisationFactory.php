<?php

namespace Database\Factories;

use App\Models\ClaimAuthorisation;
use App\Models\ClaimEstimate;
use App\Models\Invoice;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClaimAuthorisationFactory extends Factory
{
    protected $model = ClaimAuthorisation::class;

    public function definition(): array
    {
        return [
            'work_done' => $this->faker->word(),
            'reason' => $this->faker->word(),

            'estimate_id' => ClaimEstimate::factory(),
            'user_id' => User::factory(),
        ];
    }

    public function invoiced()
    {
        return $this->state([
            'invoice_id' => Invoice::factory(),
        ]);
    }

    public function invoicedAndSettled()
    {
        return $this->state([
            'invoice_id' => Invoice::factory()->state([
                'status' => Invoice::STATUS_PAID,
            ])->hasPayment(),
        ]);
    }
}
