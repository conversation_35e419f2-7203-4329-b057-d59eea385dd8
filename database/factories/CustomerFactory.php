<?php

namespace Database\Factories;

use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'email' => fake()->safeEmail(),
            'phone' => fake()->numerify('07#########'),
            'address_1' => fake()->streetAddress(),
            'address_2' => fake()->secondaryAddress(),
            'city' => fake()->city(),
            'county' => fake()->city(),
            'country' => 'UK',
            'postcode' => strtoupper(fake()->bothify('??## ???')),
        ];
    }
}
