# Working Days Service

The Working Days Service provides functionality to calculate working days while excluding weekends and UK bank holidays.

## Features

- Add or subtract working days from any date
- Check if a specific date is a working day
- Count working days between two dates
- Automatically excludes weekends (Saturday and Sunday)
- Automatically excludes UK bank holidays for England and Wales
- Caches bank holiday data for performance
- Handles null dates (defaults to current date)

## Usage

### Basic Usage

```php
use App\Services\WorkingDays\WorkingDaysService;
use Illuminate\Support\Carbon;

// Resolve from container
$workingDaysService = app(WorkingDaysService::class);

// Add 5 working days from today
$futureDate = $workingDaysService->addWorkingDays(5);

// Add 3 working days from a specific date
$startDate = Carbon::parse('2024-01-01');
$result = $workingDaysService->addWorkingDays(3, $startDate);

// Subtract 2 working days
$pastDate = $workingDaysService->subtractWorkingDays(2, $startDate);
```

### Check Working Days

```php
// Check if a date is a working day
$date = Carbon::parse('2024-01-01'); // New Year's Day
$isWorkingDay = $workingDaysService->isWorkingDay($date); // false (bank holiday)

$date = Carbon::parse('2024-01-02'); // Tuesday
$isWorkingDay = $workingDaysService->isWorkingDay($date); // true

$date = Carbon::parse('2024-01-06'); // Saturday
$isWorkingDay = $workingDaysService->isWorkingDay($date); // false (weekend)
```

### Count Working Days

```php
// Count working days between two dates (exclusive)
$start = Carbon::parse('2024-01-01');
$end = Carbon::parse('2024-01-10');
$count = $workingDaysService->countWorkingDaysBetween($start, $end);
```

### Convenience Methods

```php
// Get next working day
$nextWorkingDay = $workingDaysService->getNextWorkingDay();
$nextWorkingDay = $workingDaysService->getNextWorkingDay($someDate);

// Get previous working day
$previousWorkingDay = $workingDaysService->getPreviousWorkingDay();
$previousWorkingDay = $workingDaysService->getPreviousWorkingDay($someDate);
```

## Configuration

The service uses the UK Government's bank holidays API. Configuration is in `config/services.php`:

```php
'uk_bank_holidays' => [
    'url' => 'https://www.gov.uk/bank-holidays.json',
    'cache_ttl' => env('UK_BANK_HOLIDAYS_CACHE_TTL', 86400), // 24 hours default
],
```

## Bank Holiday Caching

Bank holidays are automatically cached for 24 hours (configurable) to improve performance and reduce API calls. The cache can be cleared manually:

```php
use App\Services\WorkingDays\UkBankHolidayService;

$bankHolidayService = app(UkBankHolidayService::class);
$bankHolidayService->clearCache();
```

## Error Handling

- The service throws `InvalidArgumentException` for negative working days
- If the UK bank holidays API is unavailable, the service continues to work but only excludes weekends
- API failures are logged but don't break the service functionality

## Examples

### Business Scenario: Invoice Due Dates

```php
// Set invoice due date to 30 working days from today
$invoice->due_date = $workingDaysService->addWorkingDays(30);
```

### Business Scenario: SLA Calculations

```php
// Calculate if we're within SLA (5 working days)
$ticketCreated = Carbon::parse($ticket->created_at);
$slaDeadline = $workingDaysService->addWorkingDays(5, $ticketCreated);
$isWithinSla = Carbon::now()->lte($slaDeadline);
```

### Business Scenario: Project Planning

```php
// Calculate project duration in working days
$projectStart = Carbon::parse('2024-01-01');
$projectEnd = Carbon::parse('2024-01-31');
$workingDays = $workingDaysService->countWorkingDaysBetween($projectStart, $projectEnd);
echo "Project duration: {$workingDays} working days";
```

## Testing

The service includes comprehensive tests covering:
- Weekend exclusion
- Bank holiday exclusion
- Edge cases (zero days, negative days)
- API failure scenarios
- Date boundary conditions

Run tests with:
```bash
php artisan test tests/Unit/Services/WorkingDays/
```
