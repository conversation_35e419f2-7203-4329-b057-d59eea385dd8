<?php

namespace OvonGroup\ManufacturerLogos;

use OvonGroup\ManufacturerLogos\Commands\SeedManufacturerLogosCommand;
use <PERSON><PERSON>\LaravelPackageTools\Package;
use <PERSON><PERSON>\LaravelPackageTools\PackageServiceProvider;

class ManufacturerLogosServiceProvider extends PackageServiceProvider
{
    public function configurePackage(Package $package): void
    {
        /*
         * This class is a Package Service Provider
         *
         * More info: https://github.com/spatie/laravel-package-tools
         */
        $package
            ->name('manufacturer-logos')
            ->hasConfigFile()
            ->hasViews()
            ->hasMigration('create_manufacturers_table')
            ->hasCommand(SeedManufacturerLogosCommand::class);
    }
}
