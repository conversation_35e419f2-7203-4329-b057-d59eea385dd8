<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;
use OvonGroup\ManufacturerLogos\Commands\SeedManufacturerLogosCommand;

return new class extends Migration
{
     public function up(): void
     {
         Schema::create('manufacturers', function (Blueprint $table) {
             $table->id();
             $table->string('name');
             $table->string('slug')->unique();
             $table->string('logo')->nullable();
             $table->timestamps();
         });

         echo "\nSeeding and copying logos...\n";

         Artisan::call(SeedManufacturerLogosCommand::class);
     }
};
