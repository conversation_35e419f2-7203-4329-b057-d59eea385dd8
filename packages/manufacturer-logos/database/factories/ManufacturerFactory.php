<?php

namespace OvonGroup\ManufacturerLogos\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use OvonGroup\ManufacturerLogos\Models\Manufacturer;

class ManufacturerFactory extends Factory
{
    protected $model = Manufacturer::class;

    public function definition(): array
    {
        return [
            'name' => $name = fake()->unique()->randomElement(['Mercedes', 'Ford', 'Vauxhall', 'Audi', 'BMW', 'Volkswagen']),
            'slug' => Str::slug($name),
        ];
    }
}
