{"name": "ovon-group/manufacturer-logos", "description": "This is my package manufacturer-logos", "keywords": ["Ovon Group", "laravel", "manufacturer-logos"], "homepage": "https://github.com/ovon-group/manufacturer-logos", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.4", "spatie/laravel-package-tools": "^1.16", "illuminate/contracts": "^10.0||^11.0||^12.0"}, "require-dev": {"laravel/pint": "^1.14", "nunomaduro/collision": "^8.1.1||^7.10.0", "orchestra/testbench": "^10.0.0||^9.0.0||^8.22.0", "pestphp/pest": "^3.0", "pestphp/pest-plugin-arch": "^3.0", "pestphp/pest-plugin-laravel": "^3.0"}, "autoload": {"psr-4": {"OvonGroup\\ManufacturerLogos\\": "src/", "OvonGroup\\ManufacturerLogos\\Database\\Factories\\": "database/factories/"}}, "autoload-dev": {"psr-4": {"OvonGroup\\ManufacturerLogos\\Tests\\": "tests/", "Workbench\\App\\": "workbench/app/"}}, "scripts": {"post-autoload-dump": "@composer run prepare", "prepare": "@php vendor/bin/testbench package:discover --ansi", "analyse": "vendor/bin/phpstan analyse", "test": "vendor/bin/pest", "test-coverage": "vendor/bin/pest --coverage", "format": "vendor/bin/pint"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "phpstan/extension-installer": true}}, "extra": {"laravel": {"providers": ["OvonGroup\\ManufacturerLogos\\ManufacturerLogosServiceProvider"], "aliases": {"ManufacturerLogos": "OvonGroup\\ManufacturerLogos\\Facades\\ManufacturerLogos"}}}, "minimum-stability": "dev", "prefer-stable": true}