<?php

return [
    'default' => env('DEFAULT_PAYMENT_PROVIDER', 'go_cardless'),

    'go_cardless' => [
        'access_token' => env('GO_CARDLESS_ACCESS_TOKEN'),
        'environment' => env('GO_CARDLESS_ENVIRONMENT', 'sandbox'),
        'manage_url' => env('GO_CARDLESS_ENVIRONMENT') === 'sandbox' ? 'https://manage-sandbox.gocardless.com' : 'https://manage.gocardless.com',
    ],

    'access_paysuite' => [
        'environment' => env('ACCESS_PAYSUITE_ENVIRONMENT', 'playpen'),
        'client_code' => env('ACCESS_PAYSUITE_CLIENT_CODE'),
        'api_key' => env('ACCESS_PAYSUITE_API_KEY'),
        'manage_url' => match (env('ACCESS_PAYSUITE_ENVIRONMENT', 'playpen')) {
            'playpen' => 'https://playpen.accesspaysuite.com',
            'live' => 'https://ecm3.eazycollect.co.uk',
        },
    ],
];
