<?php

return [

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'dvla_ves' => [
        'key' => env('DVLA_VES_KEY'),
    ],

    'motorspecs' => [
        'environment' => env('MOTORSPECS_ENVIRONMENT'),
        'client_id' => env('MOTORSPECS_CLIENT_ID'),
        'client_secret' => env('MOTORSPECS_CLIENT_SECRET'),
        'cache_ttl' => env('MOTORSPECS_CACHE_TTL', 1440),
    ],

    'ideal_postcodes' => [
        'api_key' => env('IDEAL_POSTCODES_API_KEY', 'ak_test'),
    ],

    'xero' => [
        'client_id' => env('XERO_CLIENT_ID'),
        'secret' => env('XERO_SECRET'),
        'base_url' => 'https://api.xero.com/api.xro/2.0/',
        'view_invoice_url' => 'https://invoicing.xero.com/view/%s',
        'view_bank_transaction_url' => 'https://go.xero.com/Bank/ViewTransaction.aspx?bankTransactionID=%s',
        'current_bank_account_id' => env('XERO_BANK_ACCOUNT_ID'),
        'go_cardless_bank_account_id' => env('XERO_GO_CARDLESS_BANK_ACCOUNT_ID'),
        'go_cardless_fee_account_code' => env('XERO_GO_CARDLESS_FEE_ACCOUNT_CODE'),
        'go_cardless_contact_id' => env('XERO_GO_CARDLESS_CONTACT_ID'),
        'access_paysuite_bank_account_id' => env('XERO_ACCESS_PAYSUITE_BANK_ACCOUNT_ID'),
        'subscription_contact_id' => env('XERO_SUBSCRIPTION_CONTACT_ID'),
        'webhook_key' => env('XERO_WEBHOOK_KEY'),
    ],

    'dvsa_mot_history' => [
        'key' => env('DVSA_MOT_HISTORY_KEY'),
        'client_id' => env('DVSA_MOT_HISTORY_CLIENT_ID'),
        'client_secret' => env('DVSA_MOT_HISTORY_SECRET'),
        'scope_url' => 'https://tapi.dvsa.gov.uk/.default',
        'tenant_id' => env('DVSA_MOT_HISTORY_TENANT_ID'),
    ],

    'google_places' => [
        'api_key' => env('GOOGLE_PLACES_API_KEY'),
    ],

    '3cx' => [
        'endpoint' => env('VOIP_3CX_ENDPOINT'),
        'client_id' => env('VOIP_3CX_CLIENT_ID'),
        'api_key' => env('VOIP_3CX_API_KEY'),
    ],

    'uk_bank_holidays' => [
        'url' => 'https://www.gov.uk/bank-holidays.json',
        'cache_ttl' => env('UK_BANK_HOLIDAYS_CACHE_TTL', 86400), // 24 hours default
    ],

];
