<?php

return [

    'guards' => [
        'customer' => [
            'driver' => 'session',
            'provider' => 'customers',
        ],

        'api' => [
            'driver' => 'token',
            'provider' => 'users',
            'hash' => false,
        ],
    ],

    'providers' => [
        'customers' => [
            'driver' => 'eloquent',
            'model' => \App\Models\Customer::class,
        ],
    ],

    'passwords' => [
        'invite' => [
            'provider' => 'users',
            'table' => 'user_invites',
            'expire' => 60 * 24 * 7,
            'throttle' => 60,
        ],

        'reset' => [
            'provider' => 'users',
            'table' => 'password_reset_tokens',
            'expire' => 60,
            'throttle' => 60,
        ],
    ],

];
