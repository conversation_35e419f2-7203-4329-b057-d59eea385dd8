<?php

return [
    'nominal_codes' => [
        'service_plan' => [
            'admin_fees' => [
                'code' => '264',
                'name' => 'Admin Fees - Service Plan',
                'class' => 'REVENUE',
                'type' => 'REVENUE',
            ],
            'held_for_dealer' => [
                'code' => '268',
                'name' => 'Monthly Service Plan Sales - Held For Dealer',
                'class' => 'LIABILITY',
                'type' => 'CURRLIAB',
            ],
        ],
        'warranty' => [
            'subscription_managed_fund' => [
                'code' => '806',
                'name' => 'Managed Fund Warranty Sales',
                'description' => 'Income from Managed Fund Warranty Sales',
                'class' => 'REVENUE',
                'type' => 'REVENUE',
            ],
            'subscription_self_funded' => [
                'code' => '261',
                'name' => 'Monthly Warranty Sales - Held For Dealer',
                'class' => 'LIABILITY',
                'type' => 'CURRLIAB',
            ],
            'self_funded_admin_fees' => [
                'code' => '262',
                'name' => 'Admin Fees - Warranty',
                'class' => 'REVENUE',
                'type' => 'REVENUE',
            ],
            'managed_fund_sales' => [
                'code' => '199',
                'name' => 'Managed Fund Warranty Sale',
                'class' => 'REVENUE',
                'type' => 'REVENUE',
            ],
            'self_funded_provision_retained' => [
                'code' => '808',
                'name' => 'Dealer Provision Retained',
                'description' => 'Retained provision held on behalf of dealer',
                'class' => 'REVENUE',
                'type' => 'REVENUE',
            ],
        ],
        'breakdown' => [
            'managed_fund_revenue' => [
                'code' => '201',
                'name' => 'Managed Fund Breakdown Sales',
                'description' => 'Income from Managed Fund Breakdown Sales',
                'class' => 'REVENUE',
                'type' => 'REVENUE',
            ],
            'self_funded_revenue' => [
                'code' => '203',
                'name' => 'Dealer Funded Breakdown Sales',
                'description' => 'Income from Dealer Funded Breakdown Sales',
                'class' => 'REVENUE',
                'type' => 'REVENUE',
            ],
        ],
        'provision' => [
            'warranty' => [
                'code' => '806',
                'name' => 'Warranty Provision',
                'description' => 'Current Liability against Warranty Provision',
                'class' => 'LIABILITY',
                'type' => 'CURRLIAB',
            ],
            'breakdown' => [
                'code' => '804',
                'name' => 'Breakdown Provision',
                'description' => 'Current Liability against Breakdown Provision',
                'class' => 'LIABILITY',
                'type' => 'CURRLIAB',
            ],
        ],
        'other' => [
            'warranty_claim_repair_reimbursement' => [
                'code' => '202',
                'name' => 'Repair Revenue',
                'description' => 'Reimbursement of Warranty Repair Costs',
                'class' => 'REVENUE',
                'type' => 'REVENUE',
            ],
            'go_cardless_fees' => [
                'code' => '405',
                'name' => 'GoCardlessFees',
                'description' => 'Fees charged by GoCardless for processing payments',
                'class' => 'EXPENSE',
                'type' => 'EXPENSE',
            ],
        ],
    ],
];
