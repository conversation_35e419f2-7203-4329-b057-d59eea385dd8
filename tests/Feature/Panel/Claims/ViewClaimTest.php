<?php

use App\Enums\ClaimEstimateStatus;
use App\Enums\ClaimStatus;
use App\Filament\Resources\ClaimEstimateResource;
use App\Filament\Resources\ClaimResource\Pages\ViewClaim;
use App\Filament\Resources\ClaimResource\RelationManagers\EstimatesRelationManager;
use App\Models\Account;
use App\Models\Claim;
use App\Models\ClaimAuthorisation;
use App\Models\ClaimEstimate;
use App\Models\ClaimRejection;
use App\Models\User;
use Filament\Forms\Components\Repeater;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

beforeEach(function () {
    Model::withoutEvents(function () {
        $this->account = Account::factory()->create();
        $this->user = User::factory()->recycle($this->account)->create();
        $this->claim = Claim::factory()->recycle($this->account)->create();
    });

    actingAs($this->user);
});

test('users without permission cannot view claim details', function () {
    livewire(ViewClaim::class, ['record' => $this->claim->getRouteKey()])->assertForbidden();
});

test('users with permission can view claim details', function () {
    $this->user->givePermissionTo('claims.view');

    livewire(ViewClaim::class, ['record' => $this->claim->reference])
        ->assertSuccessful()
        ->assertSee($this->claim->reference)
        ->assertSee($this->claim->warranty->sale->customer->full_name)
        ->assertSee($this->claim->warranty->sale->customer->email)
        ->assertSee($this->claim->warranty->sale->customer->phone)
        ->assertSee($this->claim->warranty->sale->id)
        ->assertSee($this->claim->warranty->sale->vehicle->vrm)
        ->assertSee($this->claim->warranty->sale->vehicle->vin)
        ->assertSee($this->claim->warranty->sale->vehicle->make)
        ->assertSee($this->claim->warranty->sale->vehicle->model)
        ->assertSee($this->claim->warranty->sale->vehicle->colour)
        ->assertSee($this->claim->warranty->sale->vehicle->fuel_type)
        ->assertSee($this->claim->warranty->sale->vehicle->transmission_type)
        ->assertSee($this->claim->warranty->sale->last_service_date->formatLocal())
        ->assertSee(number_format($this->claim->warranty->sale->last_service_mileage))
        ->assertSee($this->claim->warranty->sale->start_date->formatLocal())
        ->assertSee($this->claim->warranty->end_date->formatLocal());
});

test('users cannot reject a claim that has authorised estimates', function () {
    $this->user->givePermissionTo(
        'claims.view',
        'claim-rejections.create',
    );
    ClaimEstimate::factory()->recycle($this->claim)->hasAuthorisation()->create();

    livewire(ViewClaim::class, ['record' => $this->claim->reference])
        ->assertActionHidden('reject_claim');
});

test('users cannot reject a claim that is already rejected', function () {
    $this->user->givePermissionTo(
        'claims.view',
        'claim-rejections.create',
    );
    ClaimRejection::factory()->for($this->claim)->create();

    livewire(ViewClaim::class, ['record' => $this->claim->reference])
        ->assertActionHidden('reject_claim');
});

test('users with permission can reject a claim', function () {
    $this->user->givePermissionTo(
        'claims.view',
        'claim-rejections.create',
    );

    livewire(ViewClaim::class, ['record' => $this->claim->reference])
        ->assertSuccessful()
        ->assertActionVisible('reject_claim')
        ->mountAction('reject_claim')
        ->assertSee('Reject claim')
        ->callMountedAction()
        ->assertHasActionErrors([
            'reason',
            'notes',
        ])
        ->setActionData([
            'reason' => 'Not covered under terms',
            'notes' => 'Some notes for the rejection',
        ])
        ->callMountedAction()
        ->assertHasNoActionErrors();

    expect($this->claim->refresh()->status->value)->toBe(ClaimStatus::REJECTED->value)
        ->and($this->claim->rejection)
        ->reason->toBe('Not covered under terms')
        ->notes->toBe('Some notes for the rejection');
});

test('users without permission cannot create estimate for claim', function () {
    $this->user->givePermissionTo('claims.view');

    livewire(ViewClaim::class, ['record' => $this->claim->getRouteKey()])
        ->assertSuccessful()
        ->assertDontSee('New Estimate');
})->todo('False positive. We need to test this functionality');

test('users with permission can create invoice billable estimate for claim', function () {
    Carbon::setTestNow(now()->startOfSecond());

    $this->user->givePermissionTo(
        'claim-estimates.view',
        'claim-estimates.create',
    );

    $repairer = \App\Models\Repairer::factory()->create();

    // https://filamentphp.com/docs/3.x/forms/fields/repeater#testing-repeaters
    $undoRepeaterFake = Repeater::fake();

    livewire(ClaimEstimateResource\Pages\CreateClaimEstimate::class, [
        'claim' => $this->claim,
    ])
        ->assertSuccessful()
        ->assertFormExists()
        ->assertSee('Create Claim Estimate')
        ->fillForm([
            'repairer_id' => $repairer->id,
            'workshop_contact' => 'Test Contact',
            'workshop_phone' => '0123456789',
            'workshop_email' => '<EMAIL>',
            'current_mileage' => $this->claim->warranty->sale->delivery_mileage,
            'work_required' => 'Test Work Required',
            'estimate_booking_date' => today()->subDay(),
            'estimate_completed' => true,
            'drop_off_time' => '10:00',
            'line_items' => [
                [
                    'type' => \App\Enums\ClaimEstimateLineItemType::PART->value,
                    'description' => 'Parts description',
                    'quantity' => 1,
                    'amount' => 100,
                ],
                [
                    'type' => \App\Enums\ClaimEstimateLineItemType::PART->value,
                    'description' => 'Parts description',
                    'quantity' => 2,
                    'amount' => 200,
                ],
                [
                    'type' => \App\Enums\ClaimEstimateLineItemType::LABOUR->value,
                    'description' => 'Labour description',
                    'quantity' => 1,
                    'amount' => 120,
                ],
            ],
        ])
        ->assertFormSet([
            'line_items' => [
                [
                    'type' => \App\Enums\ClaimEstimateLineItemType::PART->value,
                    'description' => 'Parts description',
                    'quantity' => 1,
                    'amount' => 100,
                    'vat' => 20,
                ],
                [
                    'type' => \App\Enums\ClaimEstimateLineItemType::PART->value,
                    'description' => 'Parts description',
                    'quantity' => 2,
                    'amount' => 200,
                    'vat' => 40,
                ],
                [
                    'type' => \App\Enums\ClaimEstimateLineItemType::LABOUR->value,
                    'description' => 'Labour description',
                    'quantity' => 1,
                    'amount' => 120,
                    'vat' => 24,
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoTableActionErrors();

    $undoRepeaterFake();

    expect(ClaimEstimate::count())->toBe(1)
        ->and(ClaimEstimate::first())
        ->workshop_name->toBe($repairer->name)
        ->workshop_contact->toBe('Test Contact')
        ->workshop_phone->toBe('0123456789')
        ->workshop_email->toBe('<EMAIL>')
        ->current_mileage->toEqual($this->claim->warranty->sale->delivery_mileage)
        ->estimate_booking_date->toEqual(today()->subDay())
        ->estimate_completed_at->toEqual(now())
        ->drop_off_time->toBe('10:00')
        ->totalAmount()->toEqual(744)
        ->status()->value->toEqual(ClaimEstimateStatus::AWAITING_DECISION->value);
});

test('users with permission can create internal billing estimate for claim', function () {
    $this->user->givePermissionTo(
        'claim-estimates.view',
        'claim-estimates.create'
    );

    livewire(ClaimEstimateResource\Pages\CreateClaimEstimate::class, [
        'claim' => $this->claim,
    ])
        ->assertSuccessful()
        ->assertFormExists()
        ->fillForm([
            'workshop_contact' => 'Test Contact',
            'workshop_phone' => '0123456789',
            'workshop_email' => '<EMAIL>',
            'current_mileage' => $this->claim->warranty->sale->delivery_mileage + 1000,
            'is_charged_internally' => true,
            'line_items' => [
                [
                    'type' => \App\Enums\ClaimEstimateLineItemType::PART->value,
                    'description' => 'Parts description',
                    'quantity' => 1,
                    'amount' => 100,
                ],
                [
                    'type' => \App\Enums\ClaimEstimateLineItemType::PART->value,
                    'description' => 'Parts description',
                    'quantity' => 2,
                    'amount' => 200,
                ],
                [
                    'type' => \App\Enums\ClaimEstimateLineItemType::LABOUR->value,
                    'description' => 'Labour description',
                    'quantity' => 1,
                    'amount' => 120,
                ],
            ],
        ])
        ->assertFormSet([
            'line_items' => [
                [
                    'vat' => 0,
                ],
                [
                    'vat' => 0,
                ],
                [
                    'vat' => 0,
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoTableActionErrors();

    expect(ClaimEstimate::count())->toBe(1)
        ->and(ClaimEstimate::first())
        ->workshop_name->toBeNull()
        ->current_mileage->toEqual($this->claim->warranty->sale->delivery_mileage + 1000)
        ->vat()->toEqual(0)
        ->totalAmount()->toEqual(620)
        ->status()->value->toBe(ClaimEstimateStatus::PENDING->value);
});

describe('test claim estimate and authorisation permissions', function () {
    beforeEach(function () {
        Model::withoutEvents(function () {

            $this->estimate = ClaimEstimate::factory()->for($this->claim)->create();
            $this->authorisedEstimate = ClaimEstimate::factory()->for($this->claim)->hasAuthorisation()->create();
            $this->authorisedInvoicedEstimate = ClaimEstimate::factory()->for($this->claim)
                ->has(ClaimAuthorisation::factory()->recycle($this->account)->invoiced(), 'authorisation')
                ->create();
            $this->authorisedInvoicedPaidEstimate = ClaimEstimate::factory()->for($this->claim)
                ->has(
                    ClaimAuthorisation::factory()
                        ->recycle($this->account)
                        ->invoicedAndSettled(),
                    'authorisation'
                )
                ->create();
            $this->internalAuthorisedEstimate = ClaimEstimate::factory()->for($this->claim)->hasAuthorisation()->internal()->create();
            $this->directInvoiceAuthorisedEstimate = ClaimEstimate::factory()->for($this->claim)->hasAuthorisation()->directInvoice()->create();
        });
    });

    test('estimate and authorisation visibility for users without correct permissions', function () {
        livewire(EstimatesRelationManager::class, [
            'pageClass' => ViewClaim::class,
            'ownerRecord' => $this->claim,
        ])
            ->assertSuccessful()
            ->assertCountTableRecords(6)
            ->assertCanSeeTableRecords([$this->estimate])
            ->assertTableColumnStateSet('created_at', $this->estimate->created_at, $this->estimate)
            ->assertTableColumnStateSet('billing_status', $this->estimate->billingStatus(), $this->estimate)
            ->assertTableColumnFormattedStateSet('status', $this->estimate->status()->value, $this->estimate)
            ->assertTableColumnStateSet('enteredBy.name', $this->estimate->enteredBy->name, $this->estimate)
            ->assertTableColumnStateSet('workshop_name', $this->estimate->workshop_name, $this->estimate)
            ->assertTableColumnStateSet('total_estimate', $this->estimate->totalAmount(), $this->estimate)
            ->assertTableColumnStateSet('created_at', $this->estimate->created_at, $this->estimate)
            ->assertTableActionHidden('view_estimate', $this->estimate);
        //            ->assertTableActionHidden('edit_estimate', $this->estimate)
        //            ->assertTableActionHidden('delete_estimate', $this->estimate)
        //            ->assertTableActionHidden('view_invoice', $this->estimate)
        //            ->assertTableActionHidden('claim_authorisation', $this->authorisedEstimate)
        //            ->assertTableActionHidden('view_estimate', $this->authorisedEstimate)
        //            ->assertTableActionHidden('edit_estimate', $this->authorisedEstimate)
        //            ->assertTableActionHidden('delete_estimate', $this->authorisedEstimate)
        //            ->assertTableActionHidden('view_invoice', $this->authorisedEstimate)
        //            ->assertTableActionHidden('claim_authorisation', $this->authorisedInvoicedEstimate)
        //            ->assertTableActionHidden('view_estimate', $this->authorisedInvoicedEstimate)
        //            ->assertTableActionHidden('edit_estimate', $this->authorisedInvoicedEstimate)
        //            ->assertTableActionHidden('delete_estimate', $this->authorisedInvoicedEstimate)
        //            ->assertTableActionHidden('view_invoice', $this->authorisedInvoicedEstimate);
    })->todo('rewrite this test on view claim');

    test('estimate visibility for users with correct permissions', function () {
        $this->user->givePermissionTo(
            'claim-estimates.view',
            'claim-estimates.update',
            'claim-estimates.delete',
        );

        livewire(ViewClaim::class, [
            'claim' => $this->claim,
        ])
            ->assertSuccessful()
            ->assertTableActionHidden('claim_authorisation', $this->estimate)
            ->assertTableActionVisible('view_estimate', $this->estimate)
            ->assertTableActionVisible('edit_estimate', $this->estimate)
            ->assertTableActionVisible('delete_estimate', $this->estimate)
            ->assertTableActionHidden('view_invoice', $this->estimate)
            ->assertTableActionHidden('claim_authorisation', $this->authorisedEstimate)
            ->assertTableActionVisible('view_estimate', $this->authorisedEstimate)
            ->assertTableActionHidden('edit_estimate', $this->authorisedEstimate)
            ->assertTableActionHidden('delete_estimate', $this->authorisedEstimate)
            ->assertTableActionHidden('view_invoice', $this->authorisedEstimate)
            ->assertTableActionHidden('claim_authorisation', $this->authorisedInvoicedEstimate)
            ->assertTableActionVisible('view_estimate', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('edit_estimate', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('delete_estimate', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('view_invoice', $this->authorisedInvoicedEstimate);
    })->todo('replicate this test on view claim');

    test('authorisation visibility for users with correct permissions', function () {
        $this->user->givePermissionTo(
            'claim-authorisations.view',
            'claim-authorisations.create',
            'claim-authorisations.update',
        );

        livewire(EstimatesRelationManager::class, [
            'pageClass' => ViewClaim::class,
            'ownerRecord' => $this->claim,
        ])
            ->assertSuccessful()
            ->assertTableActionVisible('claim_authorisation', $this->estimate)
            ->assertTableActionVisible('claim_authorisation', $this->authorisedEstimate)
            ->assertTableActionVisible('claim_authorisation', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('view_estimate', $this->estimate)
            ->assertTableActionHidden('edit_estimate', $this->estimate)
            ->assertTableActionHidden('delete_estimate', $this->estimate)
            ->assertTableActionHidden('view_invoice', $this->estimate)
            ->assertTableActionHidden('view_estimate', $this->authorisedEstimate)
            ->assertTableActionHidden('edit_estimate', $this->authorisedEstimate)
            ->assertTableActionHidden('delete_estimate', $this->authorisedEstimate)
            ->assertTableActionHidden('view_invoice', $this->authorisedEstimate)
            ->assertTableActionHidden('view_estimate', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('edit_estimate', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('delete_estimate', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('view_invoice', $this->authorisedInvoicedEstimate);
    })->todo('replicate this test on view claim');

    test('invoice view visibility for users with correct permissions', function () {
        $this->user->givePermissionTo(
            'invoices.view',
        );

        livewire(EstimatesRelationManager::class, [
            'pageClass' => ViewClaim::class,
            'ownerRecord' => $this->claim,
        ])
            ->assertSuccessful()
            ->assertTableActionHidden('claim_authorisation', $this->estimate)
            ->assertTableActionHidden('claim_authorisation', $this->authorisedEstimate)
            ->assertTableActionHidden('claim_authorisation', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('view_estimate', $this->estimate)
            ->assertTableActionHidden('edit_estimate', $this->estimate)
            ->assertTableActionHidden('delete_estimate', $this->estimate)
            ->assertTableActionHidden('view_invoice', $this->estimate)
            ->assertTableActionHidden('view_estimate', $this->authorisedEstimate)
            ->assertTableActionHidden('edit_estimate', $this->authorisedEstimate)
            ->assertTableActionHidden('delete_estimate', $this->authorisedEstimate)
            ->assertTableActionHidden('view_invoice', $this->authorisedEstimate)
            ->assertTableActionHidden('view_estimate', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('edit_estimate', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('delete_estimate', $this->authorisedInvoicedEstimate)
            ->assertTableActionVisible('view_invoice', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('view_invoice', $this->internalAuthorisedEstimate)
            ->assertTableActionHidden('view_invoice', $this->directInvoiceAuthorisedEstimate);
    })->todo('replicate this test on view claim');

    test('invoice generate visibility for users with correct permissions', function () {
        $this->user->givePermissionTo(
            'invoices.create',
        );

        livewire(EstimatesRelationManager::class, [
            'pageClass' => ViewClaim::class,
            'ownerRecord' => $this->claim,
        ])
            ->assertSuccessful()
            ->assertTableActionHidden('view_invoice', $this->estimate)
            ->assertTableActionHidden('view_invoice', $this->authorisedEstimate)
            ->assertTableActionHidden('view_invoice', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('generate_invoice', $this->estimate)
            ->assertTableActionVisible('generate_invoice', $this->authorisedEstimate)
            ->assertTableActionHidden('generate_invoice', $this->authorisedInvoicedEstimate)
            ->assertTableActionHidden('generate_invoice', $this->internalAuthorisedEstimate)
            ->assertTableActionHidden('generate_invoice', $this->directInvoiceAuthorisedEstimate);
    })->todo('replicate this test on view claim');

    test('estimate billing statuses', function () {
        expect($this->directInvoiceAuthorisedEstimate->billingStatus())->toBe(ClaimEstimate::BILLING_STATUS_DIRECT)
            ->and($this->internalAuthorisedEstimate->billingStatus())->toBe(ClaimEstimate::BILLING_STATUS_INTERNAL)
            ->and($this->estimate->billingStatus())->toBe(ClaimEstimate::BILLING_STATUS_OUTSTANDING)
            ->and($this->authorisedInvoicedEstimate->billingStatus())->toBe(ClaimEstimate::BILLING_STATUS_INVOICED)
            ->and($this->authorisedInvoicedPaidEstimate->billingStatus())->toBe(ClaimEstimate::BILLING_STATUS_PAID);
    });
});
