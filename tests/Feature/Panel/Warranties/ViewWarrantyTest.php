<?php

use App\Enums\ClaimStatus;
use App\Filament\Resources\ClaimEstimateResource;
use App\Filament\Resources\SaleResource\Pages\ViewWarranty;
use App\Filament\Resources\SaleResource\RelationManagers\WarrantyClaimsRelationManager;
use App\Models\Account;
use App\Models\Claim;
use App\Models\Sale;
use App\Models\User;
use App\Notifications\Claims\CustomerClaimStarted;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Notification;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

beforeEach(function () {
    Model::withoutEvents(function () {
        $this->account = Account::factory()->create();
        $this->user = User::factory()->recycle($this->account)->create();
        $this->sale = Sale::factory()->recycle($this->account)->hasWarranty()->create();
    });

    actingAs($this->user);

    Notification::fake();
});

test('forbidden for users without correct permission', function () {
    livewire(ViewWarranty::class, ['record' => $this->sale->id])->assertForbidden();
});

test('visible for users with correct permission', function () {
    $this->user->givePermissionTo('sales.view');

    livewire(ViewWarranty::class, ['record' => $this->sale->id])
        ->assertSuccessful()
        ->assertSee($this->sale->warranty->product->name)
        ->assertSee($this->sale->warranty->period.' months')
        ->assertSee($this->sale->start_date->formatLocal())
        ->assertSee($this->sale->warranty->end_date->formatLocal())
        ->assertSee(number_format($this->sale->warranty->annual_mileage_limit))
        ->assertSee(number_format($this->sale->warranty->mileage_cutoff))
        ->assertSee(number_format($this->sale->warranty->selling_price, 2))
        ->assertSee(number_format($this->sale->warranty->admin_fee, 2))
        ->assertSee(number_format($this->sale->warranty->provision, 2));
});

describe('viewing warranty claims', function () {

    beforeEach(function () {
        Model::withoutEvents(function () {
            $this->claim = Claim::factory()->for($this->sale->warranty)->create();
            $this->claimWithEstimate = Claim::factory()->for($this->sale->warranty)->hasEstimates()->create();
            $this->authorisedClaim = Claim::factory()->for($this->sale->warranty)->authorised()->create();
            $this->rejectedClaim = Claim::factory()->for($this->sale->warranty)->hasRejection()->create();
        });
    });

    test('claims hidden for users without permission', function () {
        //        $this->user->givePermissionTo('sales.view');

        livewire(WarrantyClaimsRelationManager::class, [
            'ownerRecord' => $this->sale,
            'pageClass' => ViewWarranty::class,
        ])
            ->assertSuccessful();
    })->todo('This fails because of how different relation managers are implemented in different pages.');

    test('claims visible for users with permission', function () {
        //        $this->user->givePermissionTo('sales.view');

        livewire(WarrantyClaimsRelationManager::class, [
            'ownerRecord' => $this->sale,
            'pageClass' => ViewWarranty::class,
        ])->assertSuccessful()
            ->assertCountTableRecords(4)
            ->assertCanSeeTableRecords([$this->claim, $this->authorisedClaim, $this->rejectedClaim])
            ->assertTableColumnStateSet('reference', $this->claim->reference, $this->claim)
            ->assertTableColumnFormattedStateSet('status', $this->claim->status->value, $this->claim)
            ->assertTableColumnStateSet('faultType.name', $this->claim->faultType->name, $this->claim)
            ->assertTableColumnStateSet('reference', $this->authorisedClaim->reference, $this->authorisedClaim)
            ->assertTableColumnFormattedStateSet('status', $this->authorisedClaim->status->value, $this->authorisedClaim)
            ->assertTableColumnStateSet('faultType.name', $this->authorisedClaim->faultType->name, $this->authorisedClaim)
            ->assertTableColumnStateSet('reference', $this->rejectedClaim->reference, $this->rejectedClaim)
            ->assertTableColumnFormattedStateSet('status', $this->rejectedClaim->status->value, $this->rejectedClaim)
            ->assertTableColumnStateSet('faultType.name', $this->rejectedClaim->faultType->name, $this->rejectedClaim);
    });

    test('claims viewable for users with permission', function () {
        $this->user->givePermissionTo('claims.view');

        livewire(WarrantyClaimsRelationManager::class, [
            'ownerRecord' => $this->sale,
            'pageClass' => ViewWarranty::class,
        ])
            ->assertTableActionVisible('view_claim', $this->claim)
            ->assertTableActionVisible('view_claim', $this->claimWithEstimate)
            ->assertTableActionVisible('view_claim', $this->authorisedClaim)
            ->assertTableActionVisible('view_claim', $this->rejectedClaim)
            ->assertTableActionHidden('edit_claim', $this->claim)
            ->assertTableActionHidden('edit_claim', $this->claimWithEstimate)
            ->assertTableActionHidden('edit_claim', $this->authorisedClaim)
            ->assertTableActionHidden('edit_claim', $this->rejectedClaim)
            ->assertTableActionHidden('delete_claim', $this->claim)
            ->assertTableActionHidden('delete_claim', $this->claimWithEstimate)
            ->assertTableActionHidden('delete_claim', $this->authorisedClaim)
            ->assertTableActionHidden('delete_claim', $this->rejectedClaim);
    });

    test('claims editable for users with permission', function () {
        $this->user->givePermissionTo('claims.update');

        livewire(WarrantyClaimsRelationManager::class, [
            'ownerRecord' => $this->sale,
            'pageClass' => ViewWarranty::class,
        ])
            ->assertTableActionHidden('view_claim', $this->claim)
            ->assertTableActionHidden('view_claim', $this->claimWithEstimate)
            ->assertTableActionHidden('view_claim', $this->authorisedClaim)
            ->assertTableActionHidden('view_claim', $this->rejectedClaim)
            ->assertTableActionVisible('edit_claim', $this->claim)
            ->assertTableActionHidden('edit_claim', $this->claimWithEstimate)
            ->assertTableActionHidden('edit_claim', $this->authorisedClaim)
            ->assertTableActionHidden('edit_claim', $this->rejectedClaim)
            ->assertTableActionHidden('delete_claim', $this->claim)
            ->assertTableActionHidden('delete_claim', $this->claimWithEstimate)
            ->assertTableActionHidden('delete_claim', $this->authorisedClaim)
            ->assertTableActionHidden('delete_claim', $this->rejectedClaim);
    });

    test('claims deletable for users with permission', function () {
        $this->user->givePermissionTo('claims.delete');

        livewire(WarrantyClaimsRelationManager::class, [
            'ownerRecord' => $this->sale,
            'pageClass' => ViewWarranty::class,
        ])
            ->assertTableActionHidden('view_claim', $this->claim)
            ->assertTableActionHidden('view_claim', $this->claimWithEstimate)
            ->assertTableActionHidden('view_claim', $this->authorisedClaim)
            ->assertTableActionHidden('view_claim', $this->rejectedClaim)
            ->assertTableActionHidden('edit_claim', $this->claim)
            ->assertTableActionHidden('edit_claim', $this->claimWithEstimate)
            ->assertTableActionHidden('edit_claim', $this->authorisedClaim)
            ->assertTableActionHidden('edit_claim', $this->rejectedClaim)
            ->assertTableActionVisible('delete_claim', $this->claim)
            ->assertTableActionHidden('delete_claim', $this->claimWithEstimate)
            ->assertTableActionHidden('delete_claim', $this->authorisedClaim)
            ->assertTableActionHidden('delete_claim', $this->rejectedClaim);
    });

    test('claim statuses', function () {
        expect($this->claim->status)->toBe(ClaimStatus::AWAITING_ESTIMATE)
            ->and($this->authorisedClaim->status)->toBe(ClaimStatus::AUTHORISED)
            ->and($this->rejectedClaim->status)->toBe(ClaimStatus::REJECTED);
    });

    test('cannot create a claim without permission', function () {
        livewire(WarrantyClaimsRelationManager::class, [
            'ownerRecord' => $this->sale,
            'pageClass' => ViewWarranty::class,
        ])
            ->assertTableActionHidden('create');
    });

});

test('can create a claim with permission', function () {
    $this->user->givePermissionTo('claims.create');

    $faultType = \App\Models\FaultType::factory()->create();

    livewire(WarrantyClaimsRelationManager::class, [
        'ownerRecord' => $this->sale,
        'pageClass' => ViewWarranty::class,
    ])
        ->assertTableActionVisible('create')
        ->mountTableAction('create')
        ->callMountedTableAction()
        ->assertHasTableActionErrors([
            'failure_date',
            'fault_type_id',
            'vehicle_location',
            'fault_description',
        ])
        ->setTableActionData([
            'failure_date' => today()->format('Y-m-d'),
            'fault_type_id' => $faultType->id,
            'vehicle_location' => 'Home',
            'fault_description' => 'Faulty thing',
            'current_mileage' => null,
        ])
        ->callMountedTableAction()
        ->assertHasNoTableActionErrors();

    Notification::assertSentTo($this->sale->customer, CustomerClaimStarted::class);

    expect(Claim::count())->toBe(1)
        ->and(Claim::first())
        ->warranty_id->toBe($this->sale->warranty->id)
        ->status->toBe(ClaimStatus::AWAITING_ESTIMATE)
        ->failure_date->toEqual(today())
        ->current_mileage->toBeNull()
        ->fault_type_id->toBe($faultType->id)
        ->vehicle_location->toBe('Home')
        ->fault_description->toBe('Faulty thing');
});

test('creating a claim validates the vehicle mileage', function () {
    $this->user->givePermissionTo('claims.create');

    livewire(WarrantyClaimsRelationManager::class, [
        'ownerRecord' => $this->sale,
        'pageClass' => ViewWarranty::class,
    ])
        ->assertTableActionVisible('create')
        ->mountTableAction('create')
        ->setTableActionData([
            'claims_checklist' => collect(ClaimEstimateResource::getChecklistItems($this->account))
                ->keys()
                ->all(),
            'failure_date' => today()->format('Y-m-d'),
            'fault_type_id' => \App\Models\FaultType::factory()->create()->id,
            'vehicle_location' => 'Home',
            'fault_description' => 'Faulty thing',
            'current_mileage' => $this->sale->delivery_mileage - 1,
        ])
        ->callMountedTableAction()
        ->assertHasTableActionErrors([
            'current_mileage' => 'min',
        ])
        ->setTableActionData([
            'current_mileage' => $this->sale->delivery_mileage,
        ])
        ->callMountedTableAction()
        ->assertHasNoTableActionErrors();

    expect(Claim::count())->toBe(1)
        ->and(Claim::first())
        ->current_mileage->toBe(40000);
});
