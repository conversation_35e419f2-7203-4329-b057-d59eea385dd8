<?php

use App\Filament\Resources\WarrantyResource\Pages\ListWarranties;
use App\Models\Account;
use App\Models\Sale;
use App\Models\User;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

test('rendering warranties list shows warranties for confirmed sales', function () {
    $account = Account::factory()->create();

    actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view'));

    $confirmedSale = Sale::factory()
        ->hasWarranty()
        ->confirmed($account)
        ->create();

    livewire(ListWarranties::class)
        ->assertSuccessful()
        ->assertTableColumnStateSet('sale_id', $confirmedSale->id, $confirmedSale->warranty);
});
