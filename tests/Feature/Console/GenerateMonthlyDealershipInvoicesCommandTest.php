<?php

use App\Console\Commands\GenerateMonthlyDealershipInvoicesCommand;
use App\Enums\FundType;
use App\Models\Account;
use App\Models\Dealership;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Sale;
use App\Services\Accounting\InvoiceData;
use App\Services\Accounting\XeroAccountingService;
use App\Services\Payments\DirectDebit\DirectDebitPaymentProcessor;
use App\Services\Payments\DirectDebit\PaymentData;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Notification;
use Mockery\MockInterface;

describe('subscription monthly payments', function () {

    test('monthly dealership invoices include sales from the correct period', function (FundType $fundType, Carbon $date) {
        $currentNow = Carbon::now();

        Carbon::setTestNow($date);
        $warrantyAccountCode = match ($fundType) {
            FundType::MANAGED => config('accounting.nominal_codes.warranty.managed_fund_sales.code'),
            FundType::DEALER, FundType::DEALER_RETAINED => config('accounting.nominal_codes.warranty.self_funded_admin_fees.code'),
        };

        $warrantyProvisionAccountCode = match ($fundType) {
            FundType::DEALER_RETAINED => config('accounting.nominal_codes.warranty.self_funded_provision_retained.code'),
            default => null,
        };

        $breakdownPlanAccountCode = match ($fundType) {
            FundType::MANAGED => config('accounting.nominal_codes.breakdown.managed_fund_revenue.code'),
            FundType::DEALER, FundType::DEALER_RETAINED => config('accounting.nominal_codes.breakdown.self_funded_revenue.code'),
        };

        $paymentTotal = $fundType === FundType::DEALER_RETAINED ? 1243.20 + 200 : 1243.20;

        $this->mock(XeroAccountingService::class, function (MockInterface $mock) use ($warrantyAccountCode, $breakdownPlanAccountCode, $warrantyProvisionAccountCode) {
            $mock->shouldReceive('createInvoice')->once()
                ->withArgs(function (InvoiceData $invoiceData) use ($warrantyAccountCode, $warrantyProvisionAccountCode, $breakdownPlanAccountCode) {
                    if ($invoiceData->status !== 'AUTHORISED') {
                        return false;
                    }
                    if ($invoiceData->date !== now()->startOfMonth()->subDay()->toDateString()) {
                        return false;
                    }
                    if ($invoiceData->dueDate !== now()->startOfMonth()->toDateString()) {
                        return false;
                    }
                    $warrantyLineItem = $invoiceData->lineItems->firstWhere('accountCode', $warrantyAccountCode);
                    if ($warrantyLineItem->quantity != 1 || $warrantyLineItem->unitAmount != 20 || $warrantyLineItem->taxAmount != 4) {
                        return false;
                    }
                    if ($warrantyProvisionAccountCode) {
                        $warrantyProvisionLineItem = $invoiceData->lineItems->firstWhere('accountCode', $warrantyProvisionAccountCode);
                        if ($warrantyProvisionLineItem->quantity != 1 || $warrantyProvisionLineItem->unitAmount != 100 || $warrantyProvisionLineItem->taxAmount != 0) {
                            return false;
                        }
                    }
                    $breakdownPlanLineItem = $invoiceData->lineItems->firstWhere('accountCode', $breakdownPlanAccountCode);
                    if ($breakdownPlanLineItem->quantity != 1 || $breakdownPlanLineItem->unitAmount != 99 || $breakdownPlanLineItem->taxAmount != 19.80) {
                        return false;
                    }
                    $servicePlanLineItem = $invoiceData->lineItems->firstWhere('accountCode', config('accounting.nominal_codes.service_plan.admin_fees.code'));
                    if ($servicePlanLineItem->quantity != 1 || $servicePlanLineItem->unitAmount != 399 || $servicePlanLineItem->taxAmount != 79.80) {
                        return false;
                    }

                    return true;
                })
                ->andReturnUsing(function (InvoiceData $invoiceData) {
                    $invoiceData->id = 'SOME-XERO-INVOICE-ID';
                    $invoiceData->invoiceNumber = 'INV-1234';

                    return $invoiceData;
                });
            $mock->shouldReceive('emailInvoice')->once()->withArgs(['SOME-XERO-INVOICE-ID'])->andReturn(true);
        });

        $this->mock(DirectDebitPaymentProcessor::class, function (MockInterface $mock) use ($paymentTotal) {
            $mock->shouldReceive('createPayment')
                ->once()
                ->withArgs(function (Payment $payment) use ($paymentTotal) {
                    expect($payment)
                        ->provider->toBe(\App\Enums\PaymentProvider::ACCESS_PAYSUITE)
                        ->period_start->toBeNull()
                        ->amount->toEqual($paymentTotal)
                        ->charge_date->not->toBeNull()
                        ->payable->accounting_software_id->toBe('SOME-XERO-INVOICE-ID');

                    return true;
                })
                ->andReturnUsing(function (Payment $payment) {
                    return new PaymentData(
                        provider: \App\Enums\PaymentProvider::ACCESS_PAYSUITE,
                        providerPaymentId: 'SOME-PAYMENT-PROCESSOR-ID',
                        chargeDate: today()->addWeeks(2),
                        status: Payment::STATUS_PENDING_SUBMISSION,
                        amount: $payment->amount,
                    );
                });
        });

        $account = Account::factory()->create();

        $sales = Sale::factory()
            ->times(2)
            ->confirmed($account)
            ->for(Dealership::withoutEvents(fn () => Dealership::factory()
                ->for(\App\Models\BillingRequest::factory())
                ->recycle($account)
                ->create())
            )
            ->hasWarranty([
                'provision' => 100,
                'admin_fee' => 20,
                'vat' => 4,
                'selling_price' => 200,
                'fund_type' => $fundType,
            ])
            ->hasBreakdownPlan([
                'provision' => 119,
                'admin_fee' => 99,
                'vat' => 19.80,
                'selling_price' => 299,
                'fund_type' => $fundType,
            ])
            ->hasServicePlan([
                'admin_fee' => 399,
                'vat' => 79.80,
                'selling_price' => 599,
            ])
            ->create();

        Carbon::setTestNow($currentNow);

        Artisan::call(GenerateMonthlyDealershipInvoicesCommand::class);

        $invoice = Invoice::sole();

        expect($invoice)
            ->account_id->toBe($sales->first()->account_id)
            ->invoiceable_type->toBe('dealership')
            ->invoice_number->toBe('INV-1234')
            ->accounting_software_id->toBe('SOME-XERO-INVOICE-ID')
            ->date->toEqual(now()->startOfMonth()->subDay())
            ->due_date->toEqual(now()->startOfMonth())
            ->emailed_at->toEqual(now()->toDateTimeString())
            ->getTotal()->toEqual($paymentTotal)

            ->and($invoice->lineItems->firstWhere('type', 'warranty'))
            ->quantity->toBe(1)
            ->account_code->toBe($warrantyAccountCode)
            ->unit_amount->toEqual(20)
            ->tax->toEqual(4)
            ->and($invoice->lineItems->firstWhere('type', 'breakdown'))
            ->quantity->toBe(1)
            ->account_code->toBe($breakdownPlanAccountCode)
            ->unit_amount->toEqual(99)
            ->tax->toEqual(19.80)
            ->and($invoice->lineItems->firstWhere('type', 'service_plan'))
            ->quantity->toBe(1)
            ->account_code->toBe(config('accounting.nominal_codes.service_plan.admin_fees.code'))
            ->unit_amount->toEqual(399)
            ->tax->toEqual(79.80);

        expect(Payment::count())->toBe(1)
            ->and(Payment::latest()->first())
            ->provider->toBe(\App\Enums\PaymentProvider::ACCESS_PAYSUITE)
            ->amount->toEqual($paymentTotal)
            ->payable_id->toBe(Invoice::latest()->first()->id)
            ->payable_type->toBe(\Illuminate\Database\Eloquent\Relations\Relation::getMorphAlias(Invoice::class))
            ->processor_payment_id->toBe('SOME-PAYMENT-PROCESSOR-ID')
            ->charge_date->eq(today()->addWeeks(2))->toBeTrue()
            ->status->toBe(Payment::STATUS_PENDING_SUBMISSION);
    })
        ->with([
            'managed fund' => FundType::MANAGED,
            'dealer funded' => FundType::DEALER,
            'dealer funded (retained)' => FundType::DEALER_RETAINED,
        ])
        ->with([
            'sales at start of valid period' => now()->startOfMonth()->subMonth(),
            'sales at end of valid period' => now()->startOfMonth()->subMonth()->endOfMonth(),
        ]);

    test('running the command twice does not create duplicate invoices', function () {
        Carbon::setTestNow(now()->startOfMonth()->subMonth());

        $this->mock(XeroAccountingService::class, function (MockInterface $mock) {
            $mock->shouldReceive('createInvoice')->once()
                ->andReturnUsing(function (InvoiceData $invoiceData) {
                    $invoiceData->id = 'SOME-XERO-INVOICE-ID';
                    $invoiceData->invoiceNumber = 'INV-1234';

                    return $invoiceData;
                });
            $mock->shouldReceive('emailInvoice')->once()->andReturn(true);
        });

        $this->mock(DirectDebitPaymentProcessor::class, function (MockInterface $mock) {
            $mock->shouldReceive('createPayment')
                ->once()
                ->andReturnUsing(function (Payment $payment) {
                    return new PaymentData(
                        provider: \App\Enums\PaymentProvider::ACCESS_PAYSUITE,
                        providerPaymentId: 'SOME-PAYMENT-PROCESSOR-ID',
                        chargeDate: today()->addWeeks(2),
                        status: Payment::STATUS_PENDING_SUBMISSION,
                        amount: $payment->amount,
                    );
                });
        });

        $account = Account::factory()->create();

        Sale::factory()
            ->confirmed($account)
            ->for(Dealership::withoutEvents(fn () => Dealership::factory()
                ->for(\App\Models\BillingRequest::factory())
                ->recycle($account)
                ->create())
            )
            ->hasWarranty()
            ->hasBreakdownPlan()
            ->hasServicePlan()
            ->create();

        Carbon::setTestNow();

        Artisan::call(GenerateMonthlyDealershipInvoicesCommand::class);
        Artisan::call(GenerateMonthlyDealershipInvoicesCommand::class);

        expect(Invoice::count())->toBe(1);
        expect(Payment::count())->toBe(1);
    });

    test('monthly dealership invoice does not include sales from other periods', function () {
        Notification::fake();

        $this->mock(XeroAccountingService::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('createInvoice');
            $mock->shouldNotReceive('emailInvoice');
        });

        $this->mock(DirectDebitPaymentProcessor::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('createPayment');
        });

        $account = Account::factory()->create();

        foreach ([now()->endOfMonth()->subMonths(2), now()->startOfMonth()] as $saleDate) {
            Carbon::setTestNow($saleDate);

            Sale::factory()
                ->confirmed()
                ->recycle($account)
                ->for(Dealership::factory()->create())
                ->hasWarranty(function ($attributes, $sale) {
                    return [
                        'provision' => 100,
                        'admin_fee' => 20,
                        'vat' => 4,
                        'selling_price' => 200,
                        'fund_type' => FundType::MANAGED,
                    ];
                })
                ->hasBreakdownPlan(function ($attributes, $sale) {
                    return [
                        'provision' => 119,
                        'admin_fee' => 99,
                        'vat' => 19.80,
                        'selling_price' => 299,
                        'fund_type' => FundType::MANAGED,
                    ];
                })
                ->hasServicePlan(function ($attributes, $sale) {
                    return [
                        'admin_fee' => 399,
                        'vat' => 79.80,
                        'selling_price' => 599,
                    ];
                })
                ->create();
        }

        Carbon::setTestNow();

        Artisan::call(GenerateMonthlyDealershipInvoicesCommand::class);

        expect(Invoice::count())->toBe(0);
    });

});
