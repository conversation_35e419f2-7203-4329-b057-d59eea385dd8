<?php

use App\Console\Commands\CreateMonthlyCustomerPaymentsCommand;
use App\Models\Account;
use App\Models\BillingRequest;
use App\Models\Dealership;
use App\Models\Payment;
use App\Models\Sale;
use App\Services\Payments\DirectDebit\DirectDebitPaymentProcessor;
use App\Services\Payments\DirectDebit\PaymentData;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Artisan;
use Mockery\MockInterface;

test('monthly customer payments command creates payments for new sales in the current period', function ($startDate, $billingPeriodStart) {

    // 20th is the billing day so that we have enough time to
    // request payment for any date in the following month
    Carbon::setTestNow('2025-01-20');

    $this->mock(DirectDebitPaymentProcessor::class, function (MockInterface $mock) {
        $mock->shouldReceive('createPayment')
            ->withArgs(function (Payment $payment) {
                expect($payment)
                    ->provider->toBe(\App\Enums\PaymentProvider::ACCESS_PAYSUITE)
                    ->amount->toEqual(35.99);

                return true;
            })
            ->andReturnUsing(fn (Payment $payment) => new PaymentData(
                provider: \App\Enums\PaymentProvider::ACCESS_PAYSUITE,
                providerPaymentId: 'SOME-ACCESS-PAYSUITE-PAYMENT-ID',
                chargeDate: today()->addWeeks(2),
                status: Payment::STATUS_PENDING_SUBMISSION,
                amount: $payment->amount,
            ));
    });

    $account = Account::factory()->create();

    $sale = Sale::factory()
        ->subscription()
        ->recycle($account)
        ->for(Dealership::withoutEvents(fn () => Dealership::factory()->recycle($account)))
        ->create(fn () => [
            'start_date' => $startDate,
            'billing_request_id' => \App\Models\BillingRequest::factory(),
        ]);

    Artisan::call(CreateMonthlyCustomerPaymentsCommand::class);

    expect(Payment::count())->toBe($billingPeriodStart ? 1 : 0);

    if ($billingPeriodStart) {
        expect($sale->payments()->first())
            ->toBeInstanceOf(Payment::class)
            ->period_start->toDateString()->toBe($billingPeriodStart)
            ->processor_payment_id->toBe('SOME-ACCESS-PAYSUITE-PAYMENT-ID')
            ->amount->toEqual(35.99)
            ->status->toBe('pending_submission');
    }
})->with([
    'sale from earlier period' => ['2024-12-01', '2025-02-01'],
    'sale from current period' => ['2025-01-28', '2025-02-28'],
    'sale from later period' => ['2025-02-01', null],
    'sale from end of month to test shorter month billing period' => ['2025-01-31', '2025-02-28'],
]);

test('monthly payments are taken on the last day of the month if the month in question is shorter than the month of sale', function () {

    Carbon::setTestNow('2024-01-31');

    $account = Account::factory()->create();

    $sale = Sale::factory()
        ->subscription()
        ->recycle($account)
        ->recycle(BillingRequest::factory()->create())
        ->for(Dealership::withoutEvents(fn () => Dealership::factory()->recycle($account)))
        ->has(\App\Models\Warranty::factory()->subscription())
        ->create();

    $this->mock(DirectDebitPaymentProcessor::class, function (MockInterface $mock) {
        $mock->shouldReceive('createPayment')
            ->withArgs(function (Payment $payment) {
                expect($payment)
                    ->provider->toBe(\App\Enums\PaymentProvider::ACCESS_PAYSUITE)
                    ->amount->toEqual(35.99);

                return true;
            })
            ->andReturnUsing(fn (Payment $payment) => new PaymentData(
                provider: \App\Enums\PaymentProvider::ACCESS_PAYSUITE,
                providerPaymentId: 'SOME-ACCESS-PAYSUITE-PAYMENT-ID',
                chargeDate: today()->addWeeks(2),
                status: Payment::STATUS_PENDING_SUBMISSION,
                amount: $payment->amount,
            ));
    });

    Carbon::setTestNow('2024-02-20');

    for ($i = 0; $i < 12; $i++) {
        Artisan::call(CreateMonthlyCustomerPaymentsCommand::class);

        // add month
        Carbon::setTestNow(Carbon::now()->addMonth());
    }

    expect(Payment::count())->toBe(12)
        ->and(Payment::all()->pluck('period_start')->map(fn ($date) => $date->format('Y-m-d'))->toArray())->toEqual([
            '2024-03-31',
            '2024-04-30',
            '2024-05-31',
            '2024-06-30',
            '2024-07-31',
            '2024-08-31',
            '2024-09-30',
            '2024-10-31',
            '2024-11-30',
            '2024-12-31',
            '2025-01-31',
            '2025-02-28',
        ]);

    Carbon::setTestNow();
});
