<?php

use App\Enums\PayLaterAgreementStatus;
use App\Models\Account;
use App\Services\Payments\PayLater\ApplicationStatusData;
use App\Services\Payments\PayLater\PaymentAssistProcessor;
use Mo<PERSON>y\MockInterface;

use function Pest\Laravel\mock;

function getPaymentAssistPayload($filename)
{
    return json_decode(
        file_get_contents(base_path("tests/data/webhooks/payment_assist/{$filename}")),
        true
    );
}

test('application status update webhook', function () {
    $payLaterAgreement = \App\Models\PayLaterAgreement::factory()->create([
        'token' => 'b1e45648-f369-11ea-8ed1-2cfda158243b',
    ]);

    mock(PaymentAssistProcessor::class, function (MockInterface $mock) use ($payLaterAgreement) {
        $mock->shouldReceive('forAccount')
            ->once()
            ->withArgs(fn (Account $account) => $account->is($payLaterAgreement->payable->account))
            ->andReturn($mock);

        $mock->shouldReceive('getApplicationStatus')
            ->once()
            ->withArgs([$payLaterAgreement->token])
            ->andReturnUsing(fn (string $token) => new ApplicationStatusData(
                token: $token,
                providerReference: '<SOME-REFERENCE>',
                status: 'failed',
                planId: '<SOME-PLAN-ID>',
                requiresInvoice: false,
                hasInvoice: false,
                lastAccessedAt: now(),
            ));
    });

    $this
        ->postJson(route('webhooks.payment-assist'), getPaymentAssistPayload('application_failed.json'))
        ->assertOk();

    expect($payLaterAgreement->refresh())
        ->status->toBe(PayLaterAgreementStatus::FAILED)
        ->provider_reference->toBe('<SOME-REFERENCE>');
});
