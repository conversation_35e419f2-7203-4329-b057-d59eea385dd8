<?php

use App\Models\BillingRequest;
use Illuminate\Support\Facades\Queue;

function getAccessPaysuitePayload($filename)
{
    return json_decode(
        file_get_contents(base_path("tests/data/webhooks/access_paysuite/{$filename}")),
        true
    );
}

test('contract cancelled webhook', function () {
    $billingRequest = BillingRequest::factory()->create([
        'provider' => 'access_paysuite',
        'mandate_id' => 'ac190e35-2fa7-162c-8505-18702c186a43',
    ]);

    $this->postJson(route('webhooks.access-paysuite', 'contract'), getAccessPaysuitePayload('contract_cancelled.json'))
        ->assertOk();

    expect($billingRequest->refresh())
        ->status->toBe('cancelled');
});

test('payment update webhook', function () {
    Queue::fake();

    $payment = \App\Models\Payment::factory()->create([
        'provider' => 'access_paysuite',
        'processor_payment_id' => '12ae41bd-f2a2-461f-9fde-05e8599fbdf6',
    ]);

    $this->postJson(route('webhooks.access-paysuite', 'payment'), getAccessPaysuitePayload('payment_paid.json'))
        ->assertOk();

    Queue::assertPushed(fn (App\Jobs\SyncPaymentFromProcessor $job) => $job->payment->is($payment));
});
