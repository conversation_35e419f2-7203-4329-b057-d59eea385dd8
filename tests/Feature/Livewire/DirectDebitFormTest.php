<?php

use App\Livewire\DirectDebitForm;
use App\Models\BillingRequest;
use Livewire\Livewire;

it('renders successfully', function () {
    Livewire::test(DirectDebitForm::class, [
        'billingRequest' => BillingRequest::factory()->create(),
    ])
        ->assertStatus(200);
})->todo('Needs to be implemented');

todo('dealerships must have a contact last name set up to create a direct debit mandate');
