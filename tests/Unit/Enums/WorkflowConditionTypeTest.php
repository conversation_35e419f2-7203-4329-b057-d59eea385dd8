<?php

use App\Workflows\Enums\WorkflowConditionType;

test('enum has correct values', function () {
    expect(WorkflowConditionType::STANDARD_WARRANTY->value)->toBe('standard-warranty')
        ->and(WorkflowConditionType::EXTENDED_WARRANTY->value)->toBe('extended-warranty')
        ->and(WorkflowConditionType::BREAKDOWN_PLAN->value)->toBe('no-breakdown')
        ->and(WorkflowConditionType::SERVICE_PLAN->value)->toBe('no-service')
        ->and(WorkflowConditionType::START_DATE->value)->toBe('start-date-before')
        ->and(WorkflowConditionType::START_DATE_AFTER->value)->toBe('start-date-after')
        ->and(WorkflowConditionType::WARRANTY_END_DATE->value)->toBe('end-date-before')
        ->and(WorkflowConditionType::END_DATE_AFTER->value)->toBe('end-date-after');
});

test('enum has correct labels', function () {
    expect(WorkflowConditionType::STANDARD_WARRANTY->label())->toBe('Has standard warranty')
        ->and(WorkflowConditionType::EXTENDED_WARRANTY->label())->toBe('Has extended warranty')
        ->and(WorkflowConditionType::BREAKDOWN_PLAN->label())->toBe('Has no breakdown cover')
        ->and(WorkflowConditionType::SERVICE_PLAN->label())->toBe('Has no service plan')
        ->and(WorkflowConditionType::START_DATE->label())->toBe('Start date before')
        ->and(WorkflowConditionType::START_DATE_AFTER->label())->toBe('Start date after')
        ->and(WorkflowConditionType::WARRANTY_END_DATE->label())->toBe('End date before')
        ->and(WorkflowConditionType::END_DATE_AFTER->label())->toBe('End date after');
});

test('toSelectArray returns correct format', function () {
    $selectArray = WorkflowConditionType::toSelectArray();

    expect($selectArray)->toBeArray()
        ->and($selectArray['standard-warranty'])->toBe('Has standard warranty')
        ->and($selectArray['extended-warranty'])->toBe('Has extended warranty')
        ->and($selectArray['no-breakdown'])->toBe('Has no breakdown cover')
        ->and($selectArray['no-service'])->toBe('Has no service plan')
        ->and($selectArray['start-date-before'])->toBe('Start date before')
        ->and($selectArray['start-date-after'])->toBe('Start date after')
        ->and($selectArray['end-date-before'])->toBe('End date before')
        ->and($selectArray['end-date-after'])->toBe('End date after');
});

test('requiresDateValue returns correct boolean', function () {
    expect(WorkflowConditionType::START_DATE->requiresDateValue())->toBeTrue()
        ->and(WorkflowConditionType::START_DATE_AFTER->requiresDateValue())->toBeTrue()
        ->and(WorkflowConditionType::WARRANTY_END_DATE->requiresDateValue())->toBeTrue()
        ->and(WorkflowConditionType::END_DATE_AFTER->requiresDateValue())->toBeTrue()
        ->and(WorkflowConditionType::STANDARD_WARRANTY->requiresDateValue())->toBeFalse()
        ->and(WorkflowConditionType::EXTENDED_WARRANTY->requiresDateValue())->toBeFalse()
        ->and(WorkflowConditionType::BREAKDOWN_PLAN->requiresDateValue())->toBeFalse()
        ->and(WorkflowConditionType::SERVICE_PLAN->requiresDateValue())->toBeFalse();
});

test('isWarrantyRelated returns correct boolean', function () {
    expect(WorkflowConditionType::STANDARD_WARRANTY->isWarrantyRelated())->toBeTrue()
        ->and(WorkflowConditionType::EXTENDED_WARRANTY->isWarrantyRelated())->toBeTrue()
        ->and(WorkflowConditionType::BREAKDOWN_PLAN->isWarrantyRelated())->toBeFalse()
        ->and(WorkflowConditionType::SERVICE_PLAN->isWarrantyRelated())->toBeFalse()
        ->and(WorkflowConditionType::START_DATE->isWarrantyRelated())->toBeFalse()
        ->and(WorkflowConditionType::START_DATE_AFTER->isWarrantyRelated())->toBeFalse()
        ->and(WorkflowConditionType::WARRANTY_END_DATE->isWarrantyRelated())->toBeFalse()
        ->and(WorkflowConditionType::END_DATE_AFTER->isWarrantyRelated())->toBeFalse();
});

test('isCoverageRelated returns correct boolean', function () {
    expect(WorkflowConditionType::BREAKDOWN_PLAN->isCoverageRelated())->toBeTrue()
        ->and(WorkflowConditionType::SERVICE_PLAN->isCoverageRelated())->toBeTrue()
        ->and(WorkflowConditionType::STANDARD_WARRANTY->isCoverageRelated())->toBeFalse()
        ->and(WorkflowConditionType::EXTENDED_WARRANTY->isCoverageRelated())->toBeFalse()
        ->and(WorkflowConditionType::START_DATE->isCoverageRelated())->toBeFalse()
        ->and(WorkflowConditionType::START_DATE_AFTER->isCoverageRelated())->toBeFalse()
        ->and(WorkflowConditionType::WARRANTY_END_DATE->isCoverageRelated())->toBeFalse()
        ->and(WorkflowConditionType::END_DATE_AFTER->isCoverageRelated())->toBeFalse();
});
