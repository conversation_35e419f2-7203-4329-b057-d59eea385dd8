<?php

use App\Models\Account;
use App\Models\ManufacturerSurcharge;
use App\Models\Vehicle;
use App\Services\Surcharge\ManufacturerSurchargeRepository;
use OvonGroup\ManufacturerLogos\Models\Manufacturer;

beforeEach(function () {
    $this->surchargeRepository = new ManufacturerSurchargeRepository;
});

test('returns account specific surcharge when exists', function () {
    $account = Account::factory()->managedFund()->create();
    $manufacturer = Manufacturer::factory()->create();

    $surcharge = ManufacturerSurcharge::factory()->create([
        'account_id' => $account->id,
        'manufacturer_id' => $manufacturer->id,
        'warranty_admin_fee_percentage' => 100,
        'warranty_provision_percentage' => 75,
        'warranty_selling_price_percentage' => 50,
    ]);

    $vehicle = Vehicle::factory()->create([
        'account_id' => $account->id,
        'manufacturer_id' => $manufacturer->id,
    ]);

    $result = $this->surchargeRepository->lookupSurcharge($vehicle);

    expect($result->adminFeePercentage)->toBe(100.0);
    expect($result->provisionPercentage)->toBe(75.0);
    expect($result->sellingPricePercentage)->toBe(50.0);
    expect($result->source)->toBe('account_specific');
});

test('returns zero surcharge for account without specific surcharge', function () {
    $account = Account::factory()->managedFund()->create();
    $manufacturer = Manufacturer::factory()->create();

    $vehicle = Vehicle::factory()->create([
        'account_id' => $account->id,
        'manufacturer_id' => $manufacturer->id,
    ]);

    $result = $this->surchargeRepository->lookupSurcharge($vehicle);

    expect($result->adminFeePercentage)->toBe(0.0);
    expect($result->provisionPercentage)->toBe(0.0);
    expect($result->sellingPricePercentage)->toBe(0.0);
    expect($result->source)->toBe('none');
});

test('returns zero surcharge when sale has no manufacturer', function () {
    $account = Account::factory()->managedFund()->create();

    $vehicle = Vehicle::factory()->create([
        'account_id' => $account->id,
        'manufacturer_id' => null,
    ]);

    $result = $this->surchargeRepository->lookupSurcharge($vehicle);

    expect($result->adminFeePercentage)->toBe(0.0);
    expect($result->provisionPercentage)->toBe(0.0);
    expect($result->sellingPricePercentage)->toBe(0.0);
    expect($result->source)->toBe('none');
});

test('calculate warranty surcharges', function () {
    $account = Account::factory()->managedFund()->create();
    $manufacturer = Manufacturer::factory()->create();

    ManufacturerSurcharge::factory()->create([
        'account_id' => $account->id,
        'manufacturer_id' => $manufacturer->id,
        'warranty_admin_fee_percentage' => 100, // 100% surcharge
        'warranty_provision_percentage' => 50,  // 50% surcharge
        'warranty_selling_price_percentage' => 25, // 25% surcharge
    ]);

    $vehicle = Vehicle::factory()->create([
        'account_id' => $account->id,
        'manufacturer_id' => $manufacturer->id,
    ]);

    expect($this->surchargeRepository->lookupSurcharge($vehicle))
        ->addAdminFeeSurcharge(20)->toBe(40.0)
        ->addProvisionSurcharge(100)->toBe(150.0)
        ->addSellingPriceSurcharge(200)->toBe(250.0);
});
