<?php

use App\Models\ManufacturerSurcharge;
use App\Services\Surcharge\SurchargeResult;

test('calculates admin fee surcharge correctly', function () {
    $result = new SurchargeResult(50, 0, 0, 'test');

    expect($result->addAdminFeeSurcharge(50))->toBe(75.0);
    expect($result->addAdminFeeSurcharge(20))->toBe(30.0);
    expect($result->addAdminFeeSurcharge(0))->toBe(0.0);
});

test('calculates provision surcharge correctly', function () {
    $result = new SurchargeResult(0, 75, 0, 'test');

    expect($result->addProvisionSurcharge(100))->toBe(175.0);
    expect($result->addProvisionSurcharge(20))->toBe(35.0);
    expect($result->addProvisionSurcharge(0))->toBe(0.0);
});

test('calculates selling price surcharge correctly', function () {
    $result = new SurchargeResult(0, 0, 25, 'test');

    expect($result->addSellingPriceSurcharge(200))->toBe(250.0);
    expect($result->addSellingPriceSurcharge(20))->toBe(25.0);
    expect($result->addSellingPriceSurcharge(0))->toBe(0.0);
});

test('rounds calculations to two decimal places', function () {
    $result = new SurchargeResult(33.33, 0, 0, 'test');

    expect($result->addAdminFeeSurcharge(100))->toBe(133.33);
    expect($result->addAdminFeeSurcharge(10))->toBe(13.33);
});

test('has any surcharge returns true when any percentage is positive', function () {
    expect((new SurchargeResult(10, 0, 0, 'test'))->hasAnySurcharge())->toBeTrue();
    expect((new SurchargeResult(0, 10, 0, 'test'))->hasAnySurcharge())->toBeTrue();
    expect((new SurchargeResult(0, 0, 10, 'test'))->hasAnySurcharge())->toBeTrue();
    expect((new SurchargeResult(5, 5, 5, 'test'))->hasAnySurcharge())->toBeTrue();
});

test('has any surcharge returns false when all percentages are zero', function () {
    expect((new SurchargeResult(0, 0, 0, 'test'))->hasAnySurcharge())->toBeFalse();
});

test('zero factory method', function () {
    $result = SurchargeResult::zero();

    expect($result->adminFeePercentage)->toBe(0.0);
    expect($result->provisionPercentage)->toBe(0.0);
    expect($result->sellingPricePercentage)->toBe(0.0);
    expect($result->source)->toBe('none');
    expect($result->hasAnySurcharge())->toBeFalse();
});

test('from manufacturer surcharge factory method', function () {
    $surcharge = new ManufacturerSurcharge([
        'warranty_admin_fee_percentage' => 100,
        'warranty_provision_percentage' => 75,
        'warranty_selling_price_percentage' => 50,
    ]);

    $result = SurchargeResult::fromManufacturerSurcharge($surcharge);

    expect($result->adminFeePercentage)->toBe(100.0);
    expect($result->provisionPercentage)->toBe(75.0);
    expect($result->sellingPricePercentage)->toBe(50.0);
    expect($result->source)->toBe('account_specific');
    expect($result->hasAnySurcharge())->toBeTrue();
});

test('from manufacturer surcharge handles null values', function () {
    $surcharge = new ManufacturerSurcharge([
        'warranty_admin_fee_percentage' => null,
        'warranty_provision_percentage' => 75,
        'warranty_selling_price_percentage' => null,
    ]);

    $result = SurchargeResult::fromManufacturerSurcharge($surcharge);

    expect($result->adminFeePercentage)->toBe(0.0);
    expect($result->provisionPercentage)->toBe(75.0);
    expect($result->sellingPricePercentage)->toBe(0.0);
    expect($result->source)->toBe('account_specific');
    expect($result->hasAnySurcharge())->toBeTrue();
});
