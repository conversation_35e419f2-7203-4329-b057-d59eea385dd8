APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

CUSTOMER_PORTAL_DOMAIN=myaccount.protegoautocare.test
DEALER_PORTAL_DOMAIN=dealer.protegoautocare.test
PAYMENT_DOMAIN=payment.protegoautocare.test
REPAIRER_PORTAL_DOMAIN=repairer.protegoautocare.test

APP_TIMEZONE=UTC
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database
BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single

DB_CONNECTION=sqlite

BROADCAST_CONNECTION=log
CACHE_STORE=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

DVLA_VES_KEY=

MOTORSPECS_ENVIRONMENT=
MOTORSPECS_CLIENT_ID=
MOTORSPECS_CLIENT_SECRET=

BREAKDOWN_POLICY_REPORTS_EMAIL_ADDRESS=<EMAIL>

IDEAL_POSTCODES_API_KEY=

#BREAKDOWN_POLICY_REPORTS_EMAIL_ADDRESS=<EMAIL>,<EMAIL>
XERO_CLIENT_ID=
XERO_SECRET=
XERO_GO_CARDLESS_BANK_ACCOUNT_ID=
XERO_GO_CARDLESS_FEE_ACCOUNT_CODE=
XERO_GO_CARDLESS_CONTACT_ID=
XERO_ACCESS_PAYSUITE_BANK_ACCOUNT_ID=
XERO_BANK_ACCOUNT_ID=
XERO_SUBSCRIPTION_CONTACT_ID=
XERO_WEBHOOK_KEY=

DEFAULT_PAYMENT_PROVIDER=go_cardless|access_paysuite

GO_CARDLESS_ACCESS_TOKEN=
GO_CARDLESS_ENVIRONMENT=

ACCESS_PAYSUITE_ENVIRONMENT=playpen
ACCESS_PAYSUITE_CLIENT_CODE=
ACCESS_PAYSUITE_API_KEY=

BUGSNAG_API_KEY=

DVSA_MOT_HISTORY_KEY=
DVSA_MOT_HISTORY_TENANT_ID=
DVSA_MOT_HISTORY_CLIENT_ID=
DVSA_MOT_HISTORY_SECRET=

OPENAI_API_KEY=
OPENAI_ORGANIZATION=
OPENAI_PROJECT=

ANTHROPIC_API_KEY=

GOOGLE_PLACES_API_KEY=

VOIP_3CX_ENDPOINT=
VOIP_3CX_CLIENT_ID=
VOIP_3CX_API_KEY=
