<?php

use App\Console\Commands\CreateMonthlyCustomerPaymentsCommand;
use App\Console\Commands\GenerateMonthlyDealershipInvoicesCommand;
use App\Console\Commands\PullVoipCallLogs;
use App\Console\Commands\RunWorkflows;
use App\Console\Commands\SendCustomerPaymentMethodSetupReminderNotifications;
use App\Console\Commands\SendDailyBreakdownPlanSalesReport;
use App\Console\Commands\SendDealerPaymentMethodSetupReminderNotifications;
use App\Console\Commands\SendPaymentUpcomingPaymentNotifications;
use App\Console\Commands\SyncDuePaymentStatusesCommand;
use App\Console\Commands\SyncPayLaterAgreementStatusesCommand;
use App\Jobs\TranscribePhoneCall;
use App\Services\Voip\Voip3cxProvider;
use Illuminate\Support\Facades\Schedule;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Schedule::command('model:prune')->daily();

// Production only scheduled commands
Schedule::when(fn () => app()->isProduction())
    ->timezone('Europe/London')
    ->group(function () {
        Schedule::command(SendDailyBreakdownPlanSalesReport::class)->dailyAt('00:30');
        Schedule::command(GenerateMonthlyDealershipInvoicesCommand::class)->monthlyOn(1, '08:00');
        Schedule::command(CreateMonthlyCustomerPaymentsCommand::class)->monthlyOn(20);
        Schedule::command(SendPaymentUpcomingPaymentNotifications::class)->dailyAt('10:05');
        Schedule::command(SendPaymentUpcomingPaymentNotifications::class)->dailyAt('11:05');
        Schedule::command(PullVoipCallLogs::class)->everyFiveMinutes();
        Schedule::command(SyncPayLaterAgreementStatusesCommand::class)->everyThirtyMinutes();
        Schedule::command(SyncDuePaymentStatusesCommand::class)->dailyAt('08:00');
        Schedule::command(SendDealerPaymentMethodSetupReminderNotifications::class)->dailyAt('08:50');
        Schedule::command(SendCustomerPaymentMethodSetupReminderNotifications::class)->dailyAt('09:00');
        Schedule::command(RunWorkflows::class)->dailyAt('02:00');
    });

Artisan::command('transcribe', function (Voip3cxProvider $voip) {

    dd(
        $voip->recordingUrl(\App\Models\PhoneCall::find(101)->recording_id)
    );

    TranscribePhoneCall::dispatch(\App\Models\PhoneCall::find(21958));
    TranscribePhoneCall::dispatch(\App\Models\PhoneCall::find(21958));
    //    $transcribeAudio->execute(\App\Models\PhoneCall::find(21958));
    //    $transcribeAudio->execute(\App\Models\PhoneCall::find(22192));
});
