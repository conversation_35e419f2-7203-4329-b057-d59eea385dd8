<?php

use App\Filament\Pages\UserNewAccountSetupPage;
use App\Http\Controllers\AccessPaysuiteWebhookController;
use App\Http\Controllers\AmazonSNSEmailReceivedWebhookController;
use App\Http\Controllers\BreakdownPlanReportsController;
use App\Http\Controllers\ClaimReportsController;
use App\Http\Controllers\CustomerImpersonateController;
use App\Http\Controllers\DocumentsController;
use App\Http\Controllers\DownloadBrowserPluginController;
use App\Http\Controllers\GoCardlessWebhookController;
use App\Http\Controllers\PaymentAssistWebhookController;
use App\Http\Controllers\WarrantyReportsController;
use App\Http\Controllers\XeroWebhookController;
use App\Http\Middleware\ValidateXeroWebhook;
use Illuminate\Support\Facades\Route;

Route::get('user-invite/{token}', UserNewAccountSetupPage::class)
    ->middleware('guest')
    ->name('password.invite');

Route::domain(config('app.dealer_portal_domain'))->middleware('auth')->group(function () {
    // Reports
    Route::get('reports/warranties', WarrantyReportsController::class)->name('reports.warranties');
    Route::get('reports/claims', ClaimReportsController::class)->name('reports.claims');
    Route::get('reports/breakdown-plans', BreakdownPlanReportsController::class)->name('reports.breakdown-plans');

    // Documents
    Route::get('sales/{sale}/document.pdf', DocumentsController::class)->name('documents.pdf');

    // Redirect to the customer portal
    Route::get('customers/{customer}/impersonate', CustomerImpersonateController::class)->name('customers.impersonate');
});

Route::domain(config('app.payment_domain'))
    ->name('payment.')
    ->group(function () {
        Route::get('direct-debit-setup/{billingRequest:provider_billing_request_id}', \App\Livewire\DirectDebitForm::class)->name('direct-debit-setup');
    });

// Customer Portal
Route::domain(config('app.customer_portal_domain'))
    ->name('customer-portal.')
    ->group(function () {
        Route::get('magic-link/{token}', \App\Http\Controllers\CustomerPortal\MagicLinkController::class)->name('magic-link');

        Route::middleware('guest:customer')->group(function () {
            Route::get('login', \App\Livewire\CustomerPortal\Login::class)->name('login');
        });

        Route::middleware('auth:customer')->group(function () {
            Route::get('logout', \App\Http\Controllers\CustomerPortal\LogoutController::class)->name('logout');
            Route::get('/', \App\Http\Controllers\CustomerPortal\WelcomeController::class)->name('home');
            Route::get('my-offers', \App\Livewire\CustomerPortal\ListOffers::class)->name('offers');
            Route::get('my-offers/{salesOffer}', \App\Livewire\CustomerPortal\ViewOffer::class)->name('offers.view');
            Route::get('documents', \App\Http\Controllers\CustomerPortal\DocumentsController::class)->name('documents');
            Route::get('documents/{sale}.pdf', DocumentsController::class)->name('documents.download');
            Route::view('update-details', 'customer-portal.update-details')->name('update-details');
            Route::get('claims', \App\Http\Controllers\CustomerPortal\ClaimsController::class)->name('claims');
            Route::get('claims/new', \App\Livewire\CustomerPortal\CreateClaim::class)->name('claims.create');
            Route::get('claims/{claim:reference}', \App\Livewire\CustomerPortal\ViewClaim::class)->name('claims.show');
            Route::get('payments', \App\Livewire\CustomerPortal\ViewPayments::class)->name('payments');
        });
    });

Route::domain(config('app.repairer_portal_domain'))
    ->name('repairer-portal.')
    ->group(function () {
        Route::get('claims/{claim:reference}', \App\Livewire\RepairerPortal\CreateEstimate::class)->name('claims.show');
    });

Route::domain(config('app.dealer_portal_domain'))->group(function () {
    // Webhooks
    Route::post('webhooks/xero', XeroWebhookController::class)->middleware(ValidateXeroWebhook::class);
    Route::post('webhooks/go-cardless', GoCardlessWebhookController::class)->name('webhooks.go-cardless');
    Route::post('webhooks/access-paysuite/{entity}', AccessPaysuiteWebhookController::class)->name('webhooks.access-paysuite');
    Route::post('webhooks/payment-assist', PaymentAssistWebhookController::class)->name('webhooks.payment-assist');
    Route::post('webhooks/aws-ses', AmazonSNSEmailReceivedWebhookController::class);

    // Browser Plugin routes
    Route::middleware(['auth', 'role:Admin'])->group(function () {
        Route::get('/browser-plugin/download', DownloadBrowserPluginController::class)->name('browser-plugin.download');
    });
});
