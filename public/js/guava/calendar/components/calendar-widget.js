function R({view:E="dayGridMonth",locale:h="en",firstDay:p=1,events:q=[],eventContent:l=null,resourceLabelContent:s=null,selectable:L=!1,eventClickEnabled:y=!1,eventDragEnabled:i=!1,eventResizeEnabled:c=!1,noEventsClickEnabled:S=!1,dateClickEnabled:f=!1,dateSelectEnabled:u=!1,datesSetEnabled:m=!1,viewDidMountEnabled:C=!1,eventAllUpdatedEnabled:D=!1,dayMaxEvents:$=!1,moreLinkContent:v=null,resources:x=[],hasDateClickContextMenu:k=!1,hasDateSelectContextMenu:g=!1,hasEventClickContextMenu:b=!1,hasNoEventsClickContextMenu:j=!1,options:M={},dayHeaderFormat:o=null,slotLabelFormat:w=null}){return{calendarEl:null,init:async function(){this.calendarEl=this.$el;let n=this,t={view:E,resources:x,eventSources:[{events:(e,r,a)=>this.$wire.getEventsJs(e)}],locale:h,firstDay:p,dayMaxEvents:$,selectable:u,eventStartEditable:i,eventDurationEditable:c};o&&(t.dayHeaderFormat=o),w&&(t.slotLabelFormat=w),f&&(t.dateClick=e=>{k?n.$el.querySelector("[calendar-context-menu]").dispatchEvent(new CustomEvent("calendar--open-menu",{detail:{mountData:{date:e.date,dateStr:e.dateStr,allDay:e.allDay,view:e.view,resource:e.resource},jsEvent:e.jsEvent,dayEl:e.dayEl,context:"dateClick"}})):this.$wire.onDateClick({date:e.date,dateStr:e.dateStr,allDay:e.allDay,view:e.view,resource:e.resource})}),u&&(t.select=e=>{g?n.$el.querySelector("[calendar-context-menu]").dispatchEvent(new CustomEvent("calendar--open-menu",{detail:{mountData:{start:e.start,startStr:e.startStr,end:e.end,endStr:e.endStr,allDay:e.allDay,view:e.view,resource:e.resource},jsEvent:e.jsEvent,context:"dateSelect"}})):this.$wire.onDateSelect({start:e.start,startStr:e.startStr,end:e.end,endStr:e.endStr,allDay:e.allDay,view:e.view,resource:e.resource})}),m&&(t.datesSet=e=>{this.$wire.onDatesSet({start:e.start,startStr:e.startStr,end:e.end,endStr:e.endStr,view:e.view})}),l!==null&&(t.eventContent=e=>{let r=n.getEventContent(e);if(r!==void 0)return{html:r}}),v!==null&&(t.moreLinkContent=e=>({html:n.getMoreLinkContent(e)})),s!==null&&(t.resourceLabelContent=e=>{let r=n.getResourceLabelContent(e);if(r!==void 0)return{html:r}}),y&&(t.eventClick=e=>{if(e.event.extendedProps.url){let r=e.event.extendedProps.url_target??"_blank";window.open(e.event.extendedProps.url,r)}else b?n.$el.querySelector("[calendar-context-menu]").dispatchEvent(new CustomEvent("calendar--open-menu",{detail:{mountData:{event:e.event,view:e.view},jsEvent:e.jsEvent,context:"eventClick"}})):this.$wire.onEventClick({event:e.event,view:e.view})}),S&&(t.noEventsClick=e=>{j?n.$el.querySelector("[calendar-context-menu]").dispatchEvent(new CustomEvent("calendar--open-menu",{detail:{mountData:{view:e.view},jsEvent:e.jsEvent,context:"noEventsClick"}})):this.$wire.onNoEventsClick({view:e.view})}),t.eventResize=async e=>{let r=e.event.durationEditable,a=c;r!==void 0&&(a=r),a&&await this.$wire.onEventResize({event:e.event,oldEvent:e.oldEvent,endDelta:e.endDelta,view:e.view}).then(d=>{d===!1&&e.revert()})},t.eventDrop=async e=>{let r=e.event.startEditable,a=i;r!==void 0&&(a=r),a&&await this.$wire.onEventDrop({event:e.event,oldEvent:e.oldEvent,oldResource:e.oldResource,newResource:e.newResource,delta:e.delta,view:e.view}).then(d=>{d===!1&&e.revert()})},C&&(t.viewDidMount=e=>{this.$wire.onViewDidMount({view:e})}),D&&(t.eventAllUpdated=e=>{this.$wire.onEventAllUpdated({info:e})}),this.ec=EventCalendar.create(this.$el.querySelector("div"),{...t,...M}),window.addEventListener("calendar--refresh",()=>{this.ec.refetchEvents()}),this.$wire.on("calendar--set",e=>{this.ec.setOption(e.key,e.value)})},getEventContent:function(n){if(typeof l=="string")return this.wrapContent(l,n);if(typeof l=="object"){let t=n.event.extendedProps.model,e=l[t];return e===void 0?void 0:this.wrapContent(e,n)}},getResourceLabelContent:function(n){if(typeof s=="string")return this.wrapContent(s,n);if(typeof s=="object"){let t=n.event.extendedProps.model,e=s[t];return e===void 0?void 0:this.wrapContent(e,n)}},getMoreLinkContent:function(n){return this.wrapContent(v,n)},wrapContent:function(n,t){let e=document.createElement("div");return e.innerHTML=n,e.setAttribute("x-data",JSON.stringify(t)),e.classList.add("w-full"),e.outerHTML}}}export{R as default};
