<?php

// Reference: http://cs.sensiolabs.org/

return PhpCsFixer\Config::create()
    ->setUsingCache(false)
    ->setRiskyAllowed(true)
    ->setRules([
        '@PHP70Migration' => true,
        '@PHP71Migration' => true,
        '@PSR2' => true,
        '@Symfony' => true,
        'array_syntax' => ['syntax' => 'short'],
        'increment_style' => ['style' => 'post'],
        'no_multiline_whitespace_before_semicolons' => true,
        'not_operator_with_successor_space' => true,
        'ordered_imports' => ['sortAlgorithm' => 'alpha'],
        'php_unit_method_casing' => ['case' => 'snake_case'],
        'semicolon_after_instruction' => false,
        'single_line_throw' => false,
        'strict_comparison' => true,
        'yoda_style' => false,
    ]);
