name: <PERSON><PERSON> Pint

on:
    push:
        branches:
            - master

jobs:
    laravel-pint:
        runs-on: ubuntu-latest

        steps:
            -   name: Checkout Code
                uses: actions/checkout@v4.1.7
                with:
                    ref: ${{ github.head_ref }}

            -   name: Setup
                uses: ./.github/actions/setup
                with:
                    php-version: '8.4'
                    php-extensions: 'json, dom, curl, libxml, mbstring'
                    flux-pro-email: ${{ secrets.FLUX_PRO_EMAIL }}
                    flux-pro-password: ${{ secrets.FLUX_PRO_PASSWORD }}

            -   name: Install Laravel Pint
                run: composer global require laravel/pint

            -   name: Run Pint
                run: pint

            -   name: Push Changes Back to Repo
                run: |
                    git config user.name "gh-actions"
                    git config user.email "<EMAIL>"
                    git add .
                    git commit -m "Laravel Pint" || exit 0
                    git push
