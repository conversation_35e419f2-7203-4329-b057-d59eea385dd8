name: Run Tests

on:
    pull_request:
        branches:
            - develop
            - master

jobs:
    tests:
        runs-on: ubuntu-latest

        steps:
            -   name: Checkout Code
                uses: actions/checkout@v4.1.7

            -   name: Setup
                uses: ./.github/actions/setup
                with:
                    php-version: '8.4'
                    flux-pro-email: ${{ secrets.FLUX_PRO_EMAIL }}
                    flux-pro-password: ${{ secrets.FLUX_PRO_PASSWORD }}
                    coverage: xdebug

            -   name: Install Project Dependencies
                run: composer install -q --no-interaction --no-progress

            -   name: Copy Environment File
                run: cp .env.example .env

            -   name: Generate Application Key
                run: php artisan key:generate

            -   name: Run Tests
                run: php artisan test --compact --coverage --parallel
