name: Setup

description: Sets up PHP and Composer

inputs:
    php-version:
        description: The PHP version(s) you wish to use.
        required: true
    php-extensions:
        description: The PHP extensions you want to install.
        required: false
        default: dom, curl, libxml, mbstring, zip, pcntl, pdo, gd, redis, igbinary, msgpack, zstd, lz4, memcached, gmp, :php-psr
    flux-pro-email:
        description: The email used to authenticate Flux Pro.
        required: true
    flux-pro-password:
        description: The password used to authenticate Flux Pro.
        required: true
    coverage:
        description: The coverage driver to use.
        required: false
        default: none

runs:
    using: composite
    steps:
        - name: Get PHP extension cache hash
          id: get-cache-hash
          run: echo hash=$(echo "$PHP_EXTENSIONS" | md5sum | awk '{print $1}') >> $GITHUB_OUTPUT
          shell: bash
          env:
              PHP_EXTENSIONS: ${{ inputs.php-extensions }}

        - name: Setup cache environment
          id: extcache
          uses: shivammathur/cache-extensions@v1
          if: inputs.php-extensions == true
          with:
              php-version: ${{ inputs.php-version }}
              extensions: ${{ inputs.php-extensions }}
              key: php-extensions-${{ steps.get-cache-hash.outputs.hash }}

        - name: Cache extensions
          uses: actions/cache@v4.2.0
          if: inputs.php-extensions == true
          with:
              path: ${{ steps.extcache.outputs.dir }}
              key: ${{ steps.extcache.outputs.key }}
              restore-keys: ${{ steps.extcache.outputs.key }}

        - name: Setup PHP and Composer
          uses: shivammathur/setup-php@v2
          with:
              php-version: ${{ inputs.php-version }}
              extensions: ${{ inputs.php-extensions }}
              tools: composer:v2
              coverage: ${{ inputs.coverage }}

        - name: Get Composer cache dir
          id: composer-cache-dir
          run: echo dir=$(composer config cache-files-dir) >> $GITHUB_OUTPUT
          shell: bash

        - name: Cache dependencies
          uses: actions/cache@v4.2.0
          with:
              key: composer-cache-${{ hashFiles('**/composer.lock') }}
              path: ${{ steps.composer-cache-dir.outputs.dir }}
              restore-keys: composer-cache-

        - name: Authenticate Flux Pro
          env:
              FLUX_PRO_EMAIL: ${{ inputs.flux-pro-email }}
              FLUX_PRO_PASSWORD: ${{ inputs.flux-pro-password }}
          run: composer config http-basic.composer.fluxui.dev "$FLUX_PRO_EMAIL" "$FLUX_PRO_PASSWORD"
          shell: bash
