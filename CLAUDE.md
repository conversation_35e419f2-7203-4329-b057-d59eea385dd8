# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Laravel Commands
- `composer dev` - Start full development environment (server, queue, logs, vite)
- `php artisan serve` - Start Laravel development server
- `php artisan queue:listen --tries=1` - Start queue worker
- `php artisan pail --timeout=0` - Start log tailing
- `npm run dev` - Start Vite development server
- `npm run build` - Build production assets

### Testing
- `php artisan test` or `vendor/bin/pest` - Run full test suite using Pest
- `vendor/bin/pest tests/Unit` - Run unit tests only
- `vendor/bin/pest tests/Feature` - Run feature tests only
- `vendor/bin/pest tests/Arch` - Run architectural tests only

### Code Quality
- `vendor/bin/pint` - Format code using Laravel Pint (PSR-12)
- `vendor/bin/phpstan` - Static analysis (currently level 5)

### Specialized Commands
- `php artisan websocket:listen` - Start WebSocket listener for VoIP service (run with supervisor in production)

## Architecture Overview

### Multi-Portal Application
This is a multi-tenant automotive care management system with several distinct portals:
- **Dealer Portal** - Main admin interface for dealerships
- **Customer Portal** - Customer-facing interface for claims and payments
- **Repairer Portal** - For repair shops to submit estimates
- **Payment Portal** - Dedicated payment setup flows

Each portal operates on separate subdomains and has distinct authentication systems.

### Core Domain Models
- **Claims Management**: `Claim`, `ClaimEstimate`, `ClaimAuthorisation`, `BreakdownClaim`
- **Products**: `Warranty`, `BreakdownPlan`, `ServicePlan` with account-specific variants
- **Financial**: `Payment`, `Invoice`, `Payout` with multi-provider support
- **Customer Data**: `Customer`, `Dealership`, `Sale`, `Vehicle`
- **AI Integration**: `AiTask`, `AiRequest` for claim processing automation

### Service Layer Architecture
All business logic is organized in `/app/Services/` with clear separation:
- **AI Services**: Claim assessment and estimate parsing
- **Accounting**: Xero integration for financial operations
- **Payments**: Multi-provider payment processing (GoCardless, Access PaySuite)
- **Vehicle Lookup**: DVLA and MotorSpecs integration
- **VoIP**: 3CX integration for call management
- **Pay Later**: Payment plan processing

### Payment Processing
Multi-provider architecture supporting:
- Direct debit through GoCardless and Access PaySuite
- Pay Later agreements through Payment Assist
- Automatic fallback between providers

### Database Design
- Multi-tenant architecture with account-based data isolation
- Polymorphic relationships for flexible document and payment associations
- Separate models for different claim types (warranty vs breakdown)

### Frontend Stack
- **Filament Admin Panel** - Primary admin interface
- **Livewire Components** - Interactive customer-facing features
- **Flux UI** - Modern component library
- **Tailwind CSS** - Utility-first styling

### Key Integrations
- **Xero** - Accounting and invoicing
- **AI Services** - OpenAI for claim processing
- **VoIP (3CX)** - Call management and logging
- **Vehicle Data** - DVLA and MotorSpecs APIs
- **Payment Providers** - GoCardless, Access PaySuite, Payment Assist
- **Email Processing** - AWS SES with automated parsing

### Testing Strategy
- **Pest PHP** - Modern testing framework
- **Architectural Tests** - Enforce coding standards and architecture rules
- **Feature Tests** - End-to-end portal functionality
- **Unit Tests** - Service layer and model logic

### Code Standards
Follows strict Laravel conventions from `.cursor/rules/laravel.mdc`:
- Final classes for controllers and services
- Explicit type declarations
- Service layer for business logic
- Strict PSR-12 formatting via Laravel Pint
- Level 5 PHPStan analysis

### Key Configuration
- Multi-domain routing configuration
- Tenant-aware service bindings
- Morph map for polymorphic relationships
- Custom Carbon and String macros for UK-specific formatting

### Development Workflow
Use `composer dev` to start the full development stack including server, queue worker, log tailing, and asset compilation in a single command.