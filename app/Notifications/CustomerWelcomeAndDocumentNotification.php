<?php

namespace App\Notifications;

use App\Actions\GenerateSaleDocument;
use App\Models\Sale;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CustomerWelcomeAndDocumentNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(public Sale $sale) {}

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        $pdf = app(GenerateSaleDocument::class);

        return (new MailMessage)
            ->subject('Your Vehicle Warranty.')
            ->line('Thank you for giving us the opportunity to protect your vehicle. Please find your contract attached')
            ->attachData($pdf->generate($this->sale), "{$this->sale->id}.pdf");
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
