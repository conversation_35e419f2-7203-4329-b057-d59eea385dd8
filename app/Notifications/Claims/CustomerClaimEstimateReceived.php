<?php

namespace App\Notifications\Claims;

use App\Models\ClaimEstimate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CustomerClaimEstimateReceived extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public ClaimEstimate $claimEstimate) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $customer = $notifiable;
        $companyName = config('app.name');

        return (new MailMessage)
            ->subject('Your Claim: Estimate Received')
            ->greeting("Dear {$customer->full_name}")
            ->line('An estimate has been received for your claim.')
            ->line('Next Steps:')
            ->line('We\'ll review the estimates and contact you with an update.')
            ->line('If you have any questions, please contact our claims <NAME_EMAIL> or call 0330 088 3007.')
            ->line('Best regards,')
            ->line('Claims Team')
            ->salutation($companyName);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'claim_id' => $this->claimEstimate->id,
            'status' => $this->claimEstimate->status,
        ];
    }
}
