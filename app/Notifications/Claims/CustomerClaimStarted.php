<?php

namespace App\Notifications\Claims;

use App\Models\Claim;
use App\Services\MagicLinks\MagicLink;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CustomerClaimStarted extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Claim $claim) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $customer = $notifiable;
        $companyName = config('app.name');

        $message = (new MailMessage)
            ->subject($this->actionRequired() ? 'Your Claim: Action Required' : 'Your Claim')
            ->greeting("Dear {$customer->full_name}")
            ->line("Thank you for starting a claim with {$companyName}. To proceed with your claim, we need to verify some important details regarding your vehicle.")
            ->line('Please confirm the following requirements:');

        if ($this->actionRequired()) {
            $message->line('Before we can process your claim, please review and confirm the following by clicking the link below and following the instructions on screen:')
                ->action('Click here to confirm these details', $this->getCustomerPortalLink($notifiable));
        }

        return $message->line('Next Steps:')
            ->line('We\'ll review your claim information')
            ->line('A claims specialist will contact you when we have an update for you')
            ->line('If you have any questions, please contact our claims <NAME_EMAIL> or call 0330 088 3007.')
            ->line('Best regards,')
            ->line('Claims Team')
            ->salutation($companyName);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'claim_id' => $this->claim->id,
            'status' => $this->claim->status,
        ];
    }

    protected function getCustomerPortalLink($notifiable)
    {
        $magicLink = app(MagicLink::class);

        return $magicLink->encode($notifiable, route('customer-portal.claims.show', $this->claim), now()->addWeek());
    }

    protected function actionRequired(): bool
    {
        return ! $this->claim->terms_accepted_at;
    }
}
