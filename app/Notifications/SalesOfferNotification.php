<?php

namespace App\Notifications;

use App\Models\SalesOffer;
use App\Services\MagicLinks\MagicLink;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SalesOfferNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public SalesOffer $salesOffer) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(Authenticatable $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(Authenticatable $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject($this->salesOffer->offer->subject)
            ->view('email.sales_offer', ['offer' => $this->salesOffer->offer, 'href' => $this->magicLink($notifiable)]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(Authenticatable $notifiable): array
    {
        return [
            //
        ];
    }

    protected function magicLink(Authenticatable $notifiable)
    {
        return app(MagicLink::class)->encode($notifiable, route('customer-portal.offers.view', $this->salesOffer), now()->addWeek());
    }
}
