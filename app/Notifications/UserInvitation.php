<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class UserInvitation extends Notification
{
    use Queueable;

    /**
     * The password reset token.
     *
     * @var string
     */
    public $token;

    /**
     * Create a notification instance.
     *
     * @param  string  $token
     * @return void
     */
    public function __construct($token)
    {
        $this->token = $token;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        $appName = config('app.name');

        return (new MailMessage)
            ->subject(Lang::get("Welcome to {$appName}!"))
            ->line(Lang::get('You are receiving this email because you have been invited to create a password for your new account.'))
            ->action(Lang::get('Get Started'), $this->getUrl($notifiable))
            ->line(Lang::get('This password link will expire in :count days.', ['count' => config('auth.passwords.invite.expire') / 1440]));
    }

    protected function getUrl($notifiable)
    {
        return url(route('password.invite', [
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ], false));
    }
}
