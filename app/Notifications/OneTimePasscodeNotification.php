<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Twilio\TwilioChannel;
use NotificationChannels\Twilio\TwilioSmsMessage;

class OneTimePasscodeNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public string $passcode) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];

        return [TwilioChannel::class];
    }

    /**
     * Get the Twilio / SMS representation of the notification.
     */
    public function toTwilio($notifiable)
    {
        return (new TwilioSmsMessage)
            ->content('Your one-time passcode is: '.$this->passcode);
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Your One-Time Passcode')
            ->line('Your one-time passcode is: '.$this->passcode);
    }
}
