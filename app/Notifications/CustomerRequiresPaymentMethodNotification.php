<?php

namespace App\Notifications;

use App\Models\Concerns\BillableContract;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CustomerRequiresPaymentMethodNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(public BillableContract $billable, public bool $isReminder = false) {}

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        $paymentMethodLabel = $this->billable->getPaymentMethod()->getLabel();

        $message = (new MailMessage)
            ->subject("Setup Your {$paymentMethodLabel}.");

        if ($this->isReminder) {
            $message
                ->subject('Reminder: '.$message->subject)
                ->line("This is a reminder that you have not yet completed your {$paymentMethodLabel}.");
        } else {
            $message->line("Thank you for your purchase. Please complete your {$paymentMethodLabel} before collection of your vehicle to avoid any disruption to your cover..");
        }

        return $message
            ->action("Setup {$paymentMethodLabel}", $this->billable->getCompletePaymentUrl())
            ->line('Please note that your plan will not be valid until you have completed the payment setup.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
