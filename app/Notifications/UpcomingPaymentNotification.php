<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UpcomingPaymentNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public Payment $payment) {}

    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        $amount = number_format($this->payment->amount, 2);

        return (new MailMessage)
            ->subject('Upcoming Payment Notification')
            ->line("This is a reminder that a payment of £{$amount} will be taken from your account on or after {$this->payment->charge_date->formatLocal()}.")
            ->line('If you have any questions, please contact our customer service team.');
    }
}
