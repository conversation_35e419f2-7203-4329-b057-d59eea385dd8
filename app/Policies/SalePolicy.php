<?php

namespace App\Policies;

use App\Models\Sale;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SalePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('sales.view');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Sale $sale): bool
    {
        return $user->can('sales.view');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function edit(User $user, Sale $sale)
    {
        return $user->can('sales.update');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user): bool
    {
        if ($user->isViewingAllRecords()) {
            return false;
        }

        return $user->can('sales.create');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Sale $sale): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->can('sales.update');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Sale $sale): bool
    {
        if ($sale->isUnconfirmed()) {
            return true;
        }

        return $user->can('sales.delete');
    }

    /**
     * Determine whether the user can update the status of the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function cancel(User $user, Sale $sale)
    {
        return false;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function generateMissing(User $user, Sale $sale): bool
    {
        return $user->isSuperAdmin();
    }
}
