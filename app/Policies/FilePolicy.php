<?php

namespace App\Policies;

use App\Models\File;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class FilePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, File $file): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        return $user->account->is($file->account);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, File $file): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        return $user->account->is($file->account);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, File $file): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        return $user->account->is($file->account);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, File $file): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        return $user->account->is($file->account);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, File $file): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        return $user->account->is($file->account);
    }
}
