<?php

namespace App\Policies;

use App\Models\Payment;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PaymentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('payments.view');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Payment $payment): bool
    {
        return $user->can('payments.view');
    }

    /**
     * Determine whether the user can create the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user): bool
    {
        return $user->can('payments.create');
    }

    /**
     * Determine whether the user can edit the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Payment $payment): bool
    {
        if (! in_array($payment->status, [Payment::STATUS_DRAFT, Payment::STATUS_PENDING_SUBMISSION, Payment::STATUS_FAILED])) {
            return false;
        }

        return $user->can('payments.update');
    }

    public function delete(User $user, Payment $payment)
    {
        if ($user->isSuperAdmin() && in_array($payment->status, [Payment::STATUS_DRAFT, Payment::STATUS_FAILED, Payment::STATUS_CANCELLED])) {
            return true;
        }

        return false;
    }
}
