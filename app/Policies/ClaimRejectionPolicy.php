<?php

namespace App\Policies;

use App\Enums\ClaimStatus;
use App\Models\Claim;
use App\Models\ClaimRejection;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ClaimRejectionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user, Claim $claim): bool
    {
        if (in_array($claim->status, [ClaimStatus::REJECTED, ClaimStatus::AUTHORISED, ClaimStatus::SETTLED])) {
            return false;
        }

        return $user->can('claim-rejections.create');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, ClaimRejection $claimRejection): bool
    {
        return $user->can('claim-rejections.update');
    }
}
