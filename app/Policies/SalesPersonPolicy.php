<?php

namespace App\Policies;

use App\Models\SalesPerson;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SalesPersonPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('sales-people.manage');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, SalesPerson $salesPerson): bool
    {
        return $user->isAdmin() || $user->account_id === $salesPerson->account_id;
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user): bool
    {
        return (bool) $user->account_id;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, SalesPerson $salesPerson): bool
    {
        return $user->can('sales-people.manage');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, SalesPerson $salesPerson): bool
    {
        return $user->can('sales-people.manage');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, SalesPerson $salesPerson): bool
    {
        return $user->can('sales-people.manage');
    }
}
