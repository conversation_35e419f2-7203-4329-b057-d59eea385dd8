<?php

namespace App\Policies;

use App\Models\Account;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class AccountPolicy
{
    use HandlesAuthorization;

    public function before(User $user): ?bool
    {
        if (! $user->isViewingAllRecords()) {
            return false;
        }

        return null;
    }

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('accounts.manage') || $user->accounts()->exists();
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Account $account): bool
    {
        return $user->can('accounts.manage');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user): bool
    {
        return $user->can('accounts.manage');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Account $account): bool
    {
        return $user->can('accounts.manage');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Account $account): bool
    {
        return $user->can('accounts.manage');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Account $account): bool
    {
        return $user->can('accounts.manage');
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, Account $account): bool
    {
        return false;
    }

    public function switch(User $user, ?Account $account = null)
    {
        if ($user->can('accounts.manage')) {
            return true;
        }
        if ($user->accounts->count() === 1) {
            return false;
        }
        if ($account) {
            return $user->accounts->contains($account);
        }

        return true;
    }
}
