<?php

namespace App\Policies;

use App\Enums\ProductStatus;
use App\Models\BreakdownPlan;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class BreakdownPlanPolicy
{
    use HandlesAuthorization;

    public function before(User $user): ?bool
    {
        if (! $user->isViewingAllRecords() &&
            $user->account->breakdownProducts()->doesntExist() &&
            BreakdownPlan::where('account_id', $user->account_id)->doesntExist()) {

            return false;
        }

        return null;
    }

    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, BreakdownPlan $breakdownPlan): bool
    {
        if ($user->isAdmin()) {
            return true;
        }
        if ($user->account_id === $breakdownPlan->account_id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, BreakdownPlan $breakdownPlan): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can update the status of the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function cancel(User $user, BreakdownPlan $breakdownPlan)
    {
        if ($user->isAdmin() === false) {
            return false;
        }

        return in_array($breakdownPlan->status, [ProductStatus::PENDING, ProductStatus::LIVE]);
    }

    public function exportAll(User $user)
    {
        return $user->can('breakdownPlans.export');
    }
}
