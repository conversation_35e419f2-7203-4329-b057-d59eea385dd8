<?php

namespace App\Policies;

use App\Models\ClaimAuthorisation;
use App\Models\ClaimEstimate;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ClaimAuthorisationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('claim-authorisations.view');
    }

    /**
     * Determine whether the user can view models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, ClaimAuthorisation $claimAuthorisation): bool
    {
        return $user->can('claim-authorisations.view');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user, ClaimEstimate $claimEstimate): bool
    {
        if ($claimEstimate->claim?->rejection()->exists()) {
            return false;
        }
        if ($user->isAdmin()) {
            return true;
        }

        return $user->can('claim-authorisations.create');
    }

    /**
     * Determine whether the user can update models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, ClaimAuthorisation $authorisation): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        return $user->can('claim-authorisations.update');
    }

    /**
     * Determine whether the user can delete models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, ClaimAuthorisation $authorisation): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        return $user->can('claim-authorisations.delete');
    }
}
