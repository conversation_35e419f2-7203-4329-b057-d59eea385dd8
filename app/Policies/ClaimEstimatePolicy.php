<?php

namespace App\Policies;

use App\Models\Claim;
use App\Models\ClaimEstimate;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ClaimEstimatePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('claim-estimates.view');
    }

    /**
     * Determine whether the user can view models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, ClaimEstimate $claimEstimate): bool
    {
        return $user->can('claim-estimates.view');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user, ?Claim $claim = null): bool
    {
        if (! $claim) {
            return false;
        }

        if ($claim->rejection()->exists()) {
            return false;
        }

        return $user->can('claim-estimates.create');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, ClaimEstimate $claimEstimate): bool
    {
        if ($claimEstimate->authorisation()->exists()) {
            return false;
        }
        if ($claimEstimate->claim->rejection()->exists()) {
            return false;
        }

        return $user->can('claim-estimates.update');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, ClaimEstimate $claimEstimate): bool
    {
        return false;
    }
}
