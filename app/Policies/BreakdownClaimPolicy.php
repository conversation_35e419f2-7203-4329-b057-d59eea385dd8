<?php

namespace App\Policies;

use App\Models\BreakdownClaim;
use App\Models\BreakdownPlan;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class BreakdownClaimPolicy
{
    use HandlesAuthorization;

    public function before(User $user): ?bool
    {
        if (! $user->isViewingAllRecords() &&
            $user->account->breakdownProducts()->doesntExist() &&
            BreakdownPlan::where('account_id', $user->account_id)->has('claims')->doesntExist()) {

            return false;
        }

        return null;
    }

    public function viewAny(User $user): bool
    {
        return $user->can('breakdown-claims.view');
    }

    public function view(User $user, BreakdownClaim $breakdownClaim): bool
    {
        return $user->can('breakdown-claims.view');
    }

    public function create(User $user, BreakdownPlan $breakdownPlan): bool
    {
        return $user->can('breakdown-claims.create');
    }

    public function update(User $user, BreakdownClaim $breakdownClaim): bool
    {
        return $user->can('breakdown-claims.update');
    }

    public function delete(User $user, BreakdownClaim $breakdownClaim): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->can('breakdown-claims.delete');
    }
}
