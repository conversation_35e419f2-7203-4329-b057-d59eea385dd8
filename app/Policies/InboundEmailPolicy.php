<?php

namespace App\Policies;

use App\Models\InboundEmail;
use App\Models\User;

class InboundEmailPolicy
{
    /**
     * Determine whether the user can view the list.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->isViewingAllRecords();
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, InboundEmail $inboundEmail): bool
    {
        if ($user->isViewingAllRecords()) {
            return true;
        }

        return $user->account->is($inboundEmail->account);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, InboundEmail $inboundEmail): bool
    {
        if ($user->isViewingAllRecords()) {
            return true;
        }

        return $user->account->is($inboundEmail->account);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, InboundEmail $inboundEmail): bool
    {
        if ($user->isViewingAllRecords()) {
            return true;
        }

        return $user->account->is($inboundEmail->account);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, InboundEmail $inboundEmail): bool
    {
        if ($user->isViewingAllRecords()) {
            return true;
        }

        return $user->account->is($inboundEmail->account);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, InboundEmail $inboundEmail): bool
    {
        if ($user->isViewingAllRecords()) {
            return true;
        }

        return $user->account->is($inboundEmail->account);
    }
}
