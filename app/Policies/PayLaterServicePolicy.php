<?php

namespace App\Policies;

use App\Models\PayLaterService;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PayLaterServicePolicy
{
    use HandlesAuthorization;

    public function before(User $user): ?bool
    {
        if (! $user->isViewingAllRecords() &&
            ! $user->account->hasPayLater() &&
            PayLaterService::where('account_id', $user->account_id)->doesntExist()) {

            return false;
        }

        return null;
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('pay-later-services.view');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, PayLaterService $payLaterService): bool
    {
        return $user->can('pay-later-services.view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        if ($user->isViewingAllRecords()) {
            return false;
        }
        if ($user->account->hasPayLater() === false) {
            return false;
        }

        return $user->can('pay-later-services.create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, PayLaterService $payLaterService): bool
    {
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, PayLaterService $payLaterService): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, PayLaterService $payLaterService): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, PayLaterService $payLaterService): bool
    {
        return false;
    }
}
