<?php

namespace App\Policies;

use App\Models\SalesLead;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SalesLeadPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('sales-leads.view');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, SalesLead $salesLead): bool
    {
        return $user->can('sales-leads.view');
    }
}
