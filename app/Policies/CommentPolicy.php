<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Contracts\Auth\Authenticatable;
use Parallax\FilamentComments\Models\FilamentComment;

class CommentPolicy
{
    use HandlesAuthorization;

    public function viewAny(Authenticatable $user): bool
    {
        return true;
    }

    public function view(Authenticatable $user, FilamentComment $comment): bool
    {
        return true;
    }

    public function create(Authenticatable $user): bool
    {
        return true;
    }

    public function update(Authenticatable $user, FilamentComment $comment): bool
    {
        return false;
    }

    public function delete(User $user, FilamentComment $comment): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }
        if ($user->isAdmin() && $comment->user->id === $user->id) {
            return true;
        }

        return false;
    }
}
