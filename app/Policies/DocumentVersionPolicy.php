<?php

namespace App\Policies;

use App\Models\DocumentVersion;
use App\Models\Sale;
use App\Models\User;

class DocumentVersionPolicy
{
    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, DocumentVersion $documentVersion): bool
    {
        return
            $documentVersion->document->latestVersion->is($documentVersion) &&
            $documentVersion->created_at > Sale::query()->latest()->first()->created_at;
    }
}
