<?php

namespace App\Policies;

use App\Models\Repairer;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class RepairerPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('repairers.view');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Repairer $repairer): bool
    {
        return $user->can('repairers.view');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user): bool
    {
        return $user->can('repairers.manage');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Repairer $repairer): bool
    {
        return $user->can('repairers.manage');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Repairer $repairer): bool
    {
        return $user->can('repairers.manage');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Repairer $repairer): bool
    {
        return $user->can('repairers.manage');
    }
}
