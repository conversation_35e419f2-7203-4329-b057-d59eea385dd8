<?php

namespace App\Services\Accounting;

use Illuminate\Support\Collection;

interface AccountingService
{
    public function createContact(ContactData $contactData): ContactData;

    public function getContacts(array $query = []): Collection;

    public function getInvoices(): Collection;

    public function getInvoice($resourceId): InvoiceData;

    public function deleteInvoice($resourceId);

    public function createInvoice(InvoiceData $invoiceValueObject): InvoiceData;

    public function createInvoicePayment(PaymentData $paymentData): PaymentData;

    public function createBankTransaction(BankTransactionData $bankTransactionData): BankTransactionData;

    public function deleteBankTransaction($bankTransactionId): void;

    public function createBankTransfer(BankTransferData $bankTransferData): BankTransferData;

    public function getAccounts(array $query = []): Collection;

    public function getAccount($resourceId): AccountData;

    public function createAccount(AccountData $accountData): bool;

    public function updateAccount($resourceId, AccountData $accountData);

    public function emailInvoice(string $invoiceId): bool;
}
