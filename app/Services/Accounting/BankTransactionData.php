<?php

namespace App\Services\Accounting;

use Carbon\Carbon;
use Illuminate\Support\Collection;

final class BankTransactionData
{
    public function __construct(
        public ?string $id,
        public ContactData $contact,
        public ?string $type,
        public ?string $status,
        public ?string $date,
        public ?string $reference,
        public ?string $url,
        public string $bankAccountId,
        public ?Collection $lineItems = null,
        public ?bool $isReconciled = false,
    ) {}

    public static function fromXero(array $data): self
    {
        try {
            return new self(
                id: $data['BankTransactionID'],
                contact: ContactData::fromXero($data['Contact']),
                type: $data['Type'],
                status: $data['Status'],
                date: $data['DateString'] ?? null,
                reference: $data['Reference'] ?? null,
                url: $data['Url'] ?? null,
                bankAccountId: $data['BankAccount']['AccountID'],
                lineItems: collect($data['LineItems'])
                    ->filter(fn ($lineItem) => $lineItem['Quantity'] ?? false)
                    ->map(fn ($lineItem) => BankTransferLineItemData::fromXero($lineItem)),
                isReconciled: $data['IsReconciled'] ?? false,
            );
        } catch (\throwable $e) {
            throw new \Exception('Failed to parse invoice data: '.$e->getMessage());
        }
    }

    private static function convertXeroDateToTimestamp($xeroDate): ?Carbon
    {
        // Extract the timestamp part (removing '/Date(' and ')/')
        if (! preg_match('/\/Date\((\d+)([+-]\d{4})?\)\//', $xeroDate, $matches)) {
            return null;
        }

        return new Carbon($matches[1] / 1000);
    }
}
