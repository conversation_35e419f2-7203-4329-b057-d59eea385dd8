<?php

namespace App\Services\Accounting;

final class ContactData
{
    public function __construct(
        public ?string $id,
        public ?string $status = null,
        public ?string $name = null,
        public ?string $firstName = null,
        public ?string $lastName = null,
        public ?string $email = null,
        public ?string $addressLine1 = null,
        public ?string $addressLine2 = null,
        public ?string $addressLine3 = null,
        public ?string $addressLine4 = null,
        public ?string $addressCity = null,
        public ?string $addressRegion = null,
        public ?string $addressPostcode = null,
    ) {}

    public static function fromXero($data): ContactData
    {
        $address = collect($data['Addresses'] ?? [])->firstWhere('AddressType', 'STREET');

        return new self(
            id: $data['ContactID'],
            status: $data['ContactStatus'] ?? null,
            name: $data['Name'] ?? null,
            firstName: $data['FirstName'] ?? null,
            lastName: $data['LastName'] ?? null,
            email: $data['EmailAddress'] ?? null,
            addressLine1: $address['AddressLine1'] ?? null,
            addressLine2: $address['AddressLine2'] ?? null,
            addressLine3: $address['AddressLine3'] ?? null,
            addressLine4: $address['AddressLine4'] ?? null,
            addressCity: $address['City'] ?? null,
            addressRegion: $address['Region'] ?? null,
            addressPostcode: $address['PostalCode'] ?? null,
        );
    }
}
