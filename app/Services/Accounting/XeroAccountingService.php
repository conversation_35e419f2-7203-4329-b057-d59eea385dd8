<?php

namespace App\Services\Accounting;

use Illuminate\Cache\CacheManager;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class XeroAccountingService implements AccountingService
{
    protected int $authAttempts = 0;

    const SCOPES = [
        'accounting.transactions',
        'accounting.contacts',
        'accounting.settings',
    ];

    public function __construct(protected CacheManager $cache, protected array $config) {}

    public function authenticate(): bool
    {
        $response = Http::withBasicAuth($this->config['client_id'], $this->config['secret'])
            ->asForm()
            ->retry(3, 1)
            ->post('https://identity.xero.com/connect/token', [
                'grant_type' => 'client_credentials',
                'scope' => implode(' ', self::SCOPES),
            ]);

        if ($response->successful()) {
            $this->cache->put($this->cacheKey(), $response->json('access_token'), $response->json('expires_in'));

            return true;
        }

        return false;
    }

    public function sendRequest(string $method, string $endpoint, array $data = [])
    {
        if (! $this->cache->has($this->cacheKey()) && ! $this->authenticate()) {
            throw new AccountingServiceException('Unable to authenticate with Xero');
        }

        try {
            $result = Http::withToken((string) $this->cache->get($this->cacheKey()))
                ->retry(3, 1)
                ->$method($this->config['base_url'].$endpoint, $data)
                ->throw();
            $this->authAttempts = 0;

            return $result;
        } catch (RequestException $e) {
            if ($e->response->status() === 401 && $this->authAttempts++ < 3) {
                if (! $this->authenticate()) {
                    throw new AccountingServiceException('Unable to authenticate with Xero');
                }

                return $this->sendRequest(...func_get_args());
            }
            if ($e->response->status() === 429) {
                Log::warning(sprintf("Rate limited. Retrying after %s seconds\n", $e->response->header('Retry-After')));
                sleep($e->response->header('Retry-After') + 1);

                return $this->sendRequest(...func_get_args());
            }
            throw new AccountingServiceException(
                $e->response->json('Elements.0.ValidationErrors.0.Message') ?: $e->response->json('Message') ?: $e->response->json('Title'),
                $e->response->json('ErrorNumber') ?: $e->response->status()
            );
        }
    }

    public function createContact(ContactData $contactData): ContactData
    {
        return ContactData::fromXero(
            $this->sendRequest('post', 'Contacts', [
                'ContactID' => $contactData->id,
                'Name' => $contactData->name,
                'FirstName' => $contactData->firstName,
                'LastName' => $contactData->lastName,
                'EmailAddress' => $contactData->email,
                'Addresses' => [
                    [
                        'AddressType' => 'STREET',
                        'AddressLine1' => $contactData->addressLine1,
                        'AddressLine2' => $contactData->addressLine2,
                        'AddressLine3' => $contactData->addressLine3,
                        'AddressLine4' => $contactData->addressLine4,
                        'City' => $contactData->addressCity,
                        'Region' => $contactData->addressRegion,
                        'PostalCode' => $contactData->addressPostcode,
                    ],
                ],
                'IsCustomer' => true,
                'IsSupplier' => false,
            ])->json('Contacts.0')
        );
    }

    public function getContacts(array $query = []): Collection
    {
        return collect(
            $this->sendRequest(
                'get',
                'Contacts',
                array_merge(['includeArchived' => 'false'], $query),
            )->json('Contacts')
        )->map(fn ($contact) => ContactData::fromXero($contact));
    }

    public function getContact($id): ContactData
    {
        return ContactData::fromXero(
            $this->sendRequest('get', "Contacts/{$id}")->json('Contacts.0')
        );
    }

    public function createInvoice(InvoiceData $invoiceValueObject): InvoiceData
    {
        $invoice = $this->sendRequest('post', 'Invoices', [
            'InvoiceID' => $invoiceValueObject->id,
            'Type' => 'ACCREC',
            'Contact' => [
                'ContactID' => $invoiceValueObject->contact->id,
                'Name' => $invoiceValueObject->contact->name,
                'FirstName' => $invoiceValueObject->contact->firstName,
                'LastName' => $invoiceValueObject->contact->lastName,
                'EmailAddress' => $invoiceValueObject->contact->email,
                'Addresses' => [
                    [
                        'AddressType' => 'STREET',
                        'AddressLine1' => $invoiceValueObject->contact->addressLine1,
                        'AddressLine2' => $invoiceValueObject->contact->addressLine2,
                        'AddressLine3' => $invoiceValueObject->contact->addressLine3,
                        'AddressLine4' => $invoiceValueObject->contact->addressLine4,
                        'City' => $invoiceValueObject->contact->addressCity,
                        'Region' => $invoiceValueObject->contact->addressRegion,
                        'PostalCode' => $invoiceValueObject->contact->addressPostcode,
                    ],
                ],
                'IsCustomer' => true,
                'IsSupplier' => false,
            ],
            'Status' => $invoiceValueObject->status,
            'Date' => $invoiceValueObject->date,
            'DueDate' => $invoiceValueObject->dueDate,
            'Url' => $invoiceValueObject->url,
            'Reference' => $invoiceValueObject->reference,
            'LineItems' => collect($invoiceValueObject->lineItems)
                ->map(fn (InvoiceLineItemData $lineItem) => [
                    'Description' => $lineItem->description,
                    'Quantity' => $lineItem->quantity,
                    'UnitAmount' => $lineItem->unitAmount,
                    'taxAmount' => $lineItem->taxAmount,
                    'LineItemID' => $lineItem->id,
                    'AccountCode' => $lineItem->accountCode,
                ])->toArray(),
        ])->json('Invoices.0');

        return InvoiceData::fromXero($invoice);
    }

    public function getInvoice($resourceId): InvoiceData
    {
        return InvoiceData::fromXero(
            $this->sendRequest('get', "Invoices/{$resourceId}")
                ->json('Invoices.0')
        );
    }

    public function getInvoices(): Collection
    {
        return collect(
            $this->sendRequest('get', 'Invoices')
                ->json('Invoices')
        )->map(fn ($invoice) => InvoiceData::fromXero($invoice));
    }

    public function deleteInvoice($resourceId)
    {
        $this->sendRequest('post', "Invoices/{$resourceId}", [
            'Status' => 'DELETED',
            'InvoiceID' => $resourceId,
        ]);
    }

    public function getAccounts(array $query = []): Collection
    {
        return collect(
            $this->sendRequest(
                'get',
                'Accounts',
                array_merge(['includeArchived' => 'false'], $query),
            )->json('Accounts')
        )->map(fn ($contact) => AccountData::fromXero($contact));
    }

    public function getAccount($resourceId): AccountData
    {
        return AccountData::fromXero(
            $this->sendRequest(
                'get',
                "Accounts/{$resourceId}",
            )->json('Accounts.0')
        );
    }

    public function createAccount(AccountData $accountData): bool
    {
        return $this->sendRequest('put', 'Accounts', [
            'AccountID' => $accountData->id,
            'Code' => $accountData->code,
            'Name' => $accountData->name,
            'Description' => $accountData->description,
            'Type' => $accountData->type,
            'ReportingCode' => $accountData->reportingCode,
            'ReportingCodeName' => $accountData->reportingCodeName,
            'TaxType' => $accountData->taxType,
        ])->successful();
    }

    public function updateAccount($resourceId, AccountData $accountData)
    {
        return $this->sendRequest('post', "Accounts/{$resourceId}", [
            'Code' => $accountData->code,
            'Name' => $accountData->name,
            'Description' => $accountData->description,
            'Type' => $accountData->type,
            'ReportingCode' => $accountData->reportingCode,
            'ReportingCodeName' => $accountData->reportingCodeName,
            'TaxType' => $accountData->taxType,
        ])->successful();
    }

    public function createInvoicePayment(PaymentData $paymentData): PaymentData
    {
        return PaymentData::fromXero(
            $this->sendRequest('post', 'Payments', [
                'PaymentID' => $paymentData->id,
                'Invoice' => [
                    'InvoiceID' => $paymentData->invoiceId,
                ],
                'Account' => [
                    'AccountID' => $paymentData->accountId,
                ],
                'Date' => $paymentData->date,
                'Amount' => $paymentData->amount,
            ])->json('Payments.0')
        );
    }

    public function deleteInvoicePayment($paymentId): InvoiceData
    {
        $response = $this->sendRequest('post', "Payments/{$paymentId}", [
            'Status' => 'DELETED',
        ]);

        return InvoiceData::fromXero($response->json('Payments.0.Invoice'));
    }

    public function createBankTransfer(BankTransferData $bankTransferData): BankTransferData
    {
        $bankTransfer = $this->sendRequest('post', 'BankTransfers', [
            'BankTransferID' => $bankTransferData->id,
            'FromBankAccount' => [
                'AccountID' => $bankTransferData->fromAccountId,
            ],
            'ToBankAccount' => [
                'AccountID' => $bankTransferData->toAccountId,
            ],
            'Date' => $bankTransferData->date,
            'Amount' => $bankTransferData->amount,
            'FromIsReconciled' => $bankTransferData->fromIsReconciled,
            'ToIsReconciled' => $bankTransferData->toIsReconciled,
        ])->json('BankTransfers.0');

        return BankTransferData::fromXero($bankTransfer);
    }

    public function createBankTransaction(BankTransactionData $bankTransactionData): BankTransactionData
    {
        return BankTransactionData::fromXero(
            $this->sendRequest('post', 'BankTransactions', [
                'BankTransactionID' => $bankTransactionData->id,
                'Contact' => [
                    'ContactID' => $bankTransactionData->contact->id,
                ],
                'BankAccount' => [
                    'AccountID' => $bankTransactionData->bankAccountId,
                ],
                'Date' => $bankTransactionData->date,
                'Status' => $bankTransactionData->status,
                'Type' => $bankTransactionData->type,
                'Reference' => $bankTransactionData->reference,
                'Url' => $bankTransactionData->url,
                'LineAmountTypes' => 'Exclusive',
                'LineItems' => collect($bankTransactionData->lineItems)
                    ->map(fn (BankTransferLineItemData $lineItem) => [
                        'Description' => $lineItem->description,
                        'Quantity' => $lineItem->quantity,
                        'UnitAmount' => $lineItem->unitAmount,
                        'TaxAmount' => $lineItem->taxAmount,
                        'AccountCode' => $lineItem->accountCode,
                    ])->toArray(),
            ])->json('BankTransactions.0')
        );
    }

    public function deleteBankTransaction($bankTransactionId): void
    {
        $this->sendRequest('post', "BankTransactions/{$bankTransactionId}", [
            'Status' => 'DELETED',
        ])->json();
    }

    public function getBankTransfers(): Collection
    {
        return collect(
            $this->sendRequest('get', 'BankTransfers')
                ->json('BankTransfers')
        )->map(fn ($bankTransfer) => BankTransferData::fromXero($bankTransfer));
    }

    public function emailInvoice(string $invoiceId): bool
    {
        $response = $this->sendRequest(
            'post',
            "Invoices/{$invoiceId}/Email"
        );

        return $response->successful();
    }

    protected function cacheKey()
    {
        return 'xero_access_token:'.$this->config['client_id'];
    }
}
