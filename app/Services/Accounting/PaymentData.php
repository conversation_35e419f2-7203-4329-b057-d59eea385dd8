<?php

namespace App\Services\Accounting;

final class PaymentData
{
    public function __construct(
        public ?string $id,
        public ?string $invoiceId,
        public ?string $accountId,
        public string $date,
        public string $amount,
    ) {}

    public static function fromXero(array $data): self
    {
        return new self(
            id: $data['PaymentID'],
            invoiceId: $data['Invoice']['InvoiceID'] ?? null,
            accountId: $data['Account']['AccountID'] ?? null,
            date: $data['Date'],
            amount: $data['Amount'],
        );
    }
}
