<?php

namespace App\Services\Accounting;

final class BankTransferData
{
    public function __construct(
        public ?string $id,
        public ?string $date,
        public string $amount,
        public string $fromAccountId,
        public string $toAccountId,
        public ?string $fromTransactionId,
        public ?string $toTransactionId,
        public ?string $reference,
        public ?bool $fromIsReconciled = null,
        public ?bool $toIsReconciled = null,
    ) {}

    public static function fromXero($bankTransfer): self
    {
        return new self(
            id: $bankTransfer['BankTransferID'],
            date: $bankTransfer['Date'],
            amount: $bankTransfer['Amount'],
            fromAccountId: $bankTransfer['FromBankAccount']['AccountID'],
            toAccountId: $bankTransfer['ToBankAccount']['AccountID'],
            fromTransactionId: $bankTransfer['FromBankTransactionID'] ?? null,
            toTransactionId: $bankTransfer['ToBankTransactionID'] ?? null,
            reference: $bankTransfer['Reference'] ?? null,
            fromIsReconciled: $bankTransfer['FromIsReconciled'] ?? null,
            toIsReconciled: $bankTransfer['ToIsReconciled'] ?? null,
        );
    }
}
