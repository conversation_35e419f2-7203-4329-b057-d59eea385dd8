<?php

namespace App\Services\Accounting;

final class InvoiceLineItemData
{
    public function __construct(
        public string $quantity,
        public string $unitAmount,
        public string $taxAmount,
        public string $accountCode,
        public ?string $description,
        public ?string $id = null,
    ) {}

    public static function fromXero(array $data)
    {
        return new self(
            quantity: $data['Quantity'],
            unitAmount: $data['UnitAmount'],
            taxAmount: $data['TaxAmount'],
            accountCode: $data['AccountCode'] ?? '??',
            description: $data['Description'] ?? null,
            id: $data['LineItemID'],
        );
    }
}
