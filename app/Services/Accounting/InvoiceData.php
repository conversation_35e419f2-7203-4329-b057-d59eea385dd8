<?php

namespace App\Services\Accounting;

use Carbon\Carbon;
use Illuminate\Support\Collection;

class InvoiceData
{
    final public function __construct(
        public ?string $id,
        public ?ContactData $contact = null,
        public ?string $status = null,
        public ?string $date = null,
        public ?string $dueDate = null,
        public ?string $invoiceNumber = null,
        public ?string $reference = null,
        public ?string $url = null,
        public ?Collection $lineItems = null,
        public ?Collection $payments = null,
        public ?Collection $creditNotes = null,
        public ?string $updatedDate = null,
    ) {
        $this->lineItems = $lineItems ?? collect();
        $this->payments = $payments ?? collect();
        $this->creditNotes = $creditNotes ?? collect();
    }

    public static function fromXero(array $data): self
    {
        return new self(
            id: $data['InvoiceID'],
            contact: ContactData::fromXero($data['Contact']),
            status: $data['Status'],
            date: $data['DateString'] ?? null,
            dueDate: $data['DueDateString'] ?? null,
            invoiceNumber: $data['InvoiceNumber'],
            reference: $data['Reference'] ?? null,
            url: $data['Url'] ?? null,
            lineItems: collect($data['LineItems'])
                ->filter(fn ($lineItem) => $lineItem['Quantity'] ?? false)
                ->map(fn ($lineItem) => InvoiceLineItemData::fromXero($lineItem)),
            payments: collect($data['Payments'] ?? [])
                ->map(fn ($payment) => new PaymentData(
                    id: $payment['PaymentID'],
                    invoiceId: $data['InvoiceID'],
                    accountId: null,
                    date: self::convertXeroDateToTimestamp($payment['Date']),
                    amount: $payment['Amount'],
                )),
            creditNotes: collect($data['CreditNotes'] ?? [])
                ->map(fn ($creditNote) => $creditNote['CreditNoteID']),
            updatedDate: substr(self::convertXeroDateToTimestamp($data['UpdatedDateUTC']), 0, 10),
        );
    }

    private static function convertXeroDateToTimestamp($xeroDate): ?Carbon
    {
        // Extract the timestamp part (removing '/Date(' and ')/')
        if (! preg_match('/\/Date\((\d+)([+-]\d{4})?\)\//', $xeroDate, $matches)) {
            return null;
        }

        return new Carbon($matches[1] / 1000);
    }
}
