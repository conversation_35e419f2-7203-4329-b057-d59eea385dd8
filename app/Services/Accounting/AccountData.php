<?php

namespace App\Services\Accounting;

final class AccountData
{
    public function __construct(
        public ?string $id,
        public ?string $code,
        public string $name,
        public ?string $description,
        public string $type,
        public string $class,
        public ?string $reportingCode,
        public ?string $reportingCodeName,
        public ?string $taxType,
    ) {}

    public static function fromXero($contact): self
    {
        return new self(
            id: $contact['AccountID'],
            code: $contact['Code'] ?? null,
            name: $contact['Name'],
            description: $contact['Description'] ?? null,
            type: $contact['Type'],
            class: $contact['Class'],
            reportingCode: $contact['ReportingCode'],
            reportingCodeName: $contact['ReportingCodeName'] ?? null,
            taxType: $contact['TaxType'],
        );
    }
}
