<?php

namespace App\Services\Surcharge;

use App\Models\ManufacturerSurcharge;

class SurchargeResult
{
    public function __construct(
        public readonly float $adminFeePercentage,
        public readonly float $provisionPercentage,
        public readonly float $sellingPricePercentage,
        public readonly string $source
    ) {}

    public function addAdminFeeSurcharge(float $baseAmount): float
    {
        return round($baseAmount + ($baseAmount * $this->adminFeePercentage / 100), 2);
    }

    public function addProvisionSurcharge(float $baseAmount): float
    {
        return round($baseAmount + ($baseAmount * $this->provisionPercentage / 100), 2);
    }

    public function addSellingPriceSurcharge(float $baseAmount): float
    {
        return round($baseAmount + ($baseAmount * $this->sellingPricePercentage / 100), 2);
    }

    public function hasAnySurcharge(): bool
    {
        return $this->adminFeePercentage > 0
            || $this->provisionPercentage > 0
            || $this->sellingPricePercentage > 0;
    }

    public static function zero(): self
    {
        return new self(0, 0, 0, 'none');
    }

    public static function fromManufacturerSurcharge(ManufacturerSurcharge $surcharge): self
    {
        return new self(
            $surcharge->warranty_admin_fee_percentage ?? 0,
            $surcharge->warranty_provision_percentage ?? 0,
            $surcharge->warranty_selling_price_percentage ?? 0,
            'account_specific'
        );
    }
}
