<?php

namespace App\Services\Surcharge;

use App\Models\Account;
use App\Models\ManufacturerSurcharge;
use App\Models\Vehicle;
use OvonGroup\ManufacturerLogos\Models\Manufacturer;

class ManufacturerSurchargeRepository
{
    public function lookupSurcharge(Vehicle $vehicle): SurchargeResult
    {
        // Check if the account has a specific ManufacturerSurcharge for this manufacturer
        $accountSpecificSurcharge = $this->getAccountSpecificSurcharge($vehicle->account, $vehicle->manufacturer);

        if ($accountSpecificSurcharge) {
            return SurchargeResult::fromManufacturerSurcharge($accountSpecificSurcharge);
        }

        // No surcharge applies
        return SurchargeResult::zero();
    }

    /**
     * Get account-specific manufacturer surcharge if it exists
     */
    private function getAccountSpecificSurcharge(Account $account, ?Manufacturer $manufacturer): ?ManufacturerSurcharge
    {
        if (! $manufacturer) {
            return null;
        }

        return ManufacturerSurcharge::where('account_id', $account->id)
            ->where('manufacturer_id', $manufacturer->id)
            ->first();
    }
}
