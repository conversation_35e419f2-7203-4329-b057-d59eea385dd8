<?php

namespace App\Services\RecentAccounts;

use App\Models\Account;
use Illuminate\Cache\Repository;
use Illuminate\Support\Facades\Auth;

class RecentAccountsRepository
{
    const LIMIT = 10;

    public function __construct(Repository $cache)
    {
        $this->cache = $cache;
    }

    public function get()
    {
        return collect($this->cache->get($this->getKey(), []));
    }

    public function push(Account $account): bool
    {
        return $this->cache->put(
            $this->getKey(),
            collect($this->get())
                ->prepend($account->only('id', 'name'))
                ->unique('id')
                ->take(self::LIMIT)
                ->all()
        );
    }

    public function removeAccount(Account $account)
    {
        $this->cache->put(
            $this->getKey(),
            collect($this->get())
                ->reject(fn ($recentAccount) => $recentAccount['id'] === $account->id)
                ->all()
        );
    }

    private function getKey(): string
    {
        return 'recent-accounts-'.Auth::id();
    }

    public function getWithoutSelectedAccount()
    {
        return $this->get()
            ->filter(fn ($account) => Auth::user()->isAdmin() || Auth::user()->belongsToAccount($account['id']))
            ->reject(fn ($account) => $account['id'] === Auth::user()->account_id)
            ->all();
    }
}
