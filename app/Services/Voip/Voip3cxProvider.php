<?php

namespace App\Services\Voip;

use Carbon\Carbon;
use Illuminate\Cache\CacheManager;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Voip3cxProvider
{
    const string CACHE_TOKEN = 'VOIP_3CX_TOKEN';

    const int TIMEOUT = 5;

    public function __construct(protected CacheManager $cache, protected array $config) {}

    public function sendRequest(string $method, string $uri, array $options = [])
    {
        $response = Http::baseUrl($this->config['endpoint'])
            ->asJson()
            ->withToken($this->getToken())
            ->timeout(self::TIMEOUT)
            ->send($method, $uri, $options);

        if ($response->unauthorized()) {
            $this->authenticate();

            return $this->sendRequest($method, $uri, $options);
        }

        if ($response->failed()) {
            Log::error('Failed to send request to 3CX', [
                'status' => $response->status(),
                'body' => (string) $response->getBody(),
                'uri' => $uri,
                'request' => $options,
            ]);
            throw new \RuntimeException('Failed to send request to 3CX');
        }

        return $response;
    }

    public function getToken(): mixed
    {
        return $this->cache->get(self::CACHE_TOKEN) ?: $this->authenticate();
    }

    public function authenticate(): string
    {
        $response = Http::baseUrl($this->config['endpoint'])
            ->asForm()
            ->post('connect/token', [
                'client_id' => $this->config['client_id'],
                'client_secret' => $this->config['api_key'],
                'grant_type' => 'client_credentials',
            ])->json();

        $this->cache->put(self::CACHE_TOKEN, $response['access_token'], now()->addMinutes($response['expires_in'])->subSeconds(self::TIMEOUT));

        return $response['access_token'];
    }

    public function getUsers()
    {
        return $this->sendRequest('GET', 'xapi/v1/Users')->json('value');
    }

    public function getCallControlData()
    {
        return $this->sendRequest('GET', 'callcontrol')->json();
    }

    public function getCallControlParticipants(string $dn)
    {
        return $this->sendRequest('GET', "callcontrol/{$dn}/participants")->json();
    }

    public function getCallControlParticipant(string $dn, string $participantId)
    {
        return $this->sendRequest('GET', "callcontrol/{$dn}/participants/{$participantId}")->json();
    }

    public function makeCall(string $extension, string $destination, array $options = [])
    {
        //        dd([
        //            'json' => [
        //                'destination' => $destination,
        //                ...$options,
        //
        //            ]]);
        return $this->sendRequest('POST', "callcontrol/{$extension}/makecall", [
            'json' => [
                'destination' => $destination,
                ...$options,
            ],
        ])->json('result');
    }

    public function getCallLogs(?Carbon $start = null, ?Carbon $end = null)
    {
        $start = $start ?: Carbon::today();
        $end = $end ?: $start->copy()->endOfDay();

        return $this->sendRequest('GET', sprintf(
            'xapi/v1/ReportCallLogData/Pbx.GetCallLogData(periodFrom=%s,periodTo=%s,sourceType=0,sourceFilter=\'\',destinationType=0,destinationFilter=\'\',callsType=0,callTimeFilterType=0,callTimeFilterFrom=\'0:00:0\',callTimeFilterTo=\'0:00:0\',hidePcalls=true)',
            $start->toIsoString(),
            $end->toIsoString(),
        ))->json('value');
    }

    public function recordingUrl(string $recordingId)
    {
        return sprintf(
            'https://minstercarco.my3cx.uk/xapi/v1/Recordings/Pbx.DownloadRecording(recId=%s)?access_token=%s',
            $recordingId,
            $this->getToken(),
        );
    }
}
