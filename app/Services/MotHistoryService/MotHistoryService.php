<?php

namespace App\Services\MotHistoryService;

use App\Services\VehicleLookupService\VehicleNotFoundException;
use Illuminate\Cache\CacheManager;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;

class MotHistoryService
{
    protected ?string $accessToken = null;

    // curl --request POST --url 'https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token'
    // --header 'content-type: application/x-www-form-urlencoded'
    // --data grant_type=client_credentials --data client_id=CLIENT_ID_HERE
    // --data client_secret=CLIENT_SECRET_HERE
    // --data scope=https://tapi.dvsa.gov.uk/.default

    const AUTH_ENDPOINT = 'https://login.microsoftonline.com/%s/oauth2/v2.0/token';

    const API_ENDPOINT = 'https://history.mot.api.gov.uk';

    public function __construct(
        protected array $config,
        protected CacheManager $cache,
    ) {}

    public function authenticate()
    {
        $this->accessToken = $this->cache->get('dvsa_mot_history_access_token');

        if ($this->accessToken) {
            return;
        }

        $response = Http::asForm()
            ->retry(3, 1000)
            ->post(sprintf(self::AUTH_ENDPOINT, $this->config['tenant_id']), [
                'grant_type' => 'client_credentials',
                'client_id' => $this->config['client_id'],
                'client_secret' => $this->config['client_secret'],
                'scope' => $this->config['scope_url'],
            ])->throw();

        $this->accessToken = $response->json('access_token');
        $this->cache->put('dvsa_mot_history_access_token', $this->accessToken, $response->json('expires_in') - 60);
    }

    public function lookupVrm(string $vrm)
    {
        $this->authenticate();

        try {
            return Http::withHeaders([
                'Authorization' => 'Bearer '.$this->accessToken,
                'X-API-Key' => $this->config['key'],
            ])
                ->retry(1, 1000)
                ->baseUrl(self::API_ENDPOINT)
                ->get('v1/trade/vehicles/registration/'.strtolower(str_replace(' ', '', $vrm)))
                ->throw()
                ->json();
        } catch (RequestException $e) {
            if ($e->getCode() === 404) {
                VehicleNotFoundException::throw();
            }
            throw $e;
        }
    }

    public function lookupVin(string $vin)
    {
        $this->authenticate();

        try {
            return Http::withHeaders([
                'Authorization' => 'Bearer '.$this->accessToken,
                'X-API-Key' => $this->config['key'],
            ])
                ->retry(1, 1000)
                ->baseUrl(self::API_ENDPOINT)
                ->get('v1/trade/vehicles/vin/'.$vin)
                ->throw()
                ->json();
        } catch (RequestException $e) {
            if ($e->getCode() === 404) {
                VehicleNotFoundException::throw();
            }
            throw $e;
        }
    }
}
