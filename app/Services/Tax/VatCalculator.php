<?php

namespace App\Services\Tax;

class VatCalculator
{
    public function getVatAmount($amount): float
    {
        return round($amount * ($this->vatPercentage() / 100), 2);
    }

    public function addVat($amount): float
    {
        return round($amount + $this->getVatAmount($amount), 2);
    }

    public function getAmountExcludingVat($amount): float
    {
        return round($amount / (1 + ($this->vatPercentage() / 100)), 2);
    }

    public function getVatAmountIncluded($amount): float
    {
        return round($amount - $this->getAmountExcludingVat($amount), 2);
    }

    public function vatPercentage(): float
    {
        return 20;
    }
}
