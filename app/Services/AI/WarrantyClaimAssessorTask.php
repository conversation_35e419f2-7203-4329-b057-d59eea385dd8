<?php

namespace App\Services\AI;

use App\Models\ClaimEstimate;
use Filament\Infolists\Components\TextEntry;
use Prism\Prism\Schema\EnumSchema;
use Prism\Prism\Schema\NumberSchema;
use Prism\Prism\Schema\ObjectSchema;
use Prism\Prism\Schema\StringSchema;

class WarrantyClaimAssessorTask extends AiTaskProcessor
{
    private string $userPrompt = <<<'EOT'
    Warranty Coverage Document:

    %s

    ===========================
    Claim Details:
    %s
    EOT;

    protected function objectSchema(): ObjectSchema
    {
        return new ObjectSchema(
            name: 'warranty_claim_assessment',
            description: 'A structured assessment of a warranty claim',
            properties: [
                new EnumSchema('decision', 'The decision on the warranty claim', [
                    'approved',
                    'rejected',
                    'more_information_needed',
                ]),
                new NumberSchema('confidence', 'Confidence level in the decision'),
                new StringSchema('reason', 'Detailed explanation for the decision'),
            ],
            requiredFields: ['decision', 'confidence', 'reason']
        );
    }

    protected function userPrompt(): string
    {
        return sprintf($this->userPrompt, $this->warrantyDocument(), $this->claimDetails());
    }

    protected function systemPrompt(): string
    {
        return $this->systemPrompt;
    }

    public function getInfolistSchema(): array
    {
        return [
            TextEntry::make('decision')
                ->helperText('This is only the opinion of the bot and not a final decision.')
                ->badge()
                ->color(fn (string $state) => match ($state) {
                    'approved' => 'success',
                    'rejected' => 'danger',
                    'more_information_needed' => 'warning',
                    default => 'info',
                }),
            TextEntry::make('reason')
                ->separator(PHP_EOL)
                ->listWithLineBreaks(),
        ];
    }

    private function warrantyDocument(): string
    {
        // TODO account for the diminishing cover level for recurring warranties
        $coverLevel = $this->claimEstimate()->claim->warranty->product->coverLevel;

        $document = $coverLevel->document;
        if ($document) {
            return $document->getContent($this->claimEstimate()->claim->warranty->sale->start_date);
        }
        throw new \UnexpectedValueException("Document not found for Cover Level: {$coverLevel->id}");
    }

    private function claimDetails(): string
    {
        return json_encode([
            'fault_description' => $this->claimEstimate()->claim->fault_description,
            'work_required' => $this->claimEstimate()->work_required,
            'line_items' => $this->claimEstimate()->lineItems->map->only(['type', 'description', 'quantity', 'amount'])->toArray(),
        ]);
    }

    private function claimEstimate(): ClaimEstimate
    {
        return $this->subject;
    }
}
