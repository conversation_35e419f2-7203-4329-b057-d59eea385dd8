<?php

namespace App\Services\AI;

use App\Models\Claim;
use Filament\Infolists\Components\TextEntry;
use Prism\Prism\Schema\ObjectSchema;
use Prism\Prism\Schema\StringSchema;

class WarrantyClaimRejectionMessageWriterTask extends AiTaskProcessor
{
    protected function objectSchema(): ObjectSchema
    {
        return new ObjectSchema(
            name: 'warranty_claim_rejection',
            description: 'A structured rejection message for a warranty claim',
            properties: [
                new StringSchema('message', 'The rejection message to send to the customer'),
            ],
            requiredFields: ['message']
        );
    }

    protected function userPrompt(): string
    {
        return sprintf(
            "Fault Type: %s\nFault Description: %s\nRejection Reason: %s\nRejection Notes: %s",
            $this->claim()->faultType->name,
            $this->claim()->fault_description,
            $this->claim()->rejection->reason,
            $this->claim()->rejection->notes,
        );
    }

    protected function systemPrompt(): string
    {
        return $this->systemPrompt;
    }

    public function getInfolistSchema(): array
    {
        return [
            TextEntry::make('message')
                ->separator(PHP_EOL)
                ->listWithLineBreaks(),
        ];
    }

    private function claim(): Claim
    {
        return $this->subject;
    }
}
