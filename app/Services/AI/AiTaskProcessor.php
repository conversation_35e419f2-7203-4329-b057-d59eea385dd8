<?php

namespace App\Services\AI;

use Illuminate\Database\Eloquent\Model;
use Prism\Prism\Enums\Provider;
use Prism\Prism\Prism;
use Prism\Prism\Schema\ObjectSchema;
use Prism\Prism\ValueObjects\Messages\UserMessage;

abstract class AiTaskProcessor
{
    public function __construct(
        protected string $aiModel,
        protected string $systemPrompt,
        protected Model $subject,
    ) {}

    public function getProvider(): Provider
    {
        return match (true) {
            str_starts_with($this->aiModel, 'gpt-') => Provider::OpenAI,
            str_starts_with($this->aiModel, 'o1') => Provider::OpenAI,
            str_starts_with($this->aiModel, 'o3') => Provider::OpenAI,
            str_starts_with($this->aiModel, 'claude-') => Provider::Anthropic,
            str_starts_with($this->aiModel, 'mistral-') => Provider::Mistral,
            str_starts_with($this->aiModel, 'groq-') => Provider::Groq,
            str_starts_with($this->aiModel, 'deepseek-') => Provider::DeepSeek,
            str_starts_with($this->aiModel, 'gemini-') => Provider::Gemini,
            str_starts_with($this->aiModel, 'xai-') => Provider::XAI,
            default => throw new \InvalidArgumentException("Unsupported AI model: {$this->aiModel}")
        };
    }

    public function getResponse(): AIProviderResponse
    {
        $startTime = hrtime(true);

        $result = Prism::structured()
            ->using($this->getProvider(), $this->aiModel)
            ->withSystemPrompt($this->systemPrompt())
            ->withMessages($this->messages())
            ->withSchema($this->objectSchema())
            ->asStructured();

        $processingMs = (hrtime(true) - $startTime) / 1000000;

        return new AIProviderResponse(
            aiModel: $this->aiModel,
            content: json_decode($result->responseMessages->last()->content ?? [], true),
            finishReason: match ($result->finishReason) {
                \Prism\Prism\Enums\FinishReason::Stop => 'stop',
                \Prism\Prism\Enums\FinishReason::Length => 'length',
                \Prism\Prism\Enums\FinishReason::ContentFilter => 'content_filter',
                \Prism\Prism\Enums\FinishReason::ToolCalls => 'tool_calls',
                \Prism\Prism\Enums\FinishReason::Error => 'error',
                \Prism\Prism\Enums\FinishReason::Other => 'other',
                \Prism\Prism\Enums\FinishReason::Unknown => 'unknown',
                default => 'unknown'
            },
            promptTokens: $result->usage->promptTokens,
            completionTokens: $result->usage->completionTokens,
            processingMs: (int) $processingMs,
        );
    }

    protected function messages(): array
    {
        return [
            new UserMessage($this->userPrompt()),
        ];
    }

    protected function systemPrompt(): string
    {
        return $this->systemPrompt;
    }

    abstract protected function userPrompt(): string;

    abstract public function getInfolistSchema(): array;

    abstract protected function objectSchema(): ObjectSchema;
}
