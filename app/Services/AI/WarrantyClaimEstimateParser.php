<?php

namespace App\Services\AI;

use App\Enums\ClaimEstimateLineItemType;
use App\Models\File;
use App\Models\InboundEmail;
use App\Models\VehicleComponent;
use Prism\Prism\Schema\ArraySchema;
use Prism\Prism\Schema\BooleanSchema;
use Prism\Prism\Schema\EnumSchema;
use Prism\Prism\Schema\NumberSchema;
use Prism\Prism\Schema\ObjectSchema;
use Prism\Prism\Schema\StringSchema;
use Prism\Prism\ValueObjects\Messages\Support\Document;
use Prism\Prism\ValueObjects\Messages\UserMessage;
use ZBateson\MailMimeParser\Message;

class WarrantyClaimEstimateParser extends AiTaskProcessor
{
    private string $userPrompt = 'Here is the file:';

    protected function objectSchema(): ObjectSchema
    {
        return new ObjectSchema(
            name: 'items',
            description: 'A collection of repair estimates or invoices',
            properties: [
                new ArraySchema(
                    name: 'items',
                    description: 'A collection of repair estimates or invoices',
                    items: new ObjectSchema(
                        name: 'item',
                        description: 'A single repair estimate or invoice document',
                        properties: [
                            new EnumSchema('type', 'Estimate or Invoice', ['ESTIMATE', 'INVOICE']),
                            new StringSchema('timestamp', 'Invoice or estimate date as ISO 8601 timestamp'),
                            new StringSchema('document_reference', 'Invoice number or estimate number or document reference'),
                            new StringSchema('name', 'Workshop or repairer business name'),
                            new StringSchema('contact', 'Workshop or repairer contact name'),
                            new StringSchema('phone', 'Workshop or repairer phone number'),
                            new StringSchema('email', 'Workshop or repairer email address'),
                            new StringSchema('website', 'Workshop or repairer website URL'),
                            new StringSchema('vat_number', 'VAT number of the workshop or repairer'),
                            new StringSchema('company_number', 'Company number of the workshop or repairer'),
                            new StringSchema('bank_sort_code', 'Sort code of the workshop or repairer bank account'),
                            new StringSchema('bank_account_number', 'Account number of the workshop or repairer bank account'),
                            new ObjectSchema('postal_address', 'Postal address of the vehicle owner', [
                                new StringSchema('line_1', 'Line 1 of the address'),
                                new StringSchema('line_2', 'Line 2 of the address'),
                                new StringSchema('city', 'City of the address'),
                                new StringSchema('county', 'County of the address'),
                                new StringSchema('country', 'Country of the address'),
                                new StringSchema('postcode', 'Postcode of the address'),
                            ], requiredFields: ['line_1', 'city', 'county', 'country', 'postcode']),

                            new StringSchema('work_required', 'Description of work required'),
                            new StringSchema('vrm', 'VRM or registration number of vehicle'),
                            new StringSchema('vin', 'VIN or chassis number of vehicle'),
                            new StringSchema('vehicle_make', 'Manufacturer of the vehicle'),
                            new StringSchema('vehicle_model', 'Model of the vehicle'),
                            new StringSchema('vehicle_year', 'Year of the vehicle'),
                            new StringSchema('vehicle_engine_size', 'Engine size of the vehicle'),
                            new NumberSchema('mileage', 'Mileage of vehicle when presented for estimate'),
                            new NumberSchema('subtotal', 'Costs net of VAT'),
                            new NumberSchema('vat', 'Total VAT'),
                            new NumberSchema('total', 'Total cost of the estimate including VAT'),

                            new ArraySchema('line_items', 'Collection of line items for the estimate',
                                new ObjectSchema('line_item', 'Single line item',
                                    properties: [
                                        new EnumSchema('type', 'Type of line item', [
                                            ClaimEstimateLineItemType::PART->value,
                                            ClaimEstimateLineItemType::LABOUR->value,
                                            ClaimEstimateLineItemType::CONSUMABLE->value,
                                        ]),
                                        new EnumSchema('vehicle_component', 'Match to this list if possible. If not possible, leave null.', VehicleComponent::pluck('name')->all()),
                                        new StringSchema('sku', 'SKU or part number'),
                                        new StringSchema('description', 'Description of the line item'),
                                        new NumberSchema('quantity', 'Quantity of the line item'),
                                        new NumberSchema('unit_amount', 'Unit amount of the line item'),
                                        new BooleanSchema('vat_charged', 'is VAT charged on this line item?'),
                                        new NumberSchema('amount', 'Total amount of the line item'),
                                    ],
                                    requiredFields: ['description', 'quantity', 'unit_amount', 'amount', 'vat_charged']
                                ),
                            ),
                        ],
                        requiredFields: ['type', 'timestamp', 'work_required', 'vrm', 'vin', 'vehicle_make', 'vehicle_model', 'vehicle_year', 'vehicle_engine_size', 'mileage', 'subtotal', 'vat', 'total', 'line_items']
                    )
                ),
            ], requiredFields: ['items']
        );
    }

    protected function userPrompt(): string
    {
        return $this->userPrompt;
    }

    protected function systemPrompt(): string
    {
        return $this->systemPrompt;
    }

    public function getInfolistSchema(): array
    {
        return [

        ];
    }

    protected function messages(): array
    {
        if ($this->getSubject() instanceof File) {
            return $this->parseFile($this->getSubject());
        }

        if ($this->getSubject() instanceof InboundEmail) {
            if (
                $file = $this->getSubject()->attachments()
                    ->whereIn('mime_type', [
                        'application/pdf',
                        'message/rfc822',
                    ])
                    ->first()
            ) {
                return $this->parseFile($file);
            }
        }

        return $this->parseEmailBody();
    }

    protected function parseFile(File $file): array
    {
        return match ($file->mime_type) {
            'application/pdf' => $this->parsePdfFile($file),
            'message/rfc822' => $this->parseEmlFile($file),
            default => throw new \RuntimeException('Unsupported file type'),
        };
    }

    protected function parsePdfFile(File $file): array
    {
        $content = $file->getBinaryContent();

        return [
            new UserMessage($this->userPrompt(), [
                Document::fromBase64(base64_encode($content), $file->mime_type),
            ]),
        ];
    }

    protected function parseEmlFile(File $file): array
    {
        return $this->parseMimeMessage($file->getBinaryContent());
    }

    protected function parseEmailBody()
    {
        return $this->parseMimeMessage($this->getSubject()->getContent());
    }

    protected function parseMimeMessage(string $content): array
    {
        $message = Message::from($content, attached: false);

        $content = $message->getHtmlContent() ?: $message->getTextContent();

        if (empty($content)) {
            throw new \RuntimeException('No content found in email');
        }

        return [
            new UserMessage($this->userPrompt(), [
                Document::fromText($content),
            ]),
        ];
    }

    private function getSubject(): InboundEmail|File
    {
        return $this->subject;
    }
}
