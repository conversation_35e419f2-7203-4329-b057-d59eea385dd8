<?php

namespace App\Services\Payments\PayLater;

use Carbon\Carbon;

final readonly class PlanBreakdownData
{
    public function __construct(
        public string $name,
        public string $amount,
        public string $interest,
        public string $repayable,
        public array $schedule,
    ) {}

    public function forHumans(): string
    {
        if (! count($this->schedule)) {
            return 'This loan is not repayable.';
        }

        if ($this->schedule[0]->amount == $this->schedule[1]->amount) {
            return sprintf(
                'This loan comprises of %d monthly payment%s of £%s, starting on %s, and a final payment of £%s.',
                count($this->schedule),
                count($this->schedule) === 1 ? '' : 's',
                number_format($this->schedule[0]->amount, 2),
                Carbon::parse($this->schedule[0]->date)->format('d/m/Y'),
                number_format($this->schedule[count($this->schedule) - 1]->amount, 2),
            );
        }

        return sprintf(
            'This loan comprises of an initial deposit of £%s, followed by %s monthly payment%s of £%s.',
            number_format($this->schedule[0]->amount, 2),
            count($this->schedule) - 1,
            count($this->schedule) - 1 === 1 ? '' : 's',
            number_format($this->schedule[1]->amount, 2),
        );
    }
}
