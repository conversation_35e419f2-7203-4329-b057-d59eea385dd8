<?php

namespace App\Services\Payments\DirectDebit;

use App\Enums\PaymentProvider;
use App\Models\Concerns\BillableContract;
use App\Models\Payment;

interface DirectDebitPaymentProcessor
{
    public function getProcessorIdentifier(): PaymentProvider;

    public function setupBillingRequestForCustomer(BillableContract $directDebitable): BillingRequestData;

    public function getMandate(string $mandateId): MandateData;

    public function createPayment(Payment $payment): PaymentData;

    public function getPayment(Payment $payment): PaymentData;

    public function cancelPayment(Payment $payment): void;
}
