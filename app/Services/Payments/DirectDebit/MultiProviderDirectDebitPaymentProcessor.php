<?php

namespace App\Services\Payments\DirectDebit;

use App\Enums\PaymentProvider;
use App\Models\Concerns\BillableContract;
use App\Models\Payment;

class MultiProviderDirectDebitPaymentProcessor implements DirectDebitPaymentProcessor
{
    private ?DirectDebitPaymentProcessor $selectedProcessor = null;

    public function __construct(private readonly array $config, private readonly array $processors) {}

    public function selectProcessor(PaymentProvider|string $paymentProcessor): DirectDebitPaymentProcessor
    {
        if (is_string($paymentProcessor)) {
            $paymentProcessor = PaymentProvider::from($paymentProcessor);
        }

        return $this->selectedProcessor = array_find($this->processors, fn (DirectDebitPaymentProcessor $processor) => $processor->getProcessorIdentifier() === $paymentProcessor);
    }

    public function defaultProcessor(): DirectDebitPaymentProcessor
    {
        return $this->selectProcessor($this->config['default']);
    }

    public function selectedProcessor(): ?DirectDebitPaymentProcessor
    {
        return $this->selectedProcessor;
    }

    public function getProcessorIdentifier(): PaymentProvider
    {
        return $this->defaultProcessor()->getProcessorIdentifier();
    }

    public function getMandate(string $mandateId): MandateData
    {
        return $this->selectedProcessor()->getMandate($mandateId);
    }

    public function createPayment(Payment $payment): PaymentData
    {
        return $this->selectProcessor($payment->provider)->createPayment($payment);
    }

    public function getPayment(Payment $payment): PaymentData
    {
        return $this->selectProcessor($payment->provider)->getPayment($payment);
    }

    public function cancelPayment(Payment $payment): void
    {
        $this->selectProcessor($payment->provider)->cancelPayment($payment);
    }

    public function setupBillingRequestForCustomer(BillableContract $directDebitable): BillingRequestData
    {
        return $this->defaultProcessor()->setupBillingRequestForCustomer($directDebitable);
    }
}
