<?php

namespace App\Services\Payments\DirectDebit;

use App\Enums\PaymentProvider;
use App\Models\BillingRequest;
use App\Models\Concerns\BillableContract;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class AccessPaysuiteDirectDebitPaymentProcessor implements DirectDebitPaymentProcessor
{
    public function __construct(protected array $config) {}

    protected function urlEndpoint(string $uri): string
    {
        return sprintf(
            '%s/api/v3/client/%s/%s',
            match ($this->config['environment']) {
                'playpen' => 'https://playpen.accesspaysuite.com',
                'live' => 'https://ddcms.accesspaysuite.com',
            },
            $this->config['client_code'],
            $uri
        );
    }

    public function getClientInfo()
    {
        return $this->sendRequest('info');
    }

    public function getWebhookUrl(string $entity)
    {
        return $this->sendRequest("BACS/{$entity}/callback");
    }

    public function setWebhookUrl(string $entity, string $url)
    {
        return $this->sendRequest("BACS/{$entity}/callback", 'POST', [
            'query' => [
                'url' => $url,
            ],
        ]);
    }

    public function deleteWebhookUrl(string $entity)
    {
        return $this->sendRequest("BACS/{$entity}/callback", 'DELETE');
    }

    /**
     * @throws RequestException|\Illuminate\Http\Client\ConnectionException
     */
    public function sendRequest(string $uri, string $method = 'GET', array $options = [])
    {
        try {
            return Http::acceptJson()
                ->asJson()
                ->withHeaders([
                    'apiKey' => $this->config['api_key'],
                ])
                ->throw()
                ->send($method, $this->urlEndpoint($uri), $options)
                ->json();
        } catch (RequestException $e) {
            if (app()->isLocal()) {
                dd($this->config, $method, $this->urlEndpoint($uri), $options, $e->response->body());
            }
            throw $e;
        }
    }

    public function addCustomer(CustomerData $customerData, DirectDebitData $directDebitData): CustomerData
    {
        $names = array_reverse(explode(' ', $directDebitData->accountHolderName));

        [$lastName, $firstName] = [
            $names[0],
            implode(' ', array_slice($names, 1)),
        ];

        $result = $this->sendRequest('customer', 'POST', [
            'json' => [
                'Email' => $customerData->email,
                'CustomerRef' => $customerData->reference.'-'.time(),
                'FirstName' => $firstName,
                'Surname' => $lastName,
                'Line1' => $customerData->line1,
                'Line2' => $customerData->line2,
                'Line3' => $customerData->city,
                'Line4' => $customerData->county,
                'PostCode' => $customerData->postcode,
                'MobilePhoneNumber' => $customerData->phone,
                'BankSortCode' => $directDebitData->sortCode,
                'AccountNumber' => $directDebitData->accountNumber,
                'AccountHolderName' => $directDebitData->accountHolderName,
            ],
        ]);

        $customerData->paymentProcessorId = $result['Id'];

        return $customerData;
    }

    public function getPaymentNextDate($isFirstPayment = false): Carbon
    {
        $nextPaymentDates = $this->sendRequest('paymentdate');

        return Carbon::parse(match ($isFirstPayment) {
            true => $nextPaymentDates['NextFirstPaymentDate'],
            false => $nextPaymentDates['NextSubsequentPaymentDate'],
        })->addDays(Carbon::now()->hour >= 12 ? 1 : 0);
    }

    public function setupMandate(BillableContract $directDebitable, DirectDebitData $directDebitData): MandateData
    {
        $customerData = $this->addCustomer($directDebitable->toCustomerData(), $directDebitData);

        $nextPayment = $this->getPaymentNextDate(true);

        $contractCreated = $this->sendRequest("customer/{$customerData->paymentProcessorId}/contract", 'POST', [
            'json' => [
                'ScheduleName' => 'Default Schedule',
                'Start' => $nextPayment,
                'IsGiftAid' => false,
                'TerminationType' => 'Until further notice',
                'AtTheEnd' => 'Switch to further notice',
            ],
        ]);

        $contracts = $this->sendRequest("customer/{$customerData->paymentProcessorId}/contract")['Contracts'];

        $contract = collect($contracts)->where('Id', $contractCreated['Id'])->first();

        return new MandateData(
            status: $contract['Status'],
            paymentProcessorMandateId: $contract['Id'],
            paymentProcessorCustomerId: $customerData->paymentProcessorId,
            directDebitReference: $contract['DirectDebitReference'],
            activatedAt: $contract['Start'],
        );
    }

    public function getMandate(string $mandateId): MandateData
    {
        $customerId = BillingRequest::firstWhere('mandate_id', $mandateId)?->provider_customer_id;

        if (! $customerId) {
            throw new \RuntimeException('Database billing request not found');
        }

        $contracts = $this->sendRequest("customer/{$customerId}/contract")['Contracts'];

        $contract = collect($contracts)->firstWhere('Id', $mandateId);

        return new MandateData(
            status: $contract['Status'],
            paymentProcessorMandateId: $contract['Id'],
            paymentProcessorCustomerId: $customerId,
            directDebitReference: $contract['DirectDebitReference'],
            activatedAt: $contract['Start'],
        );
    }

    public function createPayment(Payment $payment): PaymentData
    {
        $chargeDate = max([
            $payment->billingRequest->mandate_activated_at,
            $payment->charge_date,
        ]);

        try {
            $processorPayment = $this->sendRequest("contract/{$payment->billingRequest->mandate_id}/payment", 'POST', [
                'json' => [
                    'Amount' => $payment->amount,
                    'Comment' => "Payment ID: {$payment->getKey()}",
                    'Date' => $chargeDate,
                    'IsCredit' => false,
                ],
            ]);
        } catch (RequestException $e) {
            throw new PaymentException($e->response->json('Detail') ?: $e->getMessage());
        }

        return PaymentData::fromAccessPaysuitePayment($processorPayment);
    }

    public function getPayment(Payment $payment): PaymentData
    {
        $processorPayment = $this->sendRequest("contract/{$payment->billingRequest->mandate_id}/payment/{$payment->processor_payment_id}");

        return PaymentData::fromAccessPaysuitePayment($processorPayment);
    }

    public function cancelPayment(Payment $payment): void
    {
        $this->sendRequest(
            uri: "contract/{$payment->billingRequest->mandate_id}/payment/{$payment->processor_payment_id}",
            method: 'DELETE',
            options: [
                'query' => ['comment' => 'Payment cancelled by '.auth()->user()->name],
            ]
        );
    }

    public function getProcessorIdentifier(): PaymentProvider
    {
        return PaymentProvider::ACCESS_PAYSUITE;
    }

    public function setupBillingRequestForCustomer(BillableContract $directDebitable): BillingRequestData
    {
        $billingRequestId = Str::uuid();

        return new BillingRequestData(
            paymentProcessorBillingRequestId: $billingRequestId,
            paymentProcessorCustomerId: null,
            authorisationUrl: route('payment.direct-debit-setup', $billingRequestId),
            urlExpiresAt: now()->addWeek(),
            status: 'pending',
        );
    }
}
