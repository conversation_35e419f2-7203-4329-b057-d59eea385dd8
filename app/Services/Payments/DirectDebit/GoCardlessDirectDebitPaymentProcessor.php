<?php

namespace App\Services\Payments\DirectDebit;

use App\Enums\PaymentProvider;
use App\Models\Concerns\BillableContract;
use App\Models\Payment;
use GoCardlessPro\Client;
use GoCardlessPro\Core\Exception\InvalidStateException;

class GoCardlessDirectDebitPaymentProcessor implements DirectDebitPaymentProcessor
{
    private Client $client;

    public function __construct(private array $config)
    {
        $this->client = new \GoCardlessPro\Client([
            'access_token' => $this->config['access_token'],
            'environment' => $this->config['environment'],
        ]);
    }

    public function setupBillingRequestForCustomer(BillableContract $directDebitable): BillingRequestData
    {
        $billingRequest = $this->client->billingRequests()->create([
            'params' => [
                'mandate_request' => [
                    'currency' => 'GBP',
                    'scheme' => 'bacs',
                ],
                'metadata' => $directDebitable->metaDataForPaymentProvider(),
            ],
        ]);

        $billable = $directDebitable->toCustomerData();

        $billingRequestFlow = $this->client->billingRequestFlows()->create([
            'params' => [
                'prefilled_customer' => array_filter([
                    'given_name' => $billable->firstName,
                    'family_name' => $billable->lastName,
                    'email' => $billable->email,

                    'address_line1' => $billable->line1,
                    'address_line2' => $billable->line2,
                    'city' => $billable->city,
                    'region' => $billable->county,
                    'postal_code' => $billable->postcode,
                    'country_code' => 'GB',
                ]),
                'links' => [
                    'customerData' => $billingRequest->links->customer,
                    'billing_request' => $billingRequest->id,
                ],
            ],
        ]);

        return new BillingRequestData(
            paymentProcessorBillingRequestId: $billingRequest->id,
            paymentProcessorCustomerId: $billingRequest->links->customer,
            authorisationUrl: $billingRequestFlow->authorisation_url,
            urlExpiresAt: $billingRequestFlow->expires_at,
            status: $billingRequest->status,
        );
    }

    public function getBillingRequest($billingRequestId)
    {
        return $this->client->billingRequests()->get($billingRequestId);
    }

    public function getMandate($mandateId): MandateData
    {
        $goCardlessMandate = $this->client->mandates()->get($mandateId);

        return new MandateData(
            status: $goCardlessMandate->status,
            paymentProcessorMandateId: $goCardlessMandate->id,
            paymentProcessorCustomerId: $goCardlessMandate->links->customer,
            directDebitReference: $goCardlessMandate->reference,
            activatedAt: $goCardlessMandate->created_at,
        );
    }

    public function createPayment(Payment $payment): PaymentData
    {
        if ($payment->processor_payment_id) {
            throw new \RuntimeException('Payment has already been sent to GoCardless.');
        }

        try {
            $gcPayment = $this->client->payments()->create([
                'params' => [
                    'charge_date' => $payment->charge_date?->toDateString(),
                    'retry_if_possible' => true,
                    'amount' => (int) round($payment->amount * 100),
                    'currency' => 'GBP',
                    'links' => [
                        'mandate' => $payment->billingRequest->mandate_id,
                    ],
                    'metadata' => [
                        'payment_id' => (string) $payment->id,
                        'payable_type' => (string) $payment->payable_type,
                        'payable_id' => (string) $payment->payable_id,
                    ],
                ],
            ]);
        } catch (InvalidStateException $e) {
            throw new PaymentException($e->getMessage());
        }

        return PaymentData::fromGoCardlessPayment($gcPayment);
    }

    public function cancelPayment(Payment $payment): void
    {
        $this->client->payments()->cancel($payment->processor_payment_id);
    }

    public function getPayment(Payment $payment): PaymentData
    {
        $gcPayment = $this->client->payments()->get($payment->processor_payment_id);

        return PaymentData::fromGoCardlessPayment($gcPayment);
    }

    public function getPayout($payoutId)
    {
        return $this->client->payouts()->get($payoutId);
    }

    public function getPayoutItems($payoutId)
    {
        return $this->client->payoutItems()->list([
            'params' => [
                'payout' => $payoutId,
            ],
        ]);
    }

    public function getClient()
    {
        return $this->client;
    }

    public function getProcessorIdentifier(): PaymentProvider
    {
        return PaymentProvider::GO_CARDLESS;
    }
}
