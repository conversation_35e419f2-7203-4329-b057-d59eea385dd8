<?php

namespace App\Services\Payments\DirectDebit;

use App\Enums\PaymentProvider;
use GoCardlessPro\Resources\Payment;

final class PaymentData
{
    public function __construct(
        public PaymentProvider $provider,
        public ?string $providerPaymentId,
        public ?string $chargeDate,
        public ?string $status,
        public ?string $amount,
    ) {}

    public static function fromGoCardlessPayment(Payment $payment)
    {
        return new self(
            provider: PaymentProvider::GO_CARDLESS,
            providerPaymentId: $payment->id,
            chargeDate: $payment->charge_date,
            status: $payment->status,
            amount: $payment->amount,
        );
    }

    public static function fromAccessPaysuitePayment(array $payment)
    {
        return new self(
            provider: PaymentProvider::ACCESS_PAYSUITE,
            providerPaymentId: $payment['Id'],
            chargeDate: $payment['Date'] ?? $payment['DueDate'],
            status: str_replace(' ', '_', strtolower($payment['Status'] ?? \App\Models\Payment::STATUS_PENDING)),
            amount: $payment['Amount'],
        );
    }
}
