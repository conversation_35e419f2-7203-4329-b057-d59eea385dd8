<?php

namespace App\Services\Reports;

use App\Models\Account;
use App\Models\Payment;
use App\Models\PaymentLineItem;
use App\Models\Warranty;
use Illuminate\Support\Facades\DB;

class WarrantyReports
{
    public function dashboard(?Account $account = null, $filters = []): array
    {
        return [
            'total' => [
                'sales' => $sales = $this->provision($account, $filters),
                'authorised_claims' => $authorisations = $this->authorisedClaims($account, $filters),
                'margin' => ['value' => round($sales->value - $authorisations->value, 2)],
            ],
            'current' => [
                'sales' => $sales = $this->provision($account, $filters + ['expired' => false]),
                'authorised_claims' => $authorisations = $this->authorisedClaims($account, $filters + ['expired' => false]),
                'margin' => ['value' => round($sales->value - $authorisations->value, 2)],
            ],
            'expired' => [
                'sales' => $sales = $this->provision($account, $filters + ['expired' => true]),
                'authorised_claims' => $authorisations = $this->authorisedClaims($account, $filters + ['expired' => true]),
                'margin' => ['value' => round($sales->value - $authorisations->value, 2)],
            ],
            '30_days' => [
                'sales' => $sales = $this->provision($account, $filters + [
                    'dates' => [today()->subDays(30), today()->subDay()],
                ]),
                'authorised_claims' => $authorisations = $this->authorisedClaims($account, $filters + [
                    'dates' => [today()->subDays(30), today()->subDay()],
                ]),
                'margin' => ['value' => round($sales->value - $authorisations->value, 2)],
            ],
            '30_days_previous' => [
                'sales' => $sales = $this->provision($account, $filters + [
                    'dates' => [today()->subDays(60), today()->subDays(31)],
                ]),
                'authorised_claims' => $authorisations = $this->authorisedClaims($account, $filters + [
                    'dates' => [today()->subDays(60), today()->subDays(31)],
                ]),
                'margin' => ['value' => round($sales->value - $authorisations->value, 2)],
            ],
        ];
    }

    public function provision(?Account $account = null, $filters = [])
    {
        $subQuery = Warranty::query()
            ->notCancelled()
            ->withoutGlobalScope('tenant')
            ->selectRaw('SUM(provision) as initial, sale_id')
            ->addSelect([
                'recurring' => PaymentLineItem::query()
                    ->selectRaw('SUM(unit_amount)')
                    ->whereColumn('payable_id', 'warranties.id')
                    ->where('payable_type', 'WARRANTY')
                    ->whereHas('payment', fn ($q) => $q->where('status', Payment::STATUS_PAID_OUT)),
            ])
            ->groupBy('sale_id', 'id')
            ->when($account, fn ($q) => $q->where('account_id', $account->id))
            ->when($filters['dates'] ?? false, fn ($q, $dates) => $q
                ->whereDate('warranties.created_at', '>=', $dates[0])
                ->whereDate('warranties.created_at', '<=', $dates[1])
            )
            ->when(isset($filters['selfFunded']), fn ($q) => $q->dealerFunded($filters['selfFunded']))
            ->when(isset($filters['expired']), fn ($q) => $q->expired($filters['expired']));

        return DB::table(DB::raw("({$subQuery->toSql()}) as sub"))
            ->mergeBindings($subQuery->getQuery())
            ->selectRaw('SUM(initial + COALESCE(recurring, 0)) as value')
            ->selectRaw('SUM(initial) as initial')
            ->selectRaw('SUM(recurring) as recurring')
            ->selectRaw('COUNT(*) as count')
            ->first();
    }

    public function authorisedClaims(?Account $account = null, $filters = [])
    {
        return Warranty::query()
            ->notCancelled()
            ->withoutGlobalScope('tenant')
            ->selectRaw('SUM(authorised_net) as value')
            ->selectRaw('COUNT(*) as count')
            ->join('claims', 'claims.warranty_id', '=', 'warranties.id')
            ->join('claim_estimates', 'claim_estimates.claim_id', '=', 'claims.id')
            ->join('claim_authorisations', 'claim_authorisations.estimate_id', '=', 'claim_estimates.id')
            ->when($account, fn ($q) => $q->where('account_id', $account->id))
            ->when($filters['dates'] ?? false, fn ($q, $dates) => $q
                ->whereDate('claim_authorisations.created_at', '>=', $dates[0])
                ->whereDate('claim_authorisations.created_at', '<=', $dates[1])
            )
            ->when(isset($filters['selfFunded']), fn ($q) => $q->dealerFunded($filters['selfFunded']))
            ->when(isset($filters['expired']), fn ($q) => $q->expired($filters['expired']))
            ->first();
    }
}
