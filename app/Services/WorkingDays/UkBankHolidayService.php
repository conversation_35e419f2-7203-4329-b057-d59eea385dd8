<?php

namespace App\Services\WorkingDays;

use Illuminate\Cache\CacheManager;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;

class UkBankHolidayService
{
    public function __construct(
        private CacheManager $cache,
        private array $config
    ) {}

    /**
     * Get UK bank holidays for England and Wales
     *
     * @return Collection<Carbon>
     */
    public function getBankHolidays(): Collection
    {
        return $this->cache->remember(
            $this->getCacheKey(),
            $this->config['cache_ttl'],
            fn () => $this->fetchBankHolidays()
        );
    }

    /**
     * Check if a given date is a UK bank holiday
     */
    public function isBankHoliday(Carbon $date): bool
    {
        $bankHolidays = $this->getBankHolidays();

        return $bankHolidays->contains(function (Carbon $holiday) use ($date) {
            return $holiday->isSameDay($date);
        });
    }

    /**
     * Fetch bank holidays from the UK government API
     *
     * @return Collection<Carbon>
     */
    private function fetchBankHolidays(): Collection
    {
        try {
            $response = Http::timeout(10)
                ->retry(3, 1000)
                ->get($this->config['url'])
                ->throw();

            $data = $response->json();

            if (! isset($data['england-and-wales']['events'])) {
                throw new \RuntimeException('Invalid response format from UK bank holidays API');
            }

            return collect($data['england-and-wales']['events'])
                ->map(fn ($event) => Carbon::parse($event['date']))
                ->sort()
                ->values();

        } catch (RequestException $e) {
            // Log the error but don't fail completely - return empty collection
            // This allows the service to continue working even if the API is down
            \Log::warning('Failed to fetch UK bank holidays', [
                'error' => $e->getMessage(),
                'url' => $this->config['url'],
            ]);

            return collect();
        }
    }

    /**
     * Get the cache key for bank holidays
     */
    private function getCacheKey(): string
    {
        return 'uk_bank_holidays_england_wales';
    }

    /**
     * Clear the bank holidays cache
     */
    public function clearCache(): void
    {
        $this->cache->forget($this->getCacheKey());
    }
}
