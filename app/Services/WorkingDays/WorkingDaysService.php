<?php

namespace App\Services\WorkingDays;

use Illuminate\Support\Carbon;

class WorkingDaysService
{
    public function __construct(
        private UkBankHolidayService $bankHolidayService
    ) {}

    /**
     * Calculate the date that is n working days from the given date
     *
     * @param  int  $workingDays  Number of working days to add
     * @param  Carbon|null  $startDate  The starting date (null for current date)
     * @return Carbon The calculated date
     */
    public function addWorkingDays(int $workingDays, ?Carbon $startDate = null): Carbon
    {
        if ($workingDays < 0) {
            throw new \InvalidArgumentException('Working days must be a non-negative integer');
        }

        $date = $startDate ? $startDate->copy() : Carbon::now();
        $remainingDays = $workingDays;

        while ($remainingDays > 0) {
            $date->addDay();

            if ($this->isWorkingDay($date)) {
                $remainingDays--;
            }
        }

        return $date;
    }

    /**
     * Calculate the date that is n working days before the given date
     *
     * @param  int  $workingDays  Number of working days to subtract
     * @param  Carbon|null  $startDate  The starting date (null for current date)
     * @return Carbon The calculated date
     */
    public function subtractWorkingDays(int $workingDays, ?Carbon $startDate = null): Carbon
    {
        if ($workingDays < 0) {
            throw new \InvalidArgumentException('Working days must be a non-negative integer');
        }

        $date = $startDate ? $startDate->copy() : Carbon::now();
        $remainingDays = $workingDays;

        while ($remainingDays > 0) {
            $date->subDay();

            if ($this->isWorkingDay($date)) {
                $remainingDays--;
            }
        }

        return $date;
    }

    /**
     * Check if a given date is a working day
     * Working days are Monday-Friday, excluding UK bank holidays
     */
    public function isWorkingDay(Carbon $date): bool
    {
        // Check if it's a weekend (Saturday = 6, Sunday = 0)
        if ($date->isWeekend()) {
            return false;
        }

        // Check if it's a UK bank holiday
        if ($this->bankHolidayService->isBankHoliday($date)) {
            return false;
        }

        return true;
    }

    /**
     * Count the number of working days between two dates (exclusive)
     *
     * @return int Number of working days between the dates
     */
    public function countWorkingDaysBetween(Carbon $startDate, Carbon $endDate): int
    {
        if ($startDate->gte($endDate)) {
            return 0;
        }

        $count = 0;
        $current = $startDate->copy()->addDay();

        while ($current->lt($endDate)) {
            if ($this->isWorkingDay($current)) {
                $count++;
            }
            $current->addDay();
        }

        return $count;
    }

    /**
     * Get the next working day from the given date
     *
     * @param  Carbon|null  $date  The starting date (null for current date)
     * @return Carbon The next working day
     */
    public function getNextWorkingDay(?Carbon $date = null): Carbon
    {
        return $this->addWorkingDays(1, $date);
    }

    /**
     * Get the previous working day from the given date
     *
     * @param  Carbon|null  $date  The starting date (null for current date)
     * @return Carbon The previous working day
     */
    public function getPreviousWorkingDay(?Carbon $date = null): Carbon
    {
        return $this->subtractWorkingDays(1, $date);
    }
}
