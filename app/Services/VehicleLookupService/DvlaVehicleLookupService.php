<?php

namespace App\Services\VehicleLookupService;

use GuzzleHttp\Client;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;

class DvlaVehicleLookupService implements VehicleLookupService
{
    protected array $config;

    const ENDPOINT = 'https://driver-vehicle-licensing.api.gov.uk/vehicle-enquiry/v1/vehicles';

    protected Client $client;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    public function lookup(string $vrm): VehicleDTO
    {
        try {
            $vehicleData = Http::withHeaders([
                'x-api-key' => $this->config['key'],
                'Content-Type' => 'application/json',
            ])->post(self::ENDPOINT, [
                'registrationNumber' => $vrm,
            ])->throw()->json();

            return new VehicleDTO(
                vrm: $vehicleData['registrationNumber'],
                make: $vehicleData['make'],
                registrationDate: $vehicleData['monthOfFirstRegistration'],
                colour: $vehicleData['colour'],
                fuelType: $vehicleData['fuelType'],
                engineCapacity: $vehicleData['engineCapacity'],
            );
        } catch (RequestException $e) {
            throw new \RuntimeException('Sorry, we could not find your vehicle.');
        }
    }
}
