<?php

namespace App\Services\VehicleLookupService;

use Exception;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class MotorSpecVehicleLookupService implements VehicleLookupService
{
    protected $endpoint;

    protected $endpoints = [
        'staging' => 'https://staging.motorspecs.com/',
        'live' => 'https://api.motorspecs.com/',
    ];

    protected $auth_code;

    public $vehicle;

    public $vrm;

    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->endpoint = Arr::get($this->endpoints, $this->config['environment']);

        $this->vehicle = collect([]);
    }

    public function checkOptionsAreSet()
    {
        $this->checkClientCredentialsSet();
        if (empty($this->auth_code)) {
            throw new Exception('Could not obtain Motorspec auth code.');
        }
    }

    protected function checkClientCredentialsSet()
    {
        if (empty($this->config['client_id'])) {
            throw new Exception('Motorspec Client ID not set.');
        }
        if (empty($this->config['client_secret'])) {
            throw new Exception('Motorspec Client secret not set.');
        }
        if (empty($this->endpoint)) {
            throw new Exception('Motorspec endpoint not set.');
        }
    }

    /**
     * @return mixed
     */
    public function getAuthCode()
    {
        $this->checkClientCredentialsSet();

        try {
            return Cache::remember('motorspec_auth_code', Carbon::now()->addDay(), function () {
                $result = $this->postRequest(
                    'oauth',
                    [
                        'grant_type' => 'client_credentials',
                        'client_id' => $this->config['client_id'],
                        'client_secret' => $this->config['client_secret'],
                    ],
                );

                return $result['access_token'];
            });
        } catch (\Exception $e) {
            Cache::forget('motorspec_auth_code');
            throw $e;
        }
    }

    public function lookup(string $vrm): VehicleDTO
    {
        $this->auth_code = $this->getAuthCode();

        try {
            $result = $this->postRequest(
                'identity-specs/lookup',
                ['registration' => $vrm],
                'application/vnd.identity-specs.v2+json'
            );
        } catch (RequestException $e) {
            $is404 = $e->getCode() === 404;
            if ($is404) {
                VehicleNotFoundException::throw();
            }
        }

        return VehicleDTO::fromMotorspecResult($result);
    }

    protected function postRequest($endpoint, $body, $contentType = 'application/json')
    {
        return Http::withHeaders([
            'Content-Type' => $contentType,
            'Accept' => $contentType,
            'Authorization' => 'Bearer '.$this->auth_code,
        ])
            ->timeout(15)
            ->throw()
            ->post($this->endpoint.$endpoint, $body)
            ->json();
    }
}
