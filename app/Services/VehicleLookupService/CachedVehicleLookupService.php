<?php

namespace App\Services\VehicleLookupService;

use Illuminate\Cache\CacheManager;

class CachedVehicleLookupService implements VehicleLookupService
{
    private array $config = [];

    private CacheManager $cache;

    private VehicleLookupService $lookupService;

    public function __construct(array $config, CacheManager $cache, VehicleLookupService $lookupService)
    {
        $this->config = $config;
        $this->cache = $cache;
        $this->lookupService = $lookupService;
    }

    public function lookup(string $vrm, $usingCache = true): VehicleDTO
    {
        $normalisedVrm = strtoupper(str_replace(' ', '', $vrm));

        if ($usingCache === false) {
            $this->cache->forget($this->cacheKey($normalisedVrm));
        }

        return $this->cache->remember(
            $this->cacheKey($normalisedVrm),
            $this->config['cache_ttl'],
            fn () => $this->lookupService->lookup($normalisedVrm)
        );
    }

    private function cacheKey(string $normalisedVrm): string
    {
        return 'vehicle_lookup_'.$normalisedVrm;
    }
}
