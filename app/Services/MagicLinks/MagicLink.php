<?php

namespace App\Services\MagicLinks;

use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;

class MagicLink
{
    public function encode(Authenticatable $authenticatable, ?string $url = null, ?Carbon $expiresAt = null): string
    {
        $provider = collect(config('auth.providers'))->search(function ($provider) use ($authenticatable) {
            return $provider['driver'] === 'eloquent' && $provider['model'] === get_class($authenticatable);
        });
        $guard = collect(config('auth.guards'))->search(function ($guard) use ($provider) {
            return $guard['provider'] === $provider;
        });

        return URL::temporarySignedRoute('customer-portal.magic-link', $expiresAt ?: now()->addMinute(), [
            'token' => encrypt(base64_encode(json_encode([
                'auth_guard' => $guard,
                'authenticatable_id' => $authenticatable->getAuthIdentifier(),
                'url' => $url,
            ]))),
        ]);
    }

    public function decode(string $token): ?string
    {
        $decoded = json_decode(base64_decode(decrypt($token)), associative: true);

        Auth::guard($decoded['auth_guard'])->loginUsingId($decoded['authenticatable_id']);

        return $decoded['url'] ?? null;
    }
}
