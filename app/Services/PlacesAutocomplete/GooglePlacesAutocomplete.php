<?php

namespace App\Services\PlacesAutocomplete;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class GooglePlacesAutocomplete
{
    const CACHE_KEY = 'google_places_autocomplete_session_token';

    public function __construct(protected array $config) {}

    public function search(string $query, string $type)
    {
        $response = Http::withHeader('X-Goog-Api-Key', $this->config['api_key'])
            ->asJson()
            ->post('https://places.googleapis.com/v1/places:autocomplete', [
                'input' => $query,
                'sessionToken' => $this->getSessionToken(),
                'includedPrimaryTypes' => [$type],
                'locationRestriction' => [
                    'rectangle' => [
                        'low' => [
                            'latitude' => 49.674,
                            'longitude' => -8.17,
                        ],
                        'high' => [
                            'latitude' => 61.061,
                            'longitude' => 1.78,
                        ],
                    ],
                ],
            ])->throw();

        return collect($response->json('suggestions.*.placePrediction'))->mapWithKeys(function ($item) {
            return [
                $item['placeId'] => $item['text']['text'],
            ];
        });
    }

    public function getPlaceDetails(string $placeId)
    {
        $result = Http::withHeader('X-Goog-Api-Key', $this->config['api_key'])
            ->asJson()
            ->get("https://places.googleapis.com/v1/places/{$placeId}", [
                'fields' => 'id,displayName,nationalPhoneNumber,formattedAddress,addressComponents,postalAddress,websiteUri',
                'sessionToken' => $this->getSessionToken(),
            ])
            ->throw()->json();

        Cache::forget($this->getCacheKey());

        return [
            'place_id' => $result['id'],
            'name' => $result['displayName']['text'],
            'phone' => $result['nationalPhoneNumber'] ?? null,
            'website' => $result['websiteUri'] ?? null,
            ...$this->mapPlaceDetailsToAddress($result['addressComponents']),
        ];
    }

    public function mapPlaceDetailsToAddress(array $addressComponents): array
    {
        $line1 = implode(', ', array_filter([
            $this->getAddressComponent($addressComponents, 'street_number'),
            $this->getAddressComponent($addressComponents, 'subpremise'),
            $this->getAddressComponent($addressComponents, 'premise'),
        ]));
        $line2 = $this->getAddressComponent($addressComponents, 'route');
        if ($line1 === '') {
            $line1 = $line2;
            $line2 = null;
        }

        return [
            'address_1' => $line1,
            'address_2' => $line2,
            'city' => $this->getAddressComponent($addressComponents, 'postal_town'),
            'county' => $this->getAddressComponent($addressComponents, 'administrative_area_level_2'),
            'country' => $this->getAddressComponent($addressComponents, 'country'),
            'postcode' => $this->getAddressComponent($addressComponents, 'postal_code'),
        ];
    }

    protected function getAddressComponent(array $addressComponents, string $key)
    {
        $item = collect($addressComponents)->first(fn ($item) => in_array($key, $item['types']));

        return $item['longText'] ?? null;
    }

    public function getSessionToken()
    {
        return Cache::remember($this->getCacheKey(), now()->addMinutes(60), fn () => Str::uuid());
    }

    protected function getCacheKey()
    {
        return self::CACHE_KEY.'.'.auth()->id();
    }
}
