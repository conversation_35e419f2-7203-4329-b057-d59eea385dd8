<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Filament\Resources\UserResource\RelationManagers\AccountsRelationManager;
use App\Models\User;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use STS\FilamentImpersonate\Pages\Actions\Impersonate;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Impersonate::make()
                ->visible(fn (User $user) => Auth::user()->canImpersonate($user))
                ->record($this->getRecord()),
            Actions\DeleteAction::make(),
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return Auth::user()->isViewingAllRecords() ? 'Edit Admin User' : 'Edit User';
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['account_id'] = auth()->user()->account_id;

        return $data;
    }

    public function getRelationManagers(): array
    {
        if (Auth::user()->isViewingAllRecords()) {
            return [];
        }

        return [
            AccountsRelationManager::class,
        ];
    }
}
