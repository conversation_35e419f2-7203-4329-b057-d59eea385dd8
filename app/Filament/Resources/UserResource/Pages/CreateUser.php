<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Models\SalesPerson;
use App\Models\User;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected bool $alsoCreateSalesPerson = false;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['account_id'] = auth()->user()->account_id;
        $this->alsoCreateSalesPerson = (bool) ($data['create_sales_person'] ?? false);
        unset($data['create_sales_person']);

        return $data;
    }

    protected function afterCreate(): void
    {
        /** @var User $user */
        $user = $this->getRecord();

        if (Auth::user()->isViewingAllRecords()) {
            $user->assignRole('Admin');

            $user->update(['is_internal' => true]);
        } else {
            $user->update(['is_internal' => false]);

            $user->accounts()->attach($user->account_id);
            if ($this->alsoCreateSalesPerson) {
                SalesPerson::create([
                    'account_id' => $user->account_id,
                    'user_id' => $user->id,
                    'name' => $user->name,
                ]);
            }
        }

        event(new Registered($user));
    }
}
