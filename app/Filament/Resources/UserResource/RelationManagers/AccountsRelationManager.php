<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;

class AccountsRelationManager extends RelationManager
{
    protected static string $relationship = 'accounts';

    protected function canAttach(): bool
    {
        return Auth::user()->isViewingAllRecords() === false;
    }

    protected function canDetachAny(): bool
    {
        return Auth::user()->isViewingAllRecords() === false;
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make(),
            ])
            ->actions([
                Tables\Actions\DetachAction::make()->requiresConfirmation(),
            ]);
    }
}
