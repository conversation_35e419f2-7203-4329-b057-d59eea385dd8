<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PayoutResource\Pages;
use App\Filament\Resources\PayoutResource\RelationManagers\PayoutLineItemsRelationManager;
use App\Models\Payout;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class PayoutResource extends Resource
{
    protected static ?string $model = Payout::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('processor_payout_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('reference')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('status')
                    ->required()
                    ->maxLength(255),
                Forms\Components\DatePicker::make('arrival_date')
                    ->required(),
                Forms\Components\TextInput::make('amount')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('deducted_fees')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('accounting_software_transfer_id')
                    ->maxLength(36),
                Forms\Components\TextInput::make('accounting_software_from_transaction_id')
                    ->maxLength(36),
                Forms\Components\TextInput::make('accounting_software_to_transaction_id')
                    ->maxLength(36),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('arrival_date', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('arrival_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reference')
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->formatStateUsing(fn (Model $record) => '£'.number_format($record->amount + $record->deducted_fees, 2))
                    ->sortable(),
                Tables\Columns\TextColumn::make('deducted_fees')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->columns(2)
                    ->schema([
                        TextEntry::make('arrival_date')->date(),
                        TextEntry::make('reference'),
                    ]),
                Section::make()
                    ->maxWidth('md')
                    ->schema([
                        TextEntry::make('total')
                            ->inlineLabel()
                            ->alignRight()
                            ->getStateUsing(fn (Payout $payout): string => '£'.number_format($payout->amount + $payout->deducted_fees, 2)),
                        TextEntry::make('deducted_fees')
                            ->inlineLabel()
                            ->alignRight()
                            ->money(),
                        TextEntry::make('amount')
                            ->inlineLabel()
                            ->alignRight()
                            ->label('Net Received')
                            ->money(),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            PayoutLineItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayouts::route('/'),
            'view' => Pages\ViewPayout::route('/{record}'),
        ];
    }
}
