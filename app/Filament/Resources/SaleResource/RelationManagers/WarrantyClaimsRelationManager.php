<?php

namespace App\Filament\Resources\SaleResource\RelationManagers;

use App\Enums\ClaimStatus;
use App\Enums\ProductStatus;
use App\Filament\Resources\ClaimResource;
use App\Models\Account;
use App\Models\Claim;
use App\Models\Sale;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Average;
use Filament\Tables\Columns\Summarizers\Count;
use Filament\Tables\Columns\Summarizers\Range;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Gate;

class WarrantyClaimsRelationManager extends RelationManager
{
    protected static string $relationship = 'warrantyClaims';

    protected function getForms(): array
    {
        return [
            'form',
        ];
    }

    public function form(Form $form): Form
    {
        return $form->schema([Forms\Components\Section::make()
            ->columns(2)
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\DatePicker::make('failure_date')
                            ->label('Date of Failure')
                            ->helperText(function () {
                                if ($this->sale()->warranty->status === ProductStatus::EXPIRED) {
                                    return 'This warranty has expired. If the dealer has agreed to cover the cost of the repair, you can proceed with the claim. In this case, please add a note to the claim explaining the reason that the claim is being processed after the warranty has expired.';
                                }

                                return '';
                            })
                            ->date()
                            ->native(false)
                            ->displayFormat('d/m/Y')
                            ->maxDate(now())
                            ->required(),
                        Forms\Components\TextInput::make('current_mileage')
                            ->label('Mileage at Failure')
                            ->helperText(fn () => sprintf(
                                'The mileage cutoff for this warranty is %s miles (based on an annual mileage of %s miles per year)',
                                number_format($this->sale()->warranty->mileage_cutoff),
                                number_format($this->sale()->warranty->annual_mileage_limit)
                            ))
                            ->numeric()
                            ->minValue($this->sale()->delivery_mileage),
                        Forms\Components\Select::make('fault_type_id')
                            ->label('Fault Type')
                            ->relationship('faultType', 'name', fn ($query) => $query->orderBy('position'))
                            ->searchable()
                            ->preload()
                            ->required(),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Textarea::make('vehicle_location')
                            ->autosize()
                            ->required(),
                        Forms\Components\Textarea::make('fault_description')
                            ->autosize()
                            ->label('Description of Fault')
                            ->required(),
                    ]),
            ]),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('reference')
            ->columns([
                Tables\Columns\TextColumn::make('reference'),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(fn (ClaimStatus $state): string => $state->value)
                    ->badge()
                    ->color(fn (ClaimStatus $state): string => $state->color()),
                Tables\Columns\TextColumn::make('faultType.name')
                    ->label('Fault Type')
                    ->sortable(),
                Tables\Columns\TextColumn::make('failure_date')->date()->sortable(),
                Tables\Columns\TextColumn::make('current_mileage')
                    ->label('Failure Mileage')
                    ->numeric()
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\TextColumn::make('estimate_line_items_sum_net')
                    ->label('Total Authorised')
                    ->sum([
                        'estimateLineItems' => fn ($q) => $q->whereHas('estimate.authorisation'),
                    ], 'net')
                    ->money()
                    ->alignRight()
                    ->sortable()
                    ->summarize([
                        Count::make(),
                        Sum::make()->money(),
                        Average::make()->money(),
                        Range::make(),
                    ]),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make('create')
                    ->label('New warranty claim')
                    ->slideOver()
                    ->createAnother(false)
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['warranty_id'] = $this->sale()->warranty->id;

                        // unset the account array that inexplicably appears here, not sure why we have to do this
                        unset($data['account']);

                        return $data;
                    })
                    ->successRedirectUrl(fn ($record) => ClaimResource::getUrl('view', [$record])),
            ])
            ->actions([
                Tables\Actions\ViewAction::make('view_claim')->url(fn ($record) => ClaimResource::getUrl('view', [$record])),
                Tables\Actions\EditAction::make('edit_claim'),
                Tables\Actions\DeleteAction::make('delete_claim'),
            ])
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['warranty']));
    }

    protected function canCreate(): bool
    {
        return Gate::check('create', [Claim::class, $this->sale()->warranty]);
    }

    private function sale(): Sale
    {
        return $this->getOwnerRecord();
    }
}
