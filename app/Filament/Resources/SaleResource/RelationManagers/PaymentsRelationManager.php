<?php

namespace App\Filament\Resources\SaleResource\RelationManagers;

use App\Filament\Resources\PaymentResource;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class PaymentsRelationManager extends RelationManager
{
    protected static string $relationship = 'payments';

    public function form(Form $form): Form
    {
        return PaymentResource::form($form);
    }

    public function table(Table $table): Table
    {
        return $table
            ->defaultSort('period_start', 'desc')
            ->paginated(false)
            ->columns(PaymentResource::getTableColumns())
            ->recordUrl(fn ($record) => PaymentResource::getUrl('view', [$record]))
            ->actions([
                Tables\Actions\EditAction::make()->slideOver(),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        if (! $ownerRecord->isRecurring()) {
            return false;
        }

        return parent::canViewForRecord($ownerRecord, $pageClass);
    }
}
