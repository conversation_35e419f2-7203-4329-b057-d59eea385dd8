<?php

namespace App\Filament\Resources\SaleResource\RelationManagers;

use App\Enums\AuthorisedServiceStatus;
use App\Filament\Resources\SaleResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class AuthorisedServicesRelationManager extends RelationManager
{
    protected static string $relationship = 'authorisedServices';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('service_type_id')
                    ->label('Service Type')
                    ->options($this->getOwnerRecord()->servicePlan->getRemainingServiceTypes()->pluck('name', 'id'))
                    ->required(),
                Forms\Components\Select::make('status')
                    ->options(AuthorisedServiceStatus::class)
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->paginated(false)
            ->columns([
                TextColumn::make('created_at')
                    ->label('Approved at')
                    ->dateTime(),
                TextColumn::make('serviceType.name')
                    ->label('Type'),
                TextColumn::make('user.name')
                    ->label('Approved by'),
                TextColumn::make('authorisation_code'),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (AuthorisedServiceStatus $state): string => match ($state) {
                        AuthorisedServiceStatus::BOOKED => 'warning',
                        AuthorisedServiceStatus::COMPLETED => 'success',
                        AuthorisedServiceStatus::CANCELLED => 'danger',
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->slideOver()
                    ->label('Authorise Service')
                    ->createAnother(false)
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['service_plan_id'] = $this->getOwnerRecord()->servicePlan->id;

                        return $data;
                    })
                    ->visible($this->getOwnerRecord()->servicePlan->getRemainingServiceTypes()->count() > 0)
                    ->successRedirectUrl(fn () => SaleResource::getUrl('view-service-plan', ['record' => $this->getOwnerRecord()])),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ]);
    }
}
