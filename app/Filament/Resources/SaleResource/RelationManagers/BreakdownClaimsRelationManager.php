<?php

namespace App\Filament\Resources\SaleResource\RelationManagers;

use App\Filament\Resources\BreakdownClaimResource;
use App\Models\BreakdownClaim;
use App\Models\Sale;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;

class BreakdownClaimsRelationManager extends RelationManager
{
    protected static string $relationship = 'breakdownClaims';

    public function form(Form $form): Form
    {
        return BreakdownClaimResource::form($form, $this->sale());
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('reference')
            ->columns([
                Tables\Columns\TextColumn::make('failure_date')->date(),
                Tables\Columns\TextColumn::make('claim_number'),
                Tables\Columns\TextColumn::make('reference'),
                Tables\Columns\TextColumn::make('cause')->wrap(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->slideOver()
                    ->createAnother(false)
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['breakdown_plan_id'] = $this->sale()->breakdownPlan->id;

                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->slideOver(),
                Tables\Actions\EditAction::make()->slideOver(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }

    protected function canCreate(): bool
    {
        return Gate::check('create', [BreakdownClaim::class, $this->getOwnerRecord()->breakdownPlan]);
    }

    private function sale(): Sale
    {
        return $this->getOwnerRecord();
    }
}
