<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Actions\RecheckVehicleData;
use App\Filament\Resources\SaleResource;
use App\Models\Sale;
use App\Services\VehicleLookupService\VehicleVinMismatch;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditSale extends EditRecord
{
    protected static string $resource = SaleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function beforeSave(): void
    {
        /** @var Sale $sale */
        $sale = $this->getRecord();
        if (strcasecmp($sale->vehicle->vrm, $this->data['vrm']) !== 0) {
            // VRM has changed, so reset the owner changes last checked at timestamp
            try {
                app(RecheckVehicleData::class)->execute($sale);
            } catch (VehicleVinMismatch $e) {
                Notification::make()
                    ->warning()
                    ->title($e->getMessage())
                    ->send();

                $this->halt();
            }
        }
    }

    protected function afterSave(): void
    {
        /** @var Sale $sale */
        $sale = $this->getRecord();

        $sale->warranty?->update([
            'end_date' => $sale->start_date->copy()->addMonths($sale->warranty->product->period)->subDay(),
        ]);
        $sale->breakdownPlan?->update([
            'end_date' => $sale->start_date->copy()->addMonths($sale->breakdownPlan->product->period)->subDay(),
        ]);
        $sale->servicePlan?->update([
            'end_date' => $sale->start_date->copy()->addYears($sale->servicePlan->duration_years)->subDay(),
        ]);
    }
}
