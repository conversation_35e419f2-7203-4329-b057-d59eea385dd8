<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Actions\CancelBreakdownPlan;
use App\Enums\FundType;
use App\Filament\Resources\CustomerResource;
use App\Filament\Resources\SaleResource;
use App\Models\Sale;
use Filament\Actions\Action;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Navigation\NavigationItem;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Gate;

class ViewBreakdownPlan extends ViewRecord
{
    protected static string $resource = SaleResource::class;

    protected static ?string $title = 'Breakdown Plan';

    public static function canAccess(array $parameters = []): bool
    {
        if (! $parameters['record']->breakdownPlan()->exists()) {
            return false;
        }

        return parent::canAccess($parameters);
    }

    public static function getNavigationIcon(): string|Htmlable|null
    {
        return null;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('cancel_breakdown_plan')
                ->visible(fn (Sale $sale) => Gate::allows('cancel', $sale->breakdownPlan))
                ->icon('heroicon-o-x-circle')
                ->requiresConfirmation()
                ->action(function (Sale $sale, CancelBreakdownPlan $cancelBreakdownPlan, Action $action) {
                    $cancelBreakdownPlan->execute($sale->breakdownPlan);
                    $action->success();
                })
                ->successNotification(fn (Sale $sale) => Notification::make('Breakdown Plan Cancelled')
                    ->body('The breakdown plan has been successfully cancelled.')
                    ->sendToDatabase($sale->dealership)
                ),
        ];
    }

    public static function getNavigationItems(array $urlParameters = []): array
    {
        return [
            NavigationItem::make(static::getNavigationLabel())
                ->group(static::getNavigationGroup())
                ->parentItem(static::getNavigationParentItem())
                ->icon(static::getNavigationIcon())
                ->activeIcon(static::getActiveNavigationIcon())
                ->isActiveWhen(fn (): bool => request()->routeIs(static::getRouteName()))
                ->sort(static::getNavigationSort())
                ->badge(static::getNavigationBadge(), color: static::getNavigationBadgeColor())
                ->badge($urlParameters['record']->breakdownPlan->status->value, color: $urlParameters['record']->breakdownPlan->status->color())
                ->url(static::getNavigationUrl($urlParameters)),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema(fn (Sale $record) => [
                CustomerResource::getCustomerDetailsSection($record->customer)->relationship('customer'),
                Section::make()
                    ->columns(3)
                    ->relationship('breakdownPlan')
                    ->schema([
                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn (Sale $sale) => $sale->breakdownPlan->status->color()),
                        TextEntry::make('product.name'),
                        TextEntry::make('product.period')->label('Term (months)'),
                        TextEntry::make('fund_type')
                            ->badge()
                            ->formatStateUsing(fn (FundType $state) => $state->label())
                            ->color(fn (FundType $state) => $state->color()),
                        TextEntry::make('provision')->numeric(2)->prefix('£'),
                        TextEntry::make('selling_price')->numeric(2)->prefix('£'),
                        TextEntry::make('admin_fee')->numeric(2)->prefix('£'),
                        TextEntry::make('sales_vat')->label('Sales VAT')->numeric(2)->prefix('£'),
                        TextEntry::make('sale.start_date')->label('Start Date')->date(),
                        TextEntry::make('end_date')->date(),
                    ]),
            ]);
    }

    public function getRelationManagers(): array
    {
        return [
            SaleResource\RelationManagers\BreakdownClaimsRelationManager::class,
        ];
    }
}
