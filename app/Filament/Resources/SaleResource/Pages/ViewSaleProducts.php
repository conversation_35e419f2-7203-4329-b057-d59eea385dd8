<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Filament\Resources\SaleResource;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;

class ViewSaleProducts extends ViewRecord
{
    protected static string $resource = SaleResource::class;

    protected static ?string $title = 'Products';

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                RepeatableEntry::make('products')
                    ->columnSpanFull()
                    ->hiddenLabel()
                    ->columns(4)
                    ->schema([
                        TextEntry::make('name'),
                        TextEntry::make('cost_price')
                            ->visible(Auth::user()->isAdmin())
                            ->money()
                            ->placeholder('N/A'),
                        TextEntry::make('selling_price')
                            ->money()
                            ->placeholder('N/A'),
                        TextEntry::make('dealer_commission')
                            ->money()
                            ->placeholder('N/A'),
                    ]),
            ]);
    }

    public function getRelationManagers(): array
    {
        return [];
    }
}
