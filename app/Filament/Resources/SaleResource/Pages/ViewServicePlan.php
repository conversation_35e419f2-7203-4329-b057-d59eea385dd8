<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Actions\CancelServicePlan;
use App\Filament\Resources\CustomerResource;
use App\Filament\Resources\SaleResource;
use App\Models\Sale;
use Filament\Actions\Action;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Navigation\NavigationItem;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Gate;

class ViewServicePlan extends ViewRecord
{
    protected static string $resource = SaleResource::class;

    protected static ?string $title = 'Service Plan';

    public static function canAccess(array $parameters = []): bool
    {
        if (! $parameters['record']->servicePlan()->exists()) {
            return false;
        }

        return parent::canAccess($parameters);
    }

    public static function getNavigationIcon(): string|Htmlable|null
    {
        return null;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('cancel_service_plan')
                ->visible(fn (Sale $sale) => Gate::allows('cancel', $sale->servicePlan))
                ->icon('heroicon-o-x-circle')
                ->requiresConfirmation()
                ->action(function (Sale $sale) {
                    $sale->servicePlan->update(['cancelled_at' => now()]);
                })
                ->action(function (Sale $sale, CancelServicePlan $cancelServicePlan, Action $action) {
                    $cancelServicePlan->execute($sale->servicePlan);
                    $action->success();
                })
                ->successNotification(fn (Sale $sale) => Notification::make('Service Plan Cancelled')
                    ->body('The service plan has been successfully cancelled.')
                    ->sendToDatabase($sale->dealership)
                ),
        ];
    }

    public static function getNavigationItems(array $urlParameters = []): array
    {
        return [
            NavigationItem::make(static::getNavigationLabel())
                ->group(static::getNavigationGroup())
                ->parentItem(static::getNavigationParentItem())
                ->icon(static::getNavigationIcon())
                ->activeIcon(static::getActiveNavigationIcon())
                ->isActiveWhen(fn (): bool => request()->routeIs(static::getRouteName()))
                ->sort(static::getNavigationSort())
                ->badge(static::getNavigationBadge(), color: static::getNavigationBadgeColor())
                ->badge($urlParameters['record']->servicePlan->status->value, color: $urlParameters['record']->servicePlan->status->color())
                ->url(static::getNavigationUrl($urlParameters)),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema(fn (Sale $record) => [
                CustomerResource::getCustomerDetailsSection($record->customer)->relationship('customer'),
                Section::make()
                    ->columns(3)
                    ->relationship('servicePlan')
                    ->schema([
                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn (Sale $sale) => $sale->servicePlan->status->color()),
                        TextEntry::make('product.name')->label('Product'),
                        TextEntry::make('end_date')->date(),
                        IconEntry::make('product.is_recurring')->label('Subscription')->boolean(),
                        TextEntry::make('duration_years')->label('Duration (years)'),
                        TextEntry::make('selling_price')->label('Selling price')->numeric(2)->prefix('£'),
                        TextEntry::make('admin_fee')->label('Admin fee')->numeric(2)->prefix('£'),
                        TextEntry::make('vat')->label('VAT')->numeric(2)->prefix('£'),
                        ViewEntry::make('service_allocation')
                            ->columnSpanFull()
                            ->view('filament.infolists.service-allocation'),
                    ]),
            ]);
    }

    public function getRelationManagers(): array
    {
        return [
            SaleResource\RelationManagers\AuthorisedServicesRelationManager::class,
        ];
    }
}
