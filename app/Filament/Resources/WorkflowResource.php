<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WorkflowResource\Pages;
use App\Models\Workflow;
use App\Models\WorkflowAction;
use App\Workflows\Enums\WorkflowActionType;
use App\Workflows\Enums\WorkflowBooleanOperators;
use App\Workflows\Enums\WorkflowConditionType;
use App\Workflows\Enums\WorkflowDateOperators;
use App\Workflows\Enums\WorkflowNumericOperators;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;

class WorkflowResource extends Resource
{
    protected static ?string $model = Workflow::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Workflow Details')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->maxWidth(MaxWidth::Medium)
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\Toggle::make('is_active')
                            ->inlineLabel(false),
                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Grid::make()->schema([
                    self::getFiltersSection(),
                    self::getActionsSection(),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['actions']))
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('filters')
                    ->wrap()
                    ->formatStateUsing(fn (Workflow $record) => $record->filtersAsHumanReadable()),
                Tables\Columns\TextColumn::make('actions')
                    ->formatStateUsing(fn (WorkflowAction $state) => $state->label())
                    ->bulleted(),
                Tables\Columns\ToggleColumn::make('is_active')->label('Active'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWorkflows::route('/'),
            'create' => Pages\CreateWorkflow::route('/create'),
            'view' => Pages\ViewWorkflow::route('/{record}'),
            'edit' => Pages\EditWorkflow::route('/{record}/edit'),
        ];
    }

    private static function getFiltersSection(): Forms\Components\Section
    {
        return Forms\Components\Section::make('Filters')
            ->description(function (Workflow $workflow, string $operation) {
                if ($operation !== 'view') {
                    return null;
                }

                return $workflow->filtersAsHumanReadable();
            })
            ->schema([
                Forms\Components\Repeater::make('filters')
                    ->label('Filter groups')
                    ->addActionLabel('Add filter group')
                    ->addActionAlignment(Alignment::End)
                    ->reorderable(false)
                    ->schema([
                        Forms\Components\Select::make('logic')
                            ->visible(fn (Forms\Get $get) => count($get('conditions')) > 1)
                            ->inlineLabel()
                            ->maxWidth(MaxWidth::ExtraSmall)
                            ->options([
                                'AND' => 'AND',
                                'OR' => 'OR',
                            ])
                            ->live()
                            ->required(),
                        Forms\Components\Repeater::make('conditions')
                            ->addActionLabel('Add condition')
                            ->addActionAlignment(Alignment::End)
                            ->columns(4)
                            ->schema([
                                Forms\Components\Placeholder::make('logic_placeholder')
                                    ->maxWidth(MaxWidth::ExtraSmall)
                                    ->hiddenLabel()
                                    ->content(function (Forms\Get $get, Forms\Components\Placeholder $component) {
                                        $conditionKey = implode('.', array_slice(explode('.', $component->getStatePath()), -2, 1));
                                        $firstKey = array_keys($component->getParentRepeater()->getState())[0] ?? null;

                                        if ($conditionKey === $firstKey) {
                                            // First condition in the group, no need to show the logic
                                            return '';
                                        }

                                        $groupStatePath = implode('.', array_slice(explode('.', $component->getStatePath()), 0, -3));

                                        $label = $get($groupStatePath.'.logic', true) ?: 'AND';

                                        return new HtmlString(Blade::render("<x-filament::badge>{$label}</x-filament::badge>"));
                                    }),
                                Forms\Components\Select::make('type')
                                    ->hiddenLabel()
                                    ->placeholder('Select type')
                                    ->live()
                                    ->options(WorkflowConditionType::toSelectArray()),
                                Forms\Components\Select::make('operator')
                                    ->disabled(fn (Forms\Get $get) => ! $get('type'))
                                    ->hiddenLabel()
                                    ->options(function (Forms\Get $get) {
                                        if (! $get('type')) {
                                            return [];
                                        }

                                        /** @var WorkflowBooleanOperators|WorkflowNumericOperators|WorkflowDateOperators $operatorsEnum */
                                        $operatorsEnum = WorkflowConditionType::from($get('type'))->getOperators();

                                        return $operatorsEnum::toSelectArray();
                                    }),
                                Forms\Components\Select::make('value')
                                    ->hiddenLabel()
                                    ->placeholder('Select value')
                                    ->hidden(fn (Forms\Components\Select $component) => empty($component->getOptions()))
                                    ->disabled(fn (Forms\Get $get) => ! $get('type'))
                                    ->options(function (Forms\Get $get) {
                                        if (! $get('type')) {
                                            return [];
                                        }

                                        return WorkflowConditionType::from($get('type'))->getValueOptions();
                                    }),
                            ]),
                    ]),
            ]);
    }

    private static function getActionsSection(): Forms\Components\Section
    {
        return Forms\Components\Section::make('Actions')
            ->description(function (Workflow $workflow, string $operation) {
                if ($operation !== 'view') {
                    return null;
                }

                return $workflow->actions->map(fn (WorkflowAction $action) => $action->label())->implode('; ');
            })
            ->schema([
                Forms\Components\Repeater::make('actions')
                    ->relationship()
                    ->hiddenLabel()
                    ->reorderable(false)
                    ->addActionAlignment(Alignment::End)
                    ->addActionLabel('Add action')
                    ->orderColumn('order')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->label('Action')
                            ->placeholder('Select action')
                            ->maxWidth(MaxWidth::ExtraSmall)
                            ->live()
                            ->options(WorkflowActionType::toSelectArray())
                            ->required(),
                        Forms\Components\Grid::make(3)
                            ->schema(function (Forms\Get $get) {
                                if (! $get('type')) {
                                    return [];
                                }

                                $fields = WorkflowActionType::from($get('type'))->getWorkflowAction()->formFields();

                                // Wrap each field to store in the values JSON column
                                foreach ($fields as $field) {
                                    $field->statePath('values.'.$field->getName());
                                }

                                return $fields;
                            }),
                    ]),
            ]);
    }
}
