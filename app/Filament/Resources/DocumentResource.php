<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DocumentResource\Pages;
use App\Filament\Resources\DocumentResource\RelationManagers\VersionsRelationManager;
use App\Models\Document;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Arr;

class DocumentResource extends Resource
{
    protected static ?string $model = Document::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->disabled()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->unique(
                                table: 'documents',
                                column: 'title',
                                ignoreRecord: true
                            )
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Section::make()
                    ->columns(4)
                    ->relationship('latestVersion')
                    ->saveRelationshipsUsing(function (Document $document, array $state) {
                        // This prevents excessive version numbers being saved to the database.
                        // It has to be done on the Eloquent builder like this, as the relation on the model
                        // creates a weird side effect where the UI doesn't update on save.
                        // Try it if you don't believe me :)
                        if (trim($document->latestVersion()->first()->content) === trim($state['content'])) {
                            return;
                        }
                        $document->versions()->create([
                            ...Arr::except($state, ['id', 'version_number', 'created_at', 'updated_at']),
                            'version_number' => $document->versions()->count() + 1,
                        ]);
                    })
                    ->schema([
                        Forms\Components\Textarea::make('change_notes')
                            ->required()
                            ->helperText('Describe the changes made in this version.')
                            ->columnSpanFull(),
                        Forms\Components\MarkdownEditor::make('content')
                            ->disableToolbarButtons(['attachFiles'])
                            ->required()
                            ->minHeight('40vh')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('slug')
                    ->searchable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            VersionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDocuments::route('/'),
            'view' => Pages\ViewDocument::route('/{record}'),
            'edit' => Pages\EditDocument::route('/{record}/edit'),
        ];
    }
}
