<?php

namespace App\Filament\Resources;

use App\Enums\FundType;
use App\Filament\Resources\AccountResource\Pages;
use App\Filament\Resources\AccountResource\RelationManagers\BreakdownProductsRelationManager;
use App\Filament\Resources\AccountResource\RelationManagers\ManufacturerSurchargesRelationManager;
use App\Filament\Resources\AccountResource\RelationManagers\PayLaterPlansRelationManager;
use App\Filament\Resources\AccountResource\RelationManagers\ProductsRelationManager;
use App\Filament\Resources\AccountResource\RelationManagers\ServicePlanProductsRelationManager;
use App\Filament\Resources\AccountResource\RelationManagers\WarrantyProductsRelationManager;
use App\Models\Account;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;

class AccountResource extends Resource
{
    protected static ?string $model = Account::class;

    public static function form(Form $form): Form
    {
        $blockUpdatingFundingTypes = (
            $form->getOperation() === 'edit'
            && auth()->user()->cannot('accounts.update-funding-types')
            && $form->getRecord()->sales()->exists()
        );

        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Grid::make()
                            ->columns(3)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->maxLength(100)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function (Get $get, Set $set, ?string $old, ?string $state) {
                                        if ($get('short_name') && $get('short_name') !== Str::shortName($old)) {
                                            return;
                                        }

                                        $set('short_name', Str::shortName($state));
                                    }),
                                Forms\Components\TextInput::make('short_name')
                                    ->required()
                                    ->maxLength(15),
                                Forms\Components\TextInput::make('authorisation_contact')
                                    ->required()
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('max_hourly_labour_rate')
                                    ->required()
                                    ->prefix('£'),
                            ]),
                        Forms\Components\Grid::make()
                            ->columns(3)
                            ->schema([
                                Forms\Components\Select::make('warranty_fund_type')
                                    ->options(FundType::toSelectArray())
                                    ->required()
                                    ->disabled($blockUpdatingFundingTypes),
                                Forms\Components\Select::make('breakdown_fund_type')
                                    ->options(FundType::toSelectArray())
                                    ->required()
                                    ->disabled($blockUpdatingFundingTypes),
                                Forms\Components\Toggle::make('send_contract_emails')
                                    ->inlineLabel()
                                    ->required(),
                                Forms\Components\Textarea::make('claim_handling_notes')
                                    ->autosize()
                                    ->columnSpanFull(),
                                Forms\Components\FileUpload::make('logo_path')
                                    ->directory('logos')
                                    ->image()
                                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'])
                                    ->imageEditor(),
                            ]),

                    ]),
                Forms\Components\Section::make()
                    ->visible(auth()->user()->hasRole('Developer'))
                    ->schema([
                        Forms\Components\Repeater::make('serviceCredentials')
                            ->relationship()
                            ->columns(4)
                            ->schema([
                                Forms\Components\Select::make('provider')
                                    ->options([
                                        'payment_assist' => 'Payment Assist',
                                    ])
                                    ->required(),
                                Forms\Components\TextInput::make('credentials.api_key')
                                    ->label('API Key')
                                    ->required(),
                                Forms\Components\TextInput::make('credentials.api_secret')
                                    ->label('API Secret')
                                    ->required(),
                                Forms\Components\Toggle::make('is_live')
                                    ->label('Live')
                                    ->inline(false)
                                    ->required(),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('short_name')
            ->columns([
                Tables\Columns\TextColumn::make('short_name')
                    ->label('Name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('authorisation_contact')
                    ->searchable(),
                Tables\Columns\TextColumn::make('max_hourly_labour_rate')
                    ->alignCenter()
                    ->money()
                    ->sortable(),
                Tables\Columns\IconColumn::make('warranty_self_funded')
                    ->alignCenter()
                    ->boolean()
                    ->sortable(),
                Tables\Columns\IconColumn::make('breakdown_self_funded')
                    ->alignCenter()
                    ->boolean()
                    ->sortable(),
                Tables\Columns\IconColumn::make('send_contract_emails')
                    ->alignCenter()
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('warranty_self_funded')
                    ->label('Warranty Dealer Funded')
                    ->options([
                        'Yes' => true,
                        'No' => false,
                        'All' => null,
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ManufacturerSurchargesRelationManager::class,
            WarrantyProductsRelationManager::class,
            BreakdownProductsRelationManager::class,
            ServicePlanProductsRelationManager::class,
            ProductsRelationManager::class,
            PayLaterPlansRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAccounts::route('/'),
            'create' => Pages\CreateAccount::route('/create'),
            'view' => Pages\ViewAccount::route('/{record}'),
            'edit' => Pages\EditAccount::route('/{record}/edit'),
        ];
    }
}
