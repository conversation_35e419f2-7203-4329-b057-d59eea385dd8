<?php

namespace App\Filament\Resources\ClaimEstimateResource\Pages;

use App\Enums\ClaimEstimateStatus;
use App\Filament\Resources\ClaimEstimateResource;
use App\Models\ClaimEstimate;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListClaimEstimates extends ListRecords
{
    protected static string $resource = ClaimEstimateResource::class;

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(),
            ClaimEstimateStatus::PENDING->value => $this->makeTabForStatus(ClaimEstimateStatus::PENDING, 'warning'),
            ClaimEstimateStatus::AUTHORISED->value => $this->makeTabForStatus(ClaimEstimateStatus::AUTHORISED, 'warning'),
            ClaimEstimateStatus::REJECTED->value => $this->makeTabForStatus(ClaimEstimateStatus::REJECTED, 'danger'),
            ClaimEstimateStatus::SETTLED->value => $this->makeTabForStatus(ClaimEstimateStatus::SETTLED, 'success'),
        ];
    }

    private function makeTabForStatus(ClaimEstimateStatus $status, string $badgeColor): Tab
    {
        return Tab::make()
            ->badge(ClaimEstimate::whereHas('claim.warranty')->withStatus($status)->count())
            ->badgeColor($badgeColor)
            ->modifyQueryUsing(fn (Builder $query) => $query->withStatus($status));
    }
}
