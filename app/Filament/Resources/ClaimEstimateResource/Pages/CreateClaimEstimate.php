<?php

namespace App\Filament\Resources\ClaimEstimateResource\Pages;

use App\Filament\Resources\ClaimEstimateResource;
use App\Filament\Resources\ClaimResource;
use App\Models\Claim;
use App\Models\ClaimEstimate;
use App\Models\Repairer;
use Filament\Forms\Form;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Gate;

class CreateClaimEstimate extends CreateRecord
{
    public Claim $claim;

    protected static string $resource = ClaimEstimateResource::class;

    protected static bool $canCreateAnother = false;

    protected function authorizeAccess(): void
    {
        Gate::authorize('create', [ClaimEstimate::class, $this->claim]);
    }

    public function getBreadcrumbs(): array
    {
        return [
            ClaimResource::getUrl() => 'Claims',
            ClaimResource::getUrl('view', ['record' => $this->claim->getRouteKey()]) => 'Claim #'.$this->claim->reference,
            'New Estimate',
        ];
    }

    public function form(Form $form): Form
    {
        return ClaimEstimateResource::getClaimForm($form, $this->claim);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['claim_id'] = $this->claim->id;
        $data['user_id'] = auth()->id();
        $data['estimate_completed_at'] = ($data['estimate_completed'] ?? false) ? now() : null;
        unset($data['estimate_completed']);

        return $data;
    }

    protected function afterCreate()
    {
        $claimEstimate = $this->getRecord();
        $repairer = Repairer::find($claimEstimate->repairer_id);
        if ($repairer) {
            $repairer->email = $repairer->email ?: $claimEstimate->workshop_email;
            $repairer->phone = $repairer->phone ?: $claimEstimate->workshop_phone;
            $repairer->contact = $repairer->contact ?: $claimEstimate->workshop_contact;
            $repairer->save();
        }
    }

    protected function getRedirectUrl(): string
    {
        return ClaimResource::getUrl('view', ['record' => $this->getRecord()->claim->getRouteKey()]);
    }
}
