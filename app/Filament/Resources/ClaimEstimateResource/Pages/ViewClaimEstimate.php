<?php

namespace App\Filament\Resources\ClaimEstimateResource\Pages;

use App\Actions\GenerateWarrantyClaimInvoiceToDealershipAndGeneratePayment;
use App\Filament\Resources\ClaimEstimateResource;
use App\Filament\Resources\ClaimResource;
use App\Filament\Resources\InvoiceResource;
use App\Models\ClaimAuthorisation;
use App\Models\ClaimEstimate;
use App\Models\Invoice;
use Filament\Actions\EditAction;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Gate;

class ViewClaimEstimate extends ViewRecord
{
    protected static string $resource = ClaimEstimateResource::class;

    public function getBreadcrumbs(): array
    {
        return [
            ClaimResource::getUrl() => 'Claims',
            ClaimResource::getUrl('view', ['record' => $this->getRecord()->claim->getRouteKey()]) => 'Claim #'.$this->getRecord()->claim->reference,
            'View Estimate',
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return view('components.headers.claim-estimate', ['claimEstimate' => $this->record]);
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('view_invoice')
                ->visible(fn (ClaimEstimate $claimEstimate) => $claimEstimate->authorisation?->invoice && Gate::allows('view', $claimEstimate->authorisation->invoice))
                ->icon('heroicon-s-eye')
                ->url(fn (ClaimEstimate $claimEstimate) => InvoiceResource::getUrl('view', ['record' => $claimEstimate->authorisation->invoice])),
            \Filament\Actions\Action::make('generate_invoice')
                ->visible(function (ClaimEstimate $claimEstimate) {
                    if ($claimEstimate->invoicing_dealer_direct) {
                        return false;
                    }
                    if (! $claimEstimate->authorisation) {
                        return false;
                    }
                    if ($claimEstimate->authorisation?->invoice) {
                        return false;
                    }
                    if ($claimEstimate->is_charged_internally) {
                        return false;
                    }
                    if ($claimEstimate->is_invoicing_dealer_direct) {
                        return false;
                    }

                    return Gate::allows('create', [Invoice::class]);
                })
                ->icon('heroicon-o-document-currency-pound')
                ->requiresConfirmation()
                ->modalDescription('Are you sure you want to generate an invoice for the dealer?')
                ->action(function (ClaimEstimate $claimEstimate, GenerateWarrantyClaimInvoiceToDealershipAndGeneratePayment $generateWarrantyClaimInvoiceToDealership) {
                    $generateWarrantyClaimInvoiceToDealership->execute($claimEstimate->authorisation);
                    Notification::make()
                        ->success()
                        ->body('Invoice has been generated.')
                        ->send();
                }),

            \Filament\Actions\Action::make('claim_authorisation')
                ->hidden(fn (ClaimEstimate $claimEstimate) => $claimEstimate->authorisation()->exists())
                ->label('Authorise claim estimate')
                ->icon('heroicon-s-hand-thumb-up')
                ->slideOver()
                ->visible(fn (ClaimEstimate $claimEstimate) => Gate::allows('create', [ClaimAuthorisation::class, $claimEstimate]))
                ->color('success')
                ->form([
                    Actions::make([
                        Action::make('view_invoice')
                            ->visible(fn (ClaimEstimate $claimEstimate) => $claimEstimate->authorisation?->invoice)
                            ->icon('heroicon-m-eye')
                            ->link()
                            ->url(fn (ClaimEstimate $claimEstimate) => InvoiceResource::getUrl('view', ['record' => $claimEstimate->authorisation->invoice]))
                            ->openUrlInNewTab(),
                    ])->columnSpanFull()->alignRight(),
                    Forms\Components\TextInput::make('current_mileage')
                        ->maxWidth(MaxWidth::ExtraSmall)
                        ->required()
                        ->numeric(),
                    Forms\Components\Select::make('reason')
                        ->maxWidth(MaxWidth::ExtraSmall)
                        ->options([
                            'EARLY_CLAIM' => 'Early Claim (< 30 days)',
                            'COVERED_BY_WARRANTY' => 'Covered by Warranty',
                            'GOODWILL' => 'Goodwill',
                            'CONSUMER_RIGHTS' => 'Consumer Rights',
                        ])
                        ->required(),
                    Forms\Components\Textarea::make('work_done')
                        ->label('Description of Work Done')
                        ->columnSpanFull()
                        ->autosize()
                        ->required(),
                ])
                ->fillForm(function (ClaimEstimate $claimEstimate) {
                    if ($claimEstimate->authorisation) {
                        return $claimEstimate->authorisation->toArray();
                    }

                    return [
                        'is_charged_internally' => $claimEstimate->is_charged_internally,
                        'work_done' => $claimEstimate->work_required,
                        'current_mileage' => $claimEstimate->current_mileage,
                    ];
                })
                ->action(function (ClaimEstimate $claimEstimate, array $data) {
                    $claimEstimate->update([
                        'current_mileage' => $data['current_mileage'],
                    ]);

                    $claimEstimate->authorisation()->updateOrCreate([], Arr::only($data, ['work_done', 'reason']));
                })
                ->successRedirectUrl(fn ($record) => ClaimResource::getUrl('view', ['record' => $this->getOwnerRecord()])),

            EditAction::make()->url(ClaimResource::getUrl('edit-estimate', ['record' => $this->getRecord(), 'claim' => $this->getRecord()->claim])),

        ];
    }
}
