<?php

namespace App\Filament\Resources\ClaimEstimateResource\Pages;

use App\Filament\Resources\ClaimEstimateResource;
use App\Filament\Resources\ClaimResource;
use Filament\Resources\Pages\EditRecord;

class EditClaimEstimate extends EditRecord
{
    protected static string $resource = ClaimEstimateResource::class;

    public function getBreadcrumbs(): array
    {
        return [
            ClaimResource::getUrl() => 'Claims',
            ClaimResource::getUrl('view', ['record' => $this->getRecord()->claim->getRouteKey()]) => 'Claim #'.$this->getRecord()->claim->reference,
            'Edit Estimate',
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if ($data['estimate_completed'] ?? false) {
            $data['estimate_completed_at'] = now();
        }
        unset($data['estimate_completed']);

        return $data;
    }

    protected function getRedirectUrl(): ?string
    {
        return ClaimResource::getUrl('view-estimate', ['record' => $this->getRecord(), 'claim' => $this->getRecord()->claim->getRouteKey()]);
    }
}
