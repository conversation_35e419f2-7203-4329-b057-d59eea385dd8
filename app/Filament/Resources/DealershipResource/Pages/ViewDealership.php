<?php

namespace App\Filament\Resources\DealershipResource\Pages;

use App\Filament\Resources\DealershipResource;
use App\Filament\Widgets\MissingPaymentMethodAlert;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewDealership extends ViewRecord
{
    protected static string $resource = DealershipResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            MissingPaymentMethodAlert::make(['record' => $this->getRecord()]),
        ];
    }
}
