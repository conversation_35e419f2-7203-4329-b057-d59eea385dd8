<?php

namespace App\Filament\Resources;

use App\Enums\AuthorisedServiceStatus;
use App\Filament\Resources\AuthorisedServiceResource\Pages;
use App\Filament\Resources\SaleResource\Pages\ViewServicePlan;
use App\Models\AuthorisedService;
use Filament\Resources\Resource;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class AuthorisedServiceResource extends Resource
{
    protected static ?string $model = AuthorisedService::class;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->whereHas('servicePlan');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('servicePlan.account.name')
                    ->label('Account')
                    ->toggleable()
                    ->visible(fn () => Auth::user()->isViewingAllRecords())
                    ->limit(40)
                    ->sortable(),

                TextColumn::make('servicePlan.product.name'),

                TextColumn::make('created_at')
                    ->label('Approved at')
                    ->dateTime(),
                TextColumn::make('serviceType.name')
                    ->label('Type'),
                TextColumn::make('user.name')
                    ->label('Approved by'),
                TextColumn::make('authorisation_code'),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (AuthorisedServiceStatus $state): string => match ($state) {
                        AuthorisedServiceStatus::BOOKED => 'warning',
                        AuthorisedServiceStatus::COMPLETED => 'success',
                        AuthorisedServiceStatus::CANCELLED => 'danger',
                    }),
            ])
            ->filters([
                //
            ])
            ->actions([
                ViewAction::make()
                    ->url(fn ($record) => ViewServicePlan::getUrl([$record->servicePlan->sale_id])),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAuthorisedServices::route('/'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
