<?php

namespace App\Filament\Resources\InvoiceResource\Pages;

use App\Filament\Resources\InvoiceResource;
use App\Models\Invoice;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListInvoices extends ListRecords
{
    protected static string $resource = InvoiceResource::class;

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(),
            Invoice::STATUS_DELETED => $this->makeTabForStatus(Invoice::STATUS_DELETED, 'danger'),
            Invoice::STATUS_VOIDED => $this->makeTabForStatus(Invoice::STATUS_VOIDED, 'danger'),
            Invoice::STATUS_PENDING => $this->makeTabForStatus(Invoice::STATUS_PENDING, 'warning'),
            Invoice::STATUS_SUBMITTED => $this->makeTabForStatus(Invoice::STATUS_SUBMITTED, 'warning'),
            Invoice::STATUS_AUTHORISED => $this->makeTabForStatus(Invoice::STATUS_AUTHORISED, 'warning'),
            Invoice::STATUS_PAID => $this->makeTabForStatus(Invoice::STATUS_PAID, 'success'),
        ];
    }

    private function makeTabForStatus(string $status, string $badgeColor): Tab
    {
        return Tab::make()
            ->badge(Invoice::query()->where('status', $status)->count())
            ->badgeColor($badgeColor)
            ->modifyQueryUsing(fn (Builder $query) => $query->where('status', $status));
    }
}
