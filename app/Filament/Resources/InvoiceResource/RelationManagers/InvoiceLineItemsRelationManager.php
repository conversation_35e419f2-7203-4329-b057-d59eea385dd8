<?php

namespace App\Filament\Resources\InvoiceResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class InvoiceLineItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'lineItems';

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->paginated(false)
            ->columns([
                Tables\Columns\TextColumn::make('sale_id')
                    ->label('Sale')
                    ->url(fn (Model $record) => "/sales/{$record->sale_id}"),
                Tables\Columns\TextColumn::make('sale.vehicle.vrm')
                    ->label('VRM'),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color('success')
                    ->formatStateUsing(fn (string $state) => ucwords($state))
                    ->label('Type'),
                Tables\Columns\TextColumn::make('quantity')->numeric(),
                Tables\Columns\TextColumn::make('description'),
                Tables\Columns\TextColumn::make('account_code')->alignCenter(),
                Tables\Columns\TextColumn::make('unit_amount')->money()->alignRight(),
                Tables\Columns\TextColumn::make('tax')->money()->alignRight()->label('VAT'),
                Tables\Columns\TextColumn::make('total')->money()->alignRight()
                    ->summarize(Tables\Columns\Summarizers\Sum::make()->money()->label('')),
            ]);
    }
}
