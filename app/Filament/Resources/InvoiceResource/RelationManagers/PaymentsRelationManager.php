<?php

namespace App\Filament\Resources\InvoiceResource\RelationManagers;

use App\Filament\Resources\PaymentResource;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class PaymentsRelationManager extends RelationManager
{
    protected static string $relationship = 'payments';

    public function form(Form $form): Form
    {
        return PaymentResource::form($form);
    }

    public function table(Table $table): Table
    {
        return $table
            ->defaultSort('charge_date', 'desc')
            ->paginated(false)
            ->columns(PaymentResource::getTableColumns())
            ->recordUrl(fn ($record) => PaymentResource::getUrl('view', [$record]))
            ->actions([
                Tables\Actions\EditAction::make()->slideOver(),
            ]);
    }
}
