<?php

namespace App\Filament\Resources\WarrantyResource\Pages;

use App\Enums\ProductStatus;
use App\Filament\Resources\WarrantyResource;
use App\Models\Warranty;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListWarranties extends ListRecords
{
    protected static string $resource = WarrantyResource::class;

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(),
            ProductStatus::PENDING->value => $this->makeTabForStatus(ProductStatus::PENDING->value, 'warning'),
            ProductStatus::LIVE->value => $this->makeTabForStatus(ProductStatus::LIVE->value, 'success'),
            ProductStatus::CANCELLED->value => $this->makeTabForStatus(ProductStatus::CANCELLED->value, 'danger'),
            ProductStatus::EXPIRED->value => $this->makeTabForStatus(ProductStatus::EXPIRED->value, 'danger'),
        ];
    }

    private function makeTabForStatus(string $status, string $badgeColor): Tab
    {
        $modifyQuery = fn (Builder $query) => match ($status) {
            ProductStatus::PENDING->value => $query->pending(),
            ProductStatus::LIVE->value => $query->live(),
            ProductStatus::CANCELLED->value => $query->cancelled(),
            ProductStatus::EXPIRED->value => $query->expired(),
        };

        return Tab::make()
            ->badge($modifyQuery(Warranty::query())->count())
            ->badgeColor($badgeColor)
            ->modifyQueryUsing($modifyQuery);
    }
}
