<?php

namespace App\Filament\Resources\PaymentResource\Pages;

use App\Filament\Resources\PaymentResource;
use App\Models\Payment;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListPayments extends ListRecords
{
    protected static string $resource = PaymentResource::class;

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(),
            Payment::STATUS_FAILED => $this->makeTabForStatus(Payment::STATUS_FAILED, 'danger'),
            Payment::STATUS_CANCELLED => $this->makeTabForStatus(Payment::STATUS_CANCELLED, 'danger'),
            Payment::STATUS_DRAFT => $this->makeTabForStatus(Payment::STATUS_DRAFT, 'warning'),
            Payment::STATUS_PENDING_SUBMISSION => $this->makeTabForStatus(Payment::STATUS_PENDING_SUBMISSION, 'warning'),
            Payment::STATUS_PAID_OUT => $this->makeTabForStatus(Payment::STATUS_PAID_OUT, 'success'),
        ];
    }

    private function makeTabForStatus(string $status, string $badgeColor): Tab
    {
        return Tab::make()
            ->badge(Payment::query()->where('status', $status)->count())
            ->badgeColor($badgeColor)
            ->modifyQueryUsing(fn (Builder $query) => $query->where('status', $status));
    }
}
