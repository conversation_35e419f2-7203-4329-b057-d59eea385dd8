<?php

namespace App\Filament\Resources\PaymentResource\Pages;

use App\Actions\CancelPayment;
use App\Actions\CreatePaymentInProcessor;
use App\Filament\Resources\PaymentResource;
use App\Jobs\SyncPaymentFromProcessor;
use App\Models\Payment;
use App\Services\Payments\DirectDebit\PaymentException;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Log;

class ViewPayment extends ViewRecord
{
    protected static string $resource = PaymentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make()->slideOver(),
            Action::make('cancel_payment')
                ->visible(fn (Payment $record) => $record->processor_payment_id && $record->status === Payment::STATUS_PENDING)
                ->requiresConfirmation()
                ->action(function (Payment $payment, CancelPayment $cancelPayment) {
                    try {
                        $cancelPayment->execute($payment);
                        Notification::make()
                            ->success()
                            ->body('The payment has been cancelled successfully.')
                            ->send();
                    } catch (PaymentException $e) {
                        Log::error($e->getMessage(), ['payment' => $payment->toArray()]);
                        Notification::make()
                            ->danger()
                            ->body($e->getMessage())
                            ->send();
                    }
                }),
            Action::make('create_in_payment_processor')
                ->label('Create in '.$this->getRecord()->billingRequest->provider->name())
                ->hidden(fn (Payment $record) => $record->processor_payment_id)
                ->requiresConfirmation()
                ->action(function (Payment $payment, CreatePaymentInProcessor $createPaymentInProcessor) {
                    if (! $payment->billingRequest?->isActive()) {
                        Notification::make()
                            ->danger()
                            ->body('There is no active Direct Debit mandate for this payment. Please create a new one.')
                            ->send();

                        return;
                    }
                    try {
                        $createPaymentInProcessor->execute($payment);
                        Notification::make()
                            ->success()
                            ->body('The payment has been generated in the payment processor.')
                            ->send();
                    } catch (PaymentException $e) {
                        Log::error($e->getMessage(), ['payment' => $payment->toArray()]);
                        Notification::make()
                            ->danger()
                            ->body($e->getMessage())
                            ->send();
                    }
                }),
            Action::make('update_from_payment_processor')
                ->label(fn (Payment $record) => 'Update from '.$record->billingRequest->provider->name())
                ->visible(fn (Payment $record) => auth()->user()->hasRole('Developer') && $record->paymentProcessorUrl())
                ->action(function (Payment $payment) {
                    try {
                        SyncPaymentFromProcessor::dispatchSync($payment);
                        Notification::make()
                            ->success()
                            ->body('The payment has been synced from the payment processor.')
                            ->send();
                    } catch (\Throwable $e) {
                        Log::error($e->getMessage(), ['payment' => $payment->toArray()]);
                        Notification::make()
                            ->danger()
                            ->body('There was an error fetching from the payment provider.')
                            ->send();
                    }
                }),
            Action::make('view_in_payment_processor')
                ->label(fn (Payment $record) => 'View in '.$record->billingRequest->provider->name())
                ->visible(fn (Payment $record) => $record->paymentProcessorUrl())
                ->url(fn (Payment $record) => $record->paymentProcessorUrl())
                ->openUrlInNewTab(),
        ];
    }
}
