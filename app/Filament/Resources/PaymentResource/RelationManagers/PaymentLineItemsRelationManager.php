<?php

namespace App\Filament\Resources\PaymentResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class PaymentLineItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'lineItems';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('description')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->paginated(false)
            ->columns([
                Tables\Columns\TextColumn::make('payable_type')
                    ->badge()
                    ->color('success')
                    ->formatStateUsing(fn (string $state) => ucwords($state))
                    ->label('Type'),
                Tables\Columns\TextColumn::make('description'),
                Tables\Columns\TextColumn::make('account_code')->alignCenter(),
                Tables\Columns\TextColumn::make('unit_amount')->money()->alignRight(),
                Tables\Columns\TextColumn::make('tax')->money()->alignRight()->label('VAT'),
                Tables\Columns\TextColumn::make('total')->money()->alignRight()
                    ->summarize(Tables\Columns\Summarizers\Sum::make()->money()->label('')),
            ]);
    }
}
