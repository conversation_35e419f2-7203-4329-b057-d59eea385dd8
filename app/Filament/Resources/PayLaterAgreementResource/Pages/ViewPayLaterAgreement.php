<?php

namespace App\Filament\Resources\PayLaterAgreementResource\Pages;

use App\Filament\Resources\PayLaterAgreementResource;
use App\Jobs\SyncPayLaterAgreementStatus;
use App\Models\PayLaterAgreement;
use Filament\Actions\Action;
use Filament\Resources\Pages\ViewRecord;

class ViewPayLaterAgreement extends ViewRecord
{
    protected static string $resource = PayLaterAgreementResource::class;

    public function getHeaderActions(): array
    {
        return [
            Action::make('sync_from_provider')
                ->visible(fn (PayLaterAgreement $record) => $record->token && auth()->user()->hasRole('Developer'))
                ->action(function (Action $action, PayLaterAgreement $record) {
                    SyncPayLaterAgreementStatus::dispatchSync($record);
                    $this->record->refresh();
                    $action->success();
                })
                ->successNotificationTitle('Agreement status has been successfully synced.'),
        ];
    }
}
