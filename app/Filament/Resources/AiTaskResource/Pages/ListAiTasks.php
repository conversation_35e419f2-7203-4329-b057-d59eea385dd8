<?php

namespace App\Filament\Resources\AiTaskResource\Pages;

use App\Filament\Resources\AiTaskResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAiTasks extends ListRecords
{
    protected static string $resource = AiTaskResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
