<?php

namespace App\Filament\Resources\AiTaskResource\Pages;

use App\Filament\Resources\AiTaskResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewAiTask extends ViewRecord
{
    protected static string $resource = AiTaskResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
