<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RepairerResource\Pages;
use App\Filament\Resources\RepairerResource\RelationManagers\ClaimEstimatesRelationManager;
use App\Models\Repairer;
use App\Services\PlacesAutocomplete\GooglePlacesAutocomplete;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class RepairerResource extends Resource
{
    protected static ?string $model = Repairer::class;

    protected static ?string $navigationIcon = 'repairers';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->when(Auth::user()->isAdmin(),
                // Add boolean for weather repairer is preferred
                fn ($q) => $q->withExists(['dealerships as is_preferred' => fn ($q) => $q->where('account_id', Auth::user()->account_id)]),
                // For dealer users, only show repairers that are associated with their account
                fn ($q) => $q->whereHas('dealerships', fn ($q) => $q->where('account_id', Auth::user()->account_id))
            );
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Google Search')
                    ->schema([
                        Forms\Components\Select::make('search')
                            ->hiddenLabel()
                            ->placeholder('Start typing the name of the repairer...')
                            ->dehydrated(false)
                            ->searchable()
                            ->debounce(500)
                            ->getSearchResultsUsing(function (string $search, GooglePlacesAutocomplete $googlePlacesAutocomplete) {
                                if (strlen($search) < 3) {
                                    return [];
                                }

                                return $googlePlacesAutocomplete->search($search, 'car_repair');
                            })
                            ->afterStateUpdated(function (?string $state, GooglePlacesAutocomplete $googlePlacesAutocomplete) use ($form) {
                                if ($state) {
                                    $form->fill($googlePlacesAutocomplete->getPlaceDetails($state));
                                }
                            }),
                    ]),
                Forms\Components\Section::make()
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(100),
                        Forms\Components\TextInput::make('contact')
                            ->maxLength(100),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->maxLength(50),
                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->maxLength(50),
                        Forms\Components\TextInput::make('website')
                            ->url()
                            ->nullable()
                            ->maxLength(100),
                    ]),
                Forms\Components\Split::make([
                    Forms\Components\Section::make('Address')
                        ->maxWidth('2xl')
                        ->inlineLabel()
                        ->schema([
                            Forms\Components\TextInput::make('address_1')
                                ->maxLength(150),
                            Forms\Components\TextInput::make('address_2')
                                ->maxLength(150),
                            Forms\Components\TextInput::make('city')
                                ->maxLength(50),
                            Forms\Components\TextInput::make('county')
                                ->maxLength(50),
                            Forms\Components\TextInput::make('country')
                                ->maxLength(50),
                            Forms\Components\TextInput::make('postcode')
                                ->maxLength(25),
                        ]),
                    Forms\Components\Section::make('Company Information')
                        ->maxWidth('2xl')
                        ->inlineLabel()
                        ->schema([
                            Forms\Components\TextInput::make('company_number')->maxLength(50),
                            Forms\Components\TextInput::make('vat_number')->prefix('GB')->maxLength(50),
                            Forms\Components\TextInput::make('bank_sort_code')->placeholder('00-00-00')->maxLength(10),
                            Forms\Components\TextInput::make('bank_account_number')->placeholder('********')->maxLength(10),
                        ]),
                ])->columnSpanFull(),
                Forms\Components\Hidden::make('place_id'),
            ]);
    }

    public static function table(Table $table): Table
    {
        $showPreferredRepairers = auth()->user()->isAdmin() && ! auth()->user()->isViewingAllRecords();

        return $table
            ->defaultSort('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->limit(40)
                    ->searchable(),
                Tables\Columns\TextColumn::make('contact'),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('city')
                    ->searchable(),
                Tables\Columns\TextColumn::make('postcode')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_preferred')
                    ->visible($showPreferredRepairers)
                    ->label(fn () => sprintf('%s preferred repairer', auth()->user()->account?->short_name))
                    ->alignCenter()
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('is_preferred')
                    ->visible($showPreferredRepairers)
                    ->label('Only Preferred Repairers')
                    ->query(fn (Builder $query) => $query->whereHas('dealerships', fn ($q) => $q->where('account_id', Auth::user()->account_id))),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Contact Information')
                    ->columns(3)
                    ->schema([
                        TextEntry::make('name'),
                        TextEntry::make('contact'),
                        TextEntry::make('email')
                            ->placeholder('N/A')
                            ->label('Email'),
                        TextEntry::make('phone')
                            ->placeholder('N/A')
                            ->label('Phone Number'),
                        TextEntry::make('website')
                            ->placeholder('N/A')
                            ->url(fn ($state) => 'http://'.$state)
                            ->openUrlInNewTab(),
                        TextEntry::make('address')
                            ->getStateUsing(fn (Repairer $record) => explode('|||', $record->fullAddress('|||')))
                            ->listWithLineBreaks(),
                    ]),
                Section::make('Banking Details')
                    ->columns(4)
                    ->schema([
                        TextEntry::make('company_number')->placeholder('Unknown'),
                        TextEntry::make('vat_number')->placeholder('Unknown'),
                        TextEntry::make('bank_sort_code')->placeholder('Unknown'),
                        TextEntry::make('bank_account_number')->placeholder('Unknown'),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ClaimEstimatesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRepairers::route('/'),
            'create' => Pages\CreateRepairer::route('/create'),
            'view' => Pages\ViewRepairer::route('/{record}'),
            'edit' => Pages\EditRepairer::route('/{record}/edit'),
        ];
    }
}
