<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OfferResource\Pages;
use App\Models\BreakdownProduct;
use App\Models\Offer;
use App\Models\WarrantyProduct;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class OfferResource extends Resource
{
    protected static ?string $model = Offer::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(200),
                Forms\Components\Section::make('Email Content')
                    ->columns()
                    ->schema([
                        Forms\Components\TextInput::make('subject')
                            ->hint('The subject line in the email')
                            ->required()
                            ->maxLength(200),
                        Forms\Components\TextInput::make('button_text')
                            ->hint('The call to action text in the email button')
                            ->required()
                            ->maxLength(50),
                        Forms\Components\RichEditor::make('body')
                            ->hint('The body of the email')
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Section::make()
                    ->columns()
                    ->schema([
                        Forms\Components\Select::make('warranty_product_id')
                            ->relationship('warrantyProduct', 'id', fn ($query) => $query->with('coverLevel')->orderBy('position'))
                            ->getOptionLabelFromRecordUsing(fn (WarrantyProduct $warrantyProduct) => $warrantyProduct->getLabel()),
                        Forms\Components\TextInput::make('warranty_selling_price')
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\Select::make('breakdown_product_id')
                            ->relationship('breakdownProduct', 'name', fn ($query) => $query->orderBy('position'))
                            ->getOptionLabelFromRecordUsing(fn (BreakdownProduct $breakdownProduct) => $breakdownProduct->getLabel()),
                        Forms\Components\TextInput::make('breakdown_selling_price')
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\Select::make('service_plan_product_id')
                            ->relationship('servicePlanProduct', 'name', fn ($query) => $query->where('is_recurring', false)->orderBy('position')),
                        Forms\Components\TextInput::make('service_plan_selling_price')
                            ->numeric()
                            ->prefix('£'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with([
                'warrantyProduct.coverLevel',
            ]))
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('subject')
                    ->searchable(),
                Tables\Columns\TextColumn::make('warrantyProduct.coverLevel.name')
                    ->placeholder('N/A')
                    ->formatStateUsing(fn (Offer $model) => $model->warrantyProduct?->getLabel())
                    ->sortable(),
                Tables\Columns\TextColumn::make('breakdownProduct.name')
                    ->placeholder('N/A')
                    ->formatStateUsing(fn (Offer $model) => $model->breakdownProduct?->getLabel())
                    ->sortable(),
                Tables\Columns\TextColumn::make('servicePlanProduct.name')
                    ->placeholder('N/A')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOffers::route('/'),
            'create' => Pages\CreateOffer::route('/create'),
            'edit' => Pages\EditOffer::route('/{record}/edit'),
        ];
    }
}
