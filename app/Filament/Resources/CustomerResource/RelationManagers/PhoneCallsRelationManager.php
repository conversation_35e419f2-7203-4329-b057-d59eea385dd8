<?php

namespace App\Filament\Resources\CustomerResource\RelationManagers;

use App\Livewire\ListPhoneCalls;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class PhoneCallsRelationManager extends RelationManager
{
    protected static string $relationship = 'phoneCalls';

    public function table(Table $table): Table
    {
        $listPhoneCalls = app(ListPhoneCalls::class);
        $listPhoneCalls->customer = $this->getOwnerRecord();

        return $listPhoneCalls->table($table);
    }
}
