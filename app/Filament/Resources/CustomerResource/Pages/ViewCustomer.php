<?php

namespace App\Filament\Resources\CustomerResource\Pages;

use App\Filament\Resources\CustomerResource;
use App\Filament\Resources\SaleResource;
use App\Models\Customer;
use App\Models\Sale;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class ViewCustomer extends ViewRecord
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('impersonate_in_customer_portal')
                ->icon('impersonate-icon')
                ->visible(fn (Customer $customer) => Auth::user()->can('impersonate', $customer))
                ->url(fn (Customer $customer) => route('customers.impersonate', $customer))
                ->openUrlInNewTab(),
            Actions\EditAction::make()->outlined(),
            Actions\CreateAction::make()
                ->color('success')
                ->label('New Sale')
                ->visible(fn (): bool => Gate::allows('create', Sale::class))
                ->url(
                    fn (): string => SaleResource::getUrl('create', [
                        'customer' => $this->record,
                    ])
                ),
        ];
    }
}
