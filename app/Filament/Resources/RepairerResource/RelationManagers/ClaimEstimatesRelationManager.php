<?php

namespace App\Filament\Resources\RepairerResource\RelationManagers;

use App\Filament\Resources\ClaimEstimateResource;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class ClaimEstimatesRelationManager extends RelationManager
{
    protected static string $relationship = 'claimEstimates';

    public function table(Table $table): Table
    {
        return ClaimEstimateResource::table($table, showWorkshopColumns: false);
    }
}
