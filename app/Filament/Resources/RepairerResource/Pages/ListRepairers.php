<?php

namespace App\Filament\Resources\RepairerResource\Pages;

use App\Filament\Resources\RepairerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRepairers extends ListRecords
{
    protected static string $resource = RepairerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
