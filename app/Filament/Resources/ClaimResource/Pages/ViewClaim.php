<?php

namespace App\Filament\Resources\ClaimResource\Pages;

use App\Filament\Resources\ClaimResource;
use App\Filament\Widgets\ClaimValidation;
use App\Filament\Widgets\EstimateCard;
use App\Filament\Widgets\EstimatesHeader;
use App\Filament\Widgets\FaultInformation;
use App\Filament\Widgets\VehicleInformation;
use App\Models\Claim;
use App\Models\ClaimEstimate;
use App\Models\ClaimRejection;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class ViewClaim extends ViewRecord
{
    protected static string $resource = ClaimResource::class;

    protected static string $view = 'filament.pages.claims.show';

    public function getTitle(): string|Htmlable
    {
        return view('components.headers.warranty-claim', ['claim' => $this->record]);
    }

    protected function getHeaderActions(): array
    {
        return [
            $this->rejectClaimAction(),
            Actions\EditAction::make(),
            Actions\Action::make('browserPlugin')
                ->view('filament.actions.browser-plugin'),
        ];
    }

    public function getHeaderWidgetsColumns(): int|string|array
    {
        return 3;
    }

    protected function getHeaderWidgets(): array
    {
        return [
            VehicleInformation::make(['sale' => $this->record->warranty->sale]),
            FaultInformation::make(['claim' => $this->record]),
            ClaimValidation::make(['claim' => $this->record]),
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            EstimatesHeader::make(['claim' => $this->record]),
            ...$this->record->estimates->map(function (ClaimEstimate $estimate, $index) {
                return EstimateCard::make(['claimEstimate' => $estimate, 'index' => $index]);
            })->all(),
        ];
    }

    private function rejectClaimAction(): Action
    {
        $reasons = config('protego.claims.rejection.reasons');

        return Action::make('reject_claim')
            ->visible(fn (Claim $claim) => Gate::allows('create', [ClaimRejection::class, $claim]))
            ->slideOver()
            ->form([
                Forms\Components\Fieldset::make()
                    ->schema([
                        Forms\Components\Select::make('reason')
                            ->columnSpanFull()
                            ->options(array_combine($reasons, $reasons))
                            ->required(),
                        Forms\Components\Textarea::make('notes')
                            ->autosize()
                            ->placeholder('Add any notes backing up the reason for rejecting this claim.')
                            ->columnSpanFull()
                            ->required(),
                    ]),
            ])
            ->action(function (Claim $claim, array $data, Action $action) {
                $claim->rejection()->create(array_merge($data, [
                    'user_id' => Auth::id(),
                ]));
                $action->success();
            })
            ->successRedirectUrl(fn () => ClaimResource::getUrl('view', ['record' => $this->record]));
    }
}
