<?php

namespace App\Filament\Resources\ClaimResource\Pages;

use App\Enums\ClaimStatus;
use App\Filament\Resources\ClaimResource;
use App\Jobs\ProcessClaimEstimate;
use App\Models\Claim;
use App\Models\ClaimEstimate;
use App\Models\File;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Set;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Gate;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class ListClaims extends ListRecords
{
    protected static string $resource = ClaimResource::class;

    protected static ?string $title = 'Warranty Claims';

    protected ?string $maxContentWidth = 'full';

    protected function getHeaderActions(): array
    {
        return [
            ActionGroup::make([
                Action::make('Warranty Claims Export')
                    ->visible(Gate::allows('exportAll', Claim::class))
                    ->url(fn () => route('reports.claims', [
                        'start' => $this->tableFilters['failure_date']['date_from'] ?? null,
                        'end' => $this->tableFilters['failure_date']['date_to'] ?? null,
                    ]))
                    ->openUrlInNewTab(),
            ])->label('Exports')->button(),
            $this->uploadEstimateAction(),
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(),
            'awaiting-estimate' => $this->makeTabForStatus(ClaimStatus::AWAITING_ESTIMATE, 'warning'),
            'authorised' => $this->makeTabForStatus(ClaimStatus::AUTHORISED, 'success'),
            'rejected' => $this->makeTabForStatus(ClaimStatus::REJECTED, 'danger'),
        ];
    }

    private function makeTabForStatus(ClaimStatus $status, string $badgeColor): Tab
    {
        return Tab::make()
            ->badge(Claim::whereHas('warranty')->withStatus($status)->count())
            ->badgeColor($badgeColor)
            ->modifyQueryUsing(fn (Builder $query) => $query->withStatus($status));
    }

    private function uploadEstimateAction(): Action
    {
        return Action::make('upload_estimate')
            ->visible(fn (Claim $claim) => Gate::allows('create', [ClaimEstimate::class, $claim]))
            ->color('success')
            ->label('Upload Estimate')
            ->slideOver()
            ->form([
                FileUpload::make('file')
                    ->label('Upload a claim estimate. The system will detect the vehicle registration and apply the estimate to an open claim, or create a new claim if necessary.')
                    ->afterStateUpdated(function (TemporaryUploadedFile $state, Set $set) {
                        $file = File::create([
                            'name' => $state->getClientOriginalName(),
                            'path' => $state->store('vault'),
                            'size' => $state->getSize(),
                            'mime_type' => $state->getMimeType(),
                        ]);
                        $set('file_id', $file->id);
                    })
                    ->helperText('Accepted file types: PDF, EML')
                    ->required(),
                Hidden::make('file_id'),
            ])
            ->action(function (Claim $claim, array $data, Action $action) {
                $file = File::find($data['file_id']);

                ProcessClaimEstimate::dispatch($file, $claim);

                $action->success();
            });
    }
}
