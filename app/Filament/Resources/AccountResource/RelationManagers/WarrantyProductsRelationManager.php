<?php

namespace App\Filament\Resources\AccountResource\RelationManagers;

use App\Models\AccountWarrantyProduct;
use App\Models\BreakdownProduct;
use App\Models\CoverLevel;
use App\Models\WarrantyProduct;
use Closure;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class WarrantyProductsRelationManager extends RelationManager
{
    protected static string $relationship = 'warrantyProducts';

    protected static ?string $title = 'Warranty Products';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->columns(2)
                    ->schema([
                        Forms\Components\Select::make('cover_level_id')
                            ->required()
                            ->label('Cover Level')
                            ->placeholder('Select Cover Level')
                            ->live()
                            ->options(fn () => CoverLevel::orderBy('id')->pluck('name', 'id'))
                            ->formatStateUsing(fn (?AccountWarrantyProduct $record) => $record?->product->cover_level_id),
                        Forms\Components\Select::make('product_id')
                            ->required()
                            ->label('Warranty Product')
                            ->placeholder('Select Warranty Product')
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state === null) {
                                    $set('is_bundle', false);
                                }
                            })
                            ->options(fn (Forms\Get $get) => WarrantyProduct::query()
                                ->with('coverLevel')
                                ->where('cover_level_id', $get('cover_level_id'))
                                ->orderBy('position')
                                ->get(['id', 'cover_level_id', 'period', 'is_recurring'])
                                ->mapWithKeys(fn (WarrantyProduct $product) => [$product->id => $product->getLabel()])
                            )
                            ->getOptionLabelFromRecordUsing(fn (WarrantyProduct $product) => $product->getLabel())
                            ->required(),
                    ]),
                Forms\Components\Section::make()
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('max_engine_capacity')
                            ->placeholder('Leave blank for no limit')
                            ->numeric(),
                        Forms\Components\TextInput::make('max_mileage')
                            ->placeholder('Leave blank for no limit')
                            ->numeric(),
                        Forms\Components\TextInput::make('annual_mileage_limit')
                            ->required()
                            ->minValue(5000)
                            ->maxValue(25000)
                            ->default(12000)
                            ->numeric(),
                        Forms\Components\TextInput::make('max_age')
                            ->placeholder('Leave blank for no limit')
                            ->numeric(),
                    ]),
                Forms\Components\Section::make()
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('provision')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('selling_price')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('admin_fee')
                            ->visible($this->getOwnerRecord()->warranty_fund_type->isDealerFunded())
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                    ]),
                Forms\Components\Section::make()
                    ->visible(function (Forms\Get $get) {
                        $productId = $get('product_id');
                        if (! $productId) {
                            return false;
                        }

                        return WarrantyProduct::find($productId)->is_recurring;
                    })
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('monthly_selling_price')
                            ->hint('(Gross)')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('monthly_admin_fee')
                            ->hint('(NET)')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                    ]),
                Forms\Components\Section::make()
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('individual_claim_limit')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('total_claim_limit')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                    ]),
                Forms\Components\Section::make('Bundled Breakdown Product')
                    ->relationship(
                        name: 'bundledBreakdownProduct',
                        condition: fn (Forms\Get $get) => (bool) $get('bundledBreakdownProduct.breakdown_product_id'),
                    )
                    ->mutateRelationshipDataBeforeCreateUsing($this->mutateBundledProductRelationship())
                    ->mutateRelationshipDataBeforeSaveUsing($this->mutateBundledProductRelationship())
                    ->columns(2)
                    ->schema([
                        Forms\Components\Select::make('breakdown_product_id')
                            ->live()
                            ->label('Select Breakdown Product')
                            ->placeholder('No bundled breakdown product')
                            ->relationship('breakdownProduct', 'name', fn ($query) => $query
                                ->orderBy('position')
                            )
                            ->getOptionLabelFromRecordUsing(fn (BreakdownProduct $breakdownProduct) => $breakdownProduct->getLabel()),
                        Forms\Components\Grid::make(3)
                            ->visible(fn (Forms\Get $get) => $get('breakdown_product_id'))
                            ->schema([
                                Forms\Components\TextInput::make('provision')
                                    ->required()
                                    ->helperText('This is accounted for separately to the provision for the warranty product')
                                    ->numeric()
                                    ->prefix('£'),
                                Forms\Components\TextInput::make('admin_fee')
                                    ->visible($this->getOwnerRecord()->breakdown_fund_type->isDealerFunded())
                                    ->helperText('This is charged in addition to the admin fee for the warranty product')
                                    ->required()
                                    ->numeric()
                                    ->prefix('£'),
                            ]),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->paginated(false)
            ->modifyQueryUsing(fn ($query) => $query->with('product.coverLevel', 'bundledBreakdownProduct.breakdownProduct'))
            ->defaultGroup(
                Tables\Grouping\Group::make('product.position')
                    ->titlePrefixedWithLabel(false)
                    ->getDescriptionFromRecordUsing(fn (AccountWarrantyProduct $accountWarrantyProduct) => $accountWarrantyProduct->product->is_recurring ? 'Subscription' : 'Paid via dealer')
                    ->getTitleFromRecordUsing(fn (AccountWarrantyProduct $accountWarrantyProduct) => $accountWarrantyProduct->getLabel())
            )
            ->columns([
                Tables\Columns\TextColumn::make('max_engine_capacity')
                    ->numeric()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('max_mileage')
                    ->numeric()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('annual_mileage_limit')
                    ->numeric()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('max_age')
                    ->numeric()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('provision')
                    ->money()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('selling_price')
                    ->money()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('admin_fee')
                    ->visible($this->getOwnerRecord()->warranty_fund_type->isDealerFunded())
                    ->money()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('monthly_selling_price')
                    ->money()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('monthly_admin_fee')
                    ->money()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('individual_claim_limit')
                    ->money()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_claim_limit')
                    ->money()
                    ->alignCenter()
                    ->placeholder('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Add Product')
                    ->mutateFormDataUsing($this->mutateFormDataUsing())
                    ->slideOver(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing($this->mutateFormDataUsing())
                    ->slideOver(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }

    protected function mutateBundledProductRelationship(): Closure
    {
        return function (array $data) {
            if (! $this->getOwnerRecord()->breakdown_self_funded) {
                $data['admin_fee'] = $data['provision'];
            }

            return $data;
        };
    }

    protected function mutateFormDataUsing(): Closure
    {
        return function (array $data) {
            unset($data['cover_level_id']);

            if (! $this->getOwnerRecord()->warranty_fund_type->isDealerFunded()) {
                $data['admin_fee'] = $data['provision'];
            }

            return $data;
        };
    }
}
