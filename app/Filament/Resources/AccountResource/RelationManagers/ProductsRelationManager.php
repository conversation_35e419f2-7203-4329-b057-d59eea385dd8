<?php

namespace App\Filament\Resources\AccountResource\RelationManagers;

use App\Models\AccountProduct;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ProductsRelationManager extends RelationManager
{
    protected static string $relationship = 'products';

    protected static ?string $title = 'Other Products';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_group_id')
                    ->disabled(fn (AccountProduct $accountProduct) => $accountProduct->exists)
                    ->live()
                    ->relationship('productGroup', 'name', fn ($query) => $query->reorder('id'))
                    ->formatStateUsing(fn (AccountProduct $accountProduct) => $accountProduct->product?->product_group_id)
                    ->dehydrated(false)
                    ->required(),
                Forms\Components\Select::make('product_id')
                    ->disabled(fn (AccountProduct $accountProduct) => $accountProduct->exists)
                    ->live()
                    ->relationship('product', 'name', fn (Forms\Get $get, $query) => $query
                        ->where('product_group_id', $get('product_group_id'))
                        ->reorder('id'))
                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                        $product = Product::find($get('product_id'));
                        $set('cost_price', $product?->cost_price);
                        $set('selling_price', $product?->selling_price);
                        $set('dealer_commission', $product?->dealer_commission);
                    })
                    ->required(),
                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('cost_price')
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('selling_price')
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('dealer_commission')
                            ->numeric()
                            ->prefix('£'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->paginated(false)
            ->columns([
                Tables\Columns\TextColumn::make('product.group.name')->label('Product Group')
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.name')->label('Product')
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_price')
                    ->money()
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\TextColumn::make('selling_price')
                    ->money()
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\TextColumn::make('dealer_commission')
                    ->money()
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Add Product')
                    ->slideOver(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->slideOver(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }
}
