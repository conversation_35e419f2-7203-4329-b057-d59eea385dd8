<?php

namespace App\Filament\Resources\AccountResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use OvonGroup\ManufacturerLogos\Models\Manufacturer;

class ManufacturerSurchargesRelationManager extends RelationManager
{
    protected static string $relationship = 'manufacturerSurcharges';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('manufacturer_id')
                    ->relationship('manufacturer', 'name')
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\TextInput::make('warranty_admin_fee_percentage')
                    ->label('Warranty Admin Fee %')
                    ->numeric()
                    ->minValue(-100)
                    ->maxValue(100)
                    ->suffix('%'),
                Forms\Components\TextInput::make('warranty_provision_percentage')
                    ->label('Warranty Provision %')
                    ->numeric()
                    ->minValue(-100)
                    ->maxValue(100)
                    ->suffix('%'),
                Forms\Components\TextInput::make('warranty_selling_price_percentage')
                    ->label('Warranty Selling Price %')
                    ->numeric()
                    ->minValue(-100)
                    ->maxValue(100)
                    ->suffix('%'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->paginated(false)
            ->defaultSort(fn ($query) => $query->orderBy(
                Manufacturer::select('name')->whereColumn('manufacturer_id', 'manufacturers.id'))
            )
            ->columns([
                Tables\Columns\TextColumn::make('manufacturer.name'),
                Tables\Columns\TextColumn::make('warranty_admin_fee_percentage')
                    ->numeric()
                    ->alignRight()
                    ->suffix('%'),
                Tables\Columns\TextColumn::make('warranty_provision_percentage')
                    ->numeric()
                    ->alignRight()
                    ->suffix('%'),
                Tables\Columns\TextColumn::make('warranty_selling_price_percentage')
                    ->numeric()
                    ->alignRight()
                    ->suffix('%'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->slideOver(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->slideOver(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
