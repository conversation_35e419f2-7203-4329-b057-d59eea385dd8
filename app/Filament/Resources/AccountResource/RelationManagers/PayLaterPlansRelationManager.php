<?php

namespace App\Filament\Resources\AccountResource\RelationManagers;

use App\Actions\PayLater\PullPayLaterPlansAction;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class PayLaterPlansRelationManager extends RelationManager
{
    protected static string $relationship = 'payLaterPlans';

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        if (! $ownerRecord->hasPayLater()) {
            return false;
        }

        return parent::canViewForRecord($ownerRecord, $pageClass);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('commission_rate_margin')
                    ->label('Dealer Uplift')
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(20)
                    ->nullable()
                    ->suffix('%'),
                Toggle::make('is_active')
                    ->label('Active')
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('apr')
                    ->label('APR')
                    ->numeric()
                    ->suffix('%')->alignCenter(),
                IconColumn::make('deposit')
                    ->boolean()
                    ->alignCenter(),
                TextColumn::make('commission_rate')
                    ->label('Protego Commission Rate')
                    ->placeholder('N/A')
                    ->numeric()
                    ->suffix('%')
                    ->alignCenter(),
                TextColumn::make('commission_rate_margin')
                    ->label('Dealer Uplift')
                    ->placeholder('N/A')
                    ->numeric()
                    ->suffix('%')
                    ->alignCenter(),
                TextColumn::make('min_amount')
                    ->label('Min Amount')
                    ->placeholder('N/A')
                    ->money(divideBy: 100)
                    ->alignCenter(),
                TextColumn::make('max_amount')
                    ->label('Max Amount')
                    ->placeholder('N/A')
                    ->money(divideBy: 100)
                    ->alignCenter(),
                IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->alignCenter(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('pull_from_api')
                    ->label('Pull plans from Pay Later Service')
//                    ->visible(fn () => $this->getOwnerRecord()->hasPayLater())
                    ->action(fn (PullPayLaterPlansAction $pullPayLaterPlans) => $pullPayLaterPlans->execute($this->getOwnerRecord())),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ]);
    }
}
