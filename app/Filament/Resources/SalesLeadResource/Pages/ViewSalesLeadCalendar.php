<?php

namespace App\Filament\Resources\SalesLeadResource\Pages;

use App\Filament\Resources\SalesLeadResource;
use Filament\Resources\Pages\Page;

class ViewSalesLeadCalendar extends Page
{
    protected static string $resource = SalesLeadResource::class;

    protected static string $view = 'filament.resources.sales-lead-resource.pages.sales-lead-call-calendar';

    public static function canAccess(array $parameters = []): bool
    {
        return SalesLeadResource::canAccess();
    }

    public function getHeaderWidgets(): array
    {
        return [
            SalesLeadResource\Widgets\SalesLeadCallCalendar::class,
        ];
    }
}
