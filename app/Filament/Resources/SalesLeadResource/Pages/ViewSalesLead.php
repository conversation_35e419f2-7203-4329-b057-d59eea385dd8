<?php

namespace App\Filament\Resources\SalesLeadResource\Pages;

use App\Filament\Resources\SalesLeadResource;
use Filament\Resources\Pages\ViewRecord;

class ViewSalesLead extends ViewRecord
{
    protected static string $resource = SalesLeadResource::class;

    public function getTitle(): string
    {
        return parent::getTitle().' - '.$this->getRecord()->sale->customer->full_name;
    }

    protected ?string $subheading = 'Customer information and interaction history';
}
