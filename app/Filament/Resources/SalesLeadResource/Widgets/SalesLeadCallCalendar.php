<?php

namespace App\Filament\Resources\SalesLeadResource\Widgets;

use App\Filament\Resources\SalesLeadResource;
use App\Models\SalesLeadCallOutcome;
use Guava\Calendar\ValueObjects\CalendarEvent;
use Guava\Calendar\Widgets\CalendarWidget;
use Illuminate\Support\Collection;
use Livewire\Attributes\Js;

class SalesLeadCallCalendar extends CalendarWidget
{
    protected bool $eventClickEnabled = true;

    #[Js]
    public function getDayHeaderFormatJs(): ?string
    {
        return <<<'JS'
            function (date) {
                return date.toLocaleDateString();
            }
JS;
    }

    public function getEvents(array $fetchInfo = []): Collection|array
    {
        $subQuery = SalesLeadCallOutcome::query()
            ->selectRaw('MAX(id) as id')
            ->whereNotNull('callback_date')
            ->where('callback_date', '>=', $fetchInfo['startStr'])
            ->where('callback_date', '<=', $fetchInfo['endStr'])
            ->has('salesLead.sale')
            ->groupBy('sales_lead_id');

        return SalesLeadCallOutcome::query()
            ->with('salesLead.sale.customer')
            ->joinSub($subQuery, as: 'sub', first: function ($join) {
                $join->on('sales_lead_call_outcomes.id', '=', 'sub.id');
            })
            ->get()
            ->map(fn (SalesLeadCallOutcome $salesLeadCallOutcome) => CalendarEvent::make($salesLeadCallOutcome)
                ->title($salesLeadCallOutcome->salesLead->sale->customer->full_name)
                ->start($salesLeadCallOutcome->callback_date)
                ->end($salesLeadCallOutcome->callback_date->addMinutes(10))
                ->backgroundColor($salesLeadCallOutcome->outcome->calendarColor())
                ->url(SalesLeadResource::getUrl('view', ['record' => $salesLeadCallOutcome->salesLead]))
            )
            ->all();
    }
}
