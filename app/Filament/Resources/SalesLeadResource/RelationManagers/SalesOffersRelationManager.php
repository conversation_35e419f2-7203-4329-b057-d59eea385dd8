<?php

namespace App\Filament\Resources\SalesLeadResource\RelationManagers;

use App\Models\Offer;
use App\Models\SalesOffer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Attributes\On;

#[On('sales-offer-created')]
class SalesOffersRelationManager extends RelationManager
{
    protected static string $relationship = 'offers';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading('Offers Sent')
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('offer.name')->sortable(),
                Tables\Columns\TextColumn::make('offer')
                    ->label('Price')
                    ->formatStateUsing(fn (Offer $state) => '£'.number_format($state->getTotalSellingPrice()))
                    ->sortable(),
                Tables\Columns\TextColumn::make('sent_at')
                    ->placeholder('Not sent')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('resend')
                    ->action(fn (SalesOffer $record) => $record->send()),
            ]);
    }
}
