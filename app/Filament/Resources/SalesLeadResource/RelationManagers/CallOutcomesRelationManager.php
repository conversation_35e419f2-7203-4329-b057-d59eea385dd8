<?php

namespace App\Filament\Resources\SalesLeadResource\RelationManagers;

use App\Enums\CallOutcome;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Attributes\On;

#[On('call-outcome-created')]
class CallOutcomesRelationManager extends RelationManager
{
    protected static string $relationship = 'callOutcomes';

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('outcome')
            ->defaultSort('created_at', 'desc')
            ->paginated(false)
            ->columns([
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Recorded at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('outcome')
                    ->badge()
                    ->color(fn (CallOutcome $state) => $state->color())
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('callback_date')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('N/A'),
                Tables\Columns\TextColumn::make('notes')
                    ->wrap()
                    ->placeholder('-'),
            ])
            ->filters([
                //
            ]);
    }
}
