<?php

namespace App\Filament\Resources\PayoutResource\RelationManagers;

use App\Models\PayoutLineItem;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class PayoutLineItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'lineItems';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('type')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('type')
            ->paginated(false)
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color('success')
                    ->formatStateUsing(function (Model $record) {
                        return strtoupper(str_replace('_', ' ', $record->type));
                    })
                    ->label('Type'),
                Tables\Columns\TextColumn::make('payment.processor_payment_id')
                    ->url(fn (PayoutLineItem $record) => "/payments/{$record->payment_id}"),
                Tables\Columns\TextColumn::make('amount')->money(),
                Tables\Columns\TextColumn::make('tax')->money(),
            ]);
    }
}
