<?php

namespace App\Filament\Resources\PayoutResource\Pages;

use App\Actions\CreateXeroBankTransferForPayout;
use App\Filament\Resources\PayoutResource;
use App\Models\Payout;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewPayout extends ViewRecord
{
    protected static string $resource = PayoutResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('view_in_xero')
                ->label('View in Xero')
                ->visible(fn (Payout $record) => $record->accountingSoftwareViewTransactionUrl())
                ->url(fn (Payout $record) => $record->accountingSoftwareViewTransactionUrl())
                ->openUrlInNewTab(),
            Actions\Action::make('create_transfer_in_xero')
                ->label('Create transfer in Xero')
                ->hidden(fn (Payout $record) => $record->accountingSoftwareViewTransactionUrl())
                ->action(fn (CreateXeroBankTransferForPayout $createXeroBankTransferForPayout, Payout $record) => $createXeroBankTransferForPayout->execute($record)),
            Actions\Action::make('view_in_gocardless')
                ->label('View in GoCardless')
                ->url(fn (Payout $record) => $record->goCardlessUrl())
                ->openUrlInNewTab(),
        ];
    }
}
