<?php

namespace App\Filament\Resources\WorkflowResource\Pages;

use App\Filament\Resources\WorkflowResource;
use App\Models\Workflow;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewWorkflow extends ViewRecord
{
    protected static string $resource = WorkflowResource::class;

    protected function getHeaderActions(): array
    {
        return [
//            Actions\Action::make('delete_sales_leads')
//                ->outlined()
//                ->action(function (Actions\Action $action, Workflow $workflow) {
//                    $count = $workflow->salesLeads()
//                        ->whereDoesntHave('offers')
//                        ->whereDoesntHave('callOutcomes')
//                        ->whereDoesntHave('workflows', fn ($q) => $q->where('workflows.id', '!=', $workflow->id))
//                        ->delete();
//
//                    $action->successNotificationTitle(sprintf('%s sales leads were deleted.', $count));
//
//                    $action->success();
//                })
//                ->requiresConfirmation(),
            Actions\Action::make('run_workflow')
                ->outlined()
                ->action(function (Actions\Action $action, Workflow $workflow) {
                    $query = $workflow->getQuery();
                    $count = $query->count();

                    $workflow->run($query);
                    $action->successNotificationTitle(sprintf('This workflow created %s sales leads', $count));
                    $action->success();
                })
                ->requiresConfirmation()
                ->modalHeading(fn (Workflow $record) => sprintf('This workflow will create leads for %d sales', $record->getQuery()->count())),
            Actions\EditAction::make(),
        ];
    }
}
