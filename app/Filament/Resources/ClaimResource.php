<?php

namespace App\Filament\Resources;

use App\Enums\ClaimStatus;
use App\Filament\Filters\DateRangeFilter;
use App\Filament\Packages\FileVault\Infolists\Components\FileVaultEntry;
use App\Filament\Resources\ClaimEstimateResource\Pages\CreateClaimEstimate;
use App\Filament\Resources\ClaimEstimateResource\Pages\EditClaimEstimate;
use App\Filament\Resources\ClaimEstimateResource\Pages\ViewClaimEstimate;
use App\Filament\Resources\ClaimResource\Pages;
use App\Filament\Resources\SaleResource\Pages\ViewWarranty;
use App\Filament\Tables\Columns\CustomerColumn;
use App\Filament\Tables\Columns\VehicleColumn;
use App\Livewire\AiAssistant;
use App\Models\Claim;
use App\Models\Warranty;
use App\Services\AI\WarrantyClaimRejectionMessageWriterTask;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Split;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Average;
use Filament\Tables\Columns\Summarizers\Count;
use Filament\Tables\Columns\Summarizers\Range;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Parallax\FilamentComments\Infolists\Components\CommentsEntry;
use Parallax\FilamentComments\Models\FilamentComment;

class ClaimResource extends Resource
{
    protected static ?string $model = Claim::class;

    protected static ?string $navigationLabel = 'Warranty Claims';

    protected static ?string $slug = 'warranty-claims';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                'estimates.repairer',
                'warranty.sale.customer',
                'warranty.sale.vehicle.manufacturer',
            ])
            ->withExists([
                'estimates',
                'rejection',
            ])
            ->whereHas('warranty');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Customer Information')
                    ->schema([
                        Forms\Components\Select::make('warranty_id')
                            ->visibleOn('create')
                            ->label('Search customer or warranty')
                            ->placeholder('Start typing customer surname, email or vehicle registration')
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search) {
                                return Warranty::query()
                                    ->active()
                                    ->withWhereHas('sale', function ($query) use ($search) {
                                        $query
                                            ->active()
                                            ->where(fn ($q) => $q
                                                ->orwhere('vrm', 'like', "%{$search}%")
                                                ->orWhere('private_plate', 'like', "%{$search}%")
                                                ->orWhere('id', $search)
                                                ->orWhereHas('customer', function (Builder $query) use ($search) {
                                                    $query->where('first_name', 'like', "%{$search}%")
                                                        ->orWhere('last_name', 'like', "%{$search}%")
                                                        ->orWhere('email', 'like', "%{$search}%");
                                                })
                                            );
                                    })
                                    ->with('sale.customer')
                                    ->limit(20)
                                    ->get(['id', 'sale_id'])
                                    ->mapWithKeys(fn (Warranty $warranty) => [$warranty->id => $warranty->sale->vehicle->vrm.' - '.$warranty->sale->customer->full_name]);
                            })
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                $warranty = Warranty::find($get('warranty_id'));
                                $set('customer_email', $warranty->sale->customer->email);
                                $set('customer_phone', $warranty->sale->customer->phone);
                            }),
                        Forms\Components\Grid::make(2)->schema([
                            Forms\Components\TextInput::make('customer_email')
                                ->helperText('Confirm this is the current email address for the customer.'),
                            Forms\Components\TextInput::make('customer_phone')
                                ->helperText('Confirm this is the current phone number for the customer.'),
                        ]),
                    ]),
                Forms\Components\Section::make('Claim Information')
                    ->schema([
                        Forms\Components\Grid::make(2)->schema([
                            Forms\Components\Select::make('fault_type_id')
                                ->relationship('faultType', 'name', fn ($query) => $query->orderBy('position'))
                                ->maxWidth(MaxWidth::ExtraSmall)
                                ->required(),
                            Forms\Components\Select::make('entered_by_id')
                                ->disabled()
                                ->maxWidth(MaxWidth::ExtraSmall)
                                ->hiddenOn('create')
                                ->relationship('enteredBy', 'last_name')
                                ->getOptionLabelFromRecordUsing(fn (Model $user) => $user->getNameAttribute()),
                        ]),
                        Forms\Components\Grid::make(3)->schema([
                            Forms\Components\TextInput::make('reference')
                                ->disabled()
                                ->hiddenOn('create'),
                            Forms\Components\DatePicker::make('failure_date')
                                ->maxWidth(MaxWidth::ExtraSmall)
                                ->required(),
                            Forms\Components\TextInput::make('current_mileage')
                                ->maxWidth(MaxWidth::ExtraSmall)
                                ->numeric(),
                        ]),
                        Forms\Components\Grid::make(2)->schema([
                            Forms\Components\Textarea::make('vehicle_location')
                                ->label('Location of Vehicle')
                                ->autosize()
                                ->required(),
                            Forms\Components\Textarea::make('fault_description')
                                ->label('Description of Fault')
                                ->autosize()
                                ->required(),
                        ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('failure_date', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('reference')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('warranty.account.short_name')
                    ->label('Account')
                    ->visible(fn () => Auth::user()->isViewingAllRecords())
                    ->toggleable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('failure_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->alignCenter()
                    ->formatStateUsing(fn (ClaimStatus $state) => ucwords(str_replace('_', ' ', $state->value)))
                    ->color(fn (Claim $claim): string => $claim->status->color()),
                VehicleColumn::make('warranty.sale.vehicle'),
                CustomerColumn::make('customer'),
                Tables\Columns\TextColumn::make('faultType.name')
                    ->toggleable()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('current_mileage')
                    ->label('Failure Mileage')
                    ->numeric()
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\TextColumn::make('estimate_line_items_sum_net')
                    ->label('Total Authorised')
                    ->sum([
                        'estimateLineItems' => fn ($q) => $q->whereHas('estimate.authorisation'),
                    ], 'net')
                    ->money()
                    ->alignRight()
                    ->sortable()
                    ->summarize([
                        Count::make(),
                        Sum::make()->money(),
                        Average::make()->money(),
                        Range::make(),
                    ]),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                DateRangeFilter::make('failure_date'),
            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(3)
            ->actions([
                Tables\Actions\ViewAction::make()->slideOver(),
                Tables\Actions\EditAction::make()->slideOver(),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema(fn (Claim $claim) => [
                CustomerResource::getCustomerDetailsSection($claim->warranty->sale->customer)
                    ->relationship('warranty.sale.customer')
                    ->collapsed()
                    ->persistCollapsed(),
                SaleResource::getVehicleDetailsInfolistSection('warranty.sale.vehicle')
                    ->collapsed()
                    ->persistCollapsed(),
                ViewWarranty::getInfoListSection($claim->warranty)
                    ->collapsed()
                    ->persistCollapsed(),
                Section::make('Claim Information')
                    ->collapsed()
                    ->persistCollapsed()
                    ->columns(3)
                    ->schema([
                        TextEntry::make('reference'),
                        TextEntry::make('status')
                            ->badge()
                            ->color(fn (Claim $claim): string => $claim->status->color()),
                        TextEntry::make('enteredBy.name')->label('Claim Submitted By')->placeholder('N/A'),
                        TextEntry::make('faultType.name')->label('Fault Type'),
                        TextEntry::make('failure_date')->label('Date of Failure')->date(),
                        TextEntry::make('current_mileage')->label('Mileage at Failure')->placeholder('TBC'),
                        TextEntry::make('rejection.reason')
                            ->label('Rejection Reason')
                            ->visible(fn (Claim $claim) => $claim->rejection),
                        TextEntry::make('rejection.notes')
                            ->label('Rejection Notes')
                            ->visible(fn (Claim $claim) => $claim->rejection),
                        TextEntry::make('vehicle_location')
                            ->label('Location of Vehicle'),
                        TextEntry::make('fault_description')
                            ->label('Description of Fault'),
                    ]),

                AiAssistant::infolistSection('AI Rejection email writer', WarrantyClaimRejectionMessageWriterTask::class)
                    ->columnSpanFull()
                    ->visible(fn (Claim $claim) => $claim->rejection()->exists() && Auth::user()->isAdmin()),

                Split::make([
                    Section::make('Comments')
                        ->collapsible()
                        ->visible(fn (): bool => auth()->user()->can('viewAny', FilamentComment::class))
                        ->schema([
                            CommentsEntry::make('filament_comments'),
                        ]),
                    Section::make('Files')
                        ->collapsible()
                        ->visible(auth()->user()->isAdmin())
                        ->schema([
                            FileVaultEntry::make('file_vault'),
                        ]),
                ])->columnSpanFull(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClaims::route('/'),
            'create' => Pages\CreateClaim::route('/create'),
            'view' => Pages\ViewClaim::route('/{record}'),

            'create-estimate' => CreateClaimEstimate::route('/{claim}/estimates/new'),
            'view-estimate' => ViewClaimEstimate::route('/{claim}/estimates/{record}'),
            'edit-estimate' => EditClaimEstimate::route('/{claim}/estimates/{record}/edit'),
        ];
    }
}
