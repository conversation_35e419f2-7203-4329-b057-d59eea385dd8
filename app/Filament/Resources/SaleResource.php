<?php

namespace App\Filament\Resources;

use App\Enums\ProductStatus;
use App\Enums\VehicleType;
use App\Filament\Filters\DateRangeFilter;
use App\Filament\Resources\SaleResource\Pages;
use App\Filament\Resources\SaleResource\Pages\MotHistory;
use App\Filament\Tables\Columns\CustomerColumn;
use App\Filament\Tables\Columns\ProductStatusColumn;
use App\Filament\Tables\Columns\VehicleColumn;
use App\Filament\Widgets\SalesOverview;
use App\Models\BreakdownPlan;
use App\Models\Claim;
use App\Models\ClaimEstimate;
use App\Models\Invoice;
use App\Models\Sale;
use App\Models\Warranty;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Infolists\Components\Actions\Action;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class SaleResource extends Resource
{
    protected static ?string $model = Sale::class;

    protected static ?string $navigationIcon = 'heroicon-o-presentation-chart-line';

    protected static \Filament\Pages\SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with([
            'customer',
            'vehicle.manufacturer',
            'warranty.sale',
            'breakdownPlan.sale',
            'servicePlan.sale',
            'servicePlan.product',
            'invoiceLineItems.invoice',
        ]);
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        /** @var Sale $sale */
        $sale = $page->getRecord();

        return $page->generateNavigationItems(array_filter([
            Pages\ViewSale::class,
            $sale->warranty ? Pages\ViewWarranty::class : null,
            $sale->breakdownPlan ? Pages\ViewBreakdownPlan::class : null,
            $sale->servicePlan ? Pages\ViewServicePlan::class : null,
            $sale->products()->exists() ? Pages\ViewSaleProducts::class : null,
            MotHistory::class,
        ]));
    }

    public static function resolveRecordRouteBinding(int|string $key): ?Model
    {
        // Remove global scope to allow unconfirmed sales to be completed.
        return app(static::getModel())
            ->resolveRouteBindingQuery(static::getEloquentQuery(), $key, static::getRecordRouteKeyName())
            ->withoutGlobalScope('confirmed')
            ->first();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Vehicle Information')
                    ->relationship('vehicle')
                    ->columns(3)
                    ->schema([
                        TextInput::make('vrm')
                            ->label('VRM')
                            ->required()
                            ->maxLength(7),
                        TextInput::make('private_plate')
                            ->maxLength(7),
                        TextInput::make('vin')
                            ->label('VIN / Chassis Number')
                            ->required()
                            ->maxLength(40),
                        TextInput::make('make')
                            ->required()
                            ->maxLength(100),
                        TextInput::make('model')
                            ->required()
                            ->maxLength(100),
                        TextInput::make('derivative')
                            ->maxLength(100),
                        TextInput::make('engine_capacity')
                            ->numeric(),
                        TextInput::make('colour')
                            ->required()
                            ->maxLength(255),
                        Select::make('body_type')
                            ->required()
                            ->options(VehicleType::toSelectArray())
                            ->in(VehicleType::cases()),
                        Select::make('fuel_type')
                            ->required()
                            ->options([
                                'DIESEL' => 'Diesel',
                                'PETROL' => 'Petrol',
                                'ELECTRICITY' => 'Electricity',
                                'HYBRID ELECTRIC' => 'Hybrid Petrol Electric',
                                'ELECTRIC DIESEL' => 'Hybrid Diesel Electric',
                            ])
                            ->in(fn (Select $component) => array_keys($component->getEnabledOptions())),
                        Select::make('transmission_type')
                            ->required()
                            ->options([
                                'MANUAL' => 'Manual',
                                'AUTOMATIC' => 'Automatic',
                            ])
                            ->in(fn (Select $component) => array_keys($component->getEnabledOptions())),
                        Forms\Components\DatePicker::make('registration_date')
                            ->native(false)
                            ->displayFormat('d/m/Y')
                            ->required(),
                    ]),
                Forms\Components\Section::make('Sale Information')
                    ->columns(3)
                    ->schema([
                        Select::make('dealership_id')
                            ->relationship('dealership', 'name', function (Builder $query, Sale $sale): void {
                                $query->where('account_id', $sale->account_id);
                            })
                            ->default(function (Sale $sale) {
                                if ($sale->account->dealerships->count() === 1) {
                                    return $sale->account->dealerships->first()->id;
                                }

                                return null;
                            })
                            ->required(),
                        Select::make('sales_person_id')
                            ->visible(fn (Sale $sale): bool => $sale->account->salesPeople->isNotEmpty())
                            ->default(function (Sale $sale) {
                                if ($sale->account->salesPeople->count() === 1) {
                                    return $sale->account->salesPeople->first()->id;
                                }

                                return null;
                            })
                            ->relationship('salesPerson', 'name', function (Builder $query, Sale $sale): void {
                                $query->where('account_id', $sale->account_id);
                            }),
                        Forms\Components\DatePicker::make('start_date')
                            ->label('Vehicle collection / delivery date')
                            ->native(false)
                            ->displayFormat('d/m/Y')
                            ->disabled(),
                        TextInput::make('delivery_mileage')
                            ->required()
                            ->numeric()
                            ->rules(['between:0,1000000']),
                        TextInput::make('vehicle_price_paid')
                            ->required()
                            ->numeric()
                            ->rules(['between:1000,250000']),
                        Forms\Components\DatePicker::make('last_service_date'),
                        TextInput::make('last_service_mileage')
                            ->numeric(),
                        Select::make('funding_method')
                            ->required()
                            ->options([
                                'cash' => 'Cash',
                                'finance' => 'Finance',
                                'lease' => 'Lease',
                            ])
                            ->in(fn (Select $component) => array_keys($component->getEnabledOptions())),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->groups([
                Group::make('confirmed_at')
                    ->label('Date Entered')
                    ->collapsible()
                    ->date(),
            ])
            ->columns(self::getTableColumns(showCustomerColumn: true))
            ->filters([
                DateRangeFilter::make('confirmed_at', 'Date entered'),
                SelectFilter::make('warranty.status')
                    ->label('Warranty Status')
                    ->options(ProductStatus::toSelectArray())
                    ->query(function (Builder $query, $state): void {
                        if (isset($state['value'])) {
                            $query->whereHas('warranty', function (Builder $query) use ($state): void {
                                match ($state['value']) {
                                    ProductStatus::CANCELLED->value => $query->cancelled(),
                                    ProductStatus::EXPIRED->value => $query->expired(),
                                    ProductStatus::LIVE->value => $query->live(),
                                    ProductStatus::PENDING->value => $query->pending(),
                                    default => $query,
                                };
                            });
                        }
                    }),
                TernaryFilter::make('warranty.is_recurring')
                    ->label('Warranty Subscription')
                    ->query(function (Builder $query, $state): void {
                        if (isset($state['value'])) {
                            $query->whereHas('warranty', fn (Builder $query) => match ($state['value']) {
                                '1' => $query->whereNotNull('monthly_selling_price'),
                                '0' => $query->whereNull('monthly_selling_price'),
                                default => $query,
                            });
                        }
                    }),
                SelectFilter::make('breakdownPlan.status')
                    ->label('Breakdown Plan Status')
                    ->options(ProductStatus::toSelectArray())
                    ->query(function (Builder $query, $state): void {
                        if (isset($state['value'])) {
                            $query->whereHas('breakdownPlan', function (Builder $query) use ($state): void {
                                match ($state['value']) {
                                    ProductStatus::CANCELLED->value => $query->cancelled(),
                                    ProductStatus::EXPIRED->value => $query->expired(),
                                    ProductStatus::LIVE->value => $query->active(),
                                    ProductStatus::PENDING->value => $query->pending(),
                                    default => $query,
                                };
                            });
                        }
                    }),
                //                TernaryFilter::make('breakdownPlan.is_recurring')
                //                    ->query(function (Builder $query, $state) {
                //                        return $query->whereHas('warranty', fn(Builder $query) => match ($state['value']) {
                //                            '1' => $query->whereNotNull('monthly_selling_price'),
                //                            '0' => $query->whereNull('monthly_selling_price'),
                //                            default => $query,
                //                        });
                //                    }),
                SelectFilter::make('servicePlan.status')
                    ->label('Service Plan Status')
                    ->options(ProductStatus::toSelectArray())
                    ->query(function (Builder $query, $state): void {
                        if (isset($state['value'])) {
                            $query->whereHas('servicePlan', function (Builder $query) use ($state): void {
                                match ($state['value']) {
                                    ProductStatus::CANCELLED->value => $query->cancelled(),
                                    ProductStatus::EXPIRED->value => $query->expired(),
                                    ProductStatus::LIVE->value => $query->active(),
                                    ProductStatus::PENDING->value => $query->pending(),
                                    default => $query,
                                };
                            });
                        }
                    }),
                TernaryFilter::make('servicePlan.is_recurring')
                    ->label('Service Plan Subscription')
                    ->query(fn (Builder $query, $state) => $query
                        ->when(isset($state['value']))
                        ->whereHas('servicePlan.product', fn (Builder $query) => match ($state['value']) {
                            '1' => $query->where('is_recurring', true),
                            '0' => $query->where('is_recurring', false),
                            default => $query,
                        })),
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'active' => 'Active',
                        'active-subscription' => 'Active Subscription',
                        'needs-direct-debit' => 'Needs Direct Debit',
                    ])
                    ->query(fn (Builder $query, $state) => match ($state['value'] ?? null) {
                        'active' => $query->active(),
                        'active-subscription' => $query->activeRecurring(),
                        'needs-direct-debit' => $query->needsDirectDebit(),
                        'failed-subscription-payments' => $query->withFailedSubscriptionPayments(),
                        'incomplete-pay-later-agreements' => $query->withIncompletePayLaterAgreements(),
                        default => $query,
                    }),
            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(3)
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema(fn (Sale $record) => [
                CustomerResource::getCustomerDetailsSection($record->customer)->relationship('customer'),
                static::getVehicleDetailsInfolistSection(),
                static::getSaleDetailsInfolistSection(),
                PayLaterAgreementResource::getAgreementDetailsSection()
                    ->visible(fn (Sale $record) => $record->isPayLater())
                    ->heading('Pay Later Agreement')
                    ->relationship('payLaterAgreement')
                    ->headerActions([
                        Action::make('view_pay_later_agreement')
                            ->url(fn (Sale $record) => PayLaterAgreementResource::getUrl('view', [$record->payLaterAgreement])),
                    ])
                    ->collapsed(false)
                    ->persistCollapsed(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            SaleResource\RelationManagers\PaymentsRelationManager::class,
        ];
    }

    public static function getWidgets(): array
    {
        return [
            SalesOverview::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSales::route('/'),
            'create' => Pages\CreateSale::route('/create'),
            'view' => Pages\ViewSale::route('/{record}'),
            'edit' => Pages\EditSale::route('/{record}/edit'),
            'add-products' => Pages\AddSaleProducts::route('/{record}/add-products'),
            'view-warranty' => Pages\ViewWarranty::route('/{record}/warranty'),
            'view-breakdown-plan' => Pages\ViewBreakdownPlan::route('/{record}/breakdown-plan'),
            'view-service-plan' => Pages\ViewServicePlan::route('/{record}/service-plan'),
            'view-products' => Pages\ViewSaleProducts::route('/{record}/products'),
            'mot-history' => Pages\MotHistory::route('/{record}/mot-history'),
        ];
    }

    public static function getTableColumns(bool $showCustomerColumn = false): array
    {
        return [
            Tables\Columns\TextColumn::make('id')
                ->label('Sale #')
                ->searchable()
                ->sortable(),
            Tables\Columns\TextColumn::make('confirmed_at')
                ->label('Date entered')
                ->date()
                ->sortable(),
            Tables\Columns\TextColumn::make('account.short_name')
                ->label('Account')
                ->toggleable()
                ->visible(fn () => Auth::user()->isViewingAllRecords())
                ->sortable(),
            Tables\Columns\TextColumn::make('dealership.short_name')
                ->label('Dealership')
                ->toggleable()
                ->visible(fn () => Auth::user()->account?->dealerships->count() > 1)
                ->sortable(),
            Tables\Columns\TextColumn::make('start_date')
                ->date()
                ->sortable(),

            CustomerColumn::make('customer.last_name')
                ->visible($showCustomerColumn)
                ->sortable(),

            Tables\Columns\TextColumn::make('salesPerson.name')
                ->toggleable(isToggledHiddenByDefault: true)
                ->sortable(),
            VehicleColumn::make('vehicle'),
            Tables\Columns\TextColumn::make('vehicle.registration_date')
                ->label('Registration Date')
                ->date()
                ->toggleable()
                ->sortable(),
            ProductStatusColumn::make('warranty.status')
                ->recurringBadge(fn (Sale $sale) => $sale->warranty?->isRecurring()),
            ProductStatusColumn::make('breakdownPlan.status')
                ->label('Breakdown Plan')
                ->recurringBadge(fn (Sale $sale) => $sale->breakdownPlan?->isRecurring()),
            ProductStatusColumn::make('servicePlan.status')
                ->label('Service Plan')
                ->recurringBadge(fn (Sale $sale) => $sale->servicePlan?->isRecurring()),
            Tables\Columns\TextColumn::make('updated_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
        ];
    }

    public static function getVehicleDetailsInfolistSection($relationship = 'vehicle'): Section
    {
        return Section::make('Vehicle Information')
            ->relationship($relationship)
            ->columns(3)
            ->schema([
                TextEntry::make('vrm')->label('Registration'),
                TextEntry::make('vehicle')->label('Vehicle')
                    ->getStateUsing(function (Model $record): string {
                        if ($record instanceof ClaimEstimate) {
                            $record = $record->claim->warranty->sale;
                        }
                        if ($record instanceof Claim) {
                            $record = $record->warranty->sale;
                        }

                        return $record->vehicle->details;
                    }),
                TextEntry::make('colour'),
                TextEntry::make('registration_date')
                    ->label('Date of Registration')
                    ->date(),
                TextEntry::make('fuel_type'),
                TextEntry::make('transmission_type'),
                TextEntry::make('vin')->label('VIN / Chassis number'),
            ]);

    }

    public static function getSaleDetailsInfolistSection($relationship = ''): Section
    {
        return Section::make('Sale Information')
            ->relationship($relationship)
            ->columns(3)
            ->schema([
                TextEntry::make('id')->label('Sale #')
                    ->url(fn (string $state) => static::getUrl('view', [$state])),
                TextEntry::make('vehicle_price_paid')
                    ->label('Vehicle Sale Price')
                    ->money(),
                TextEntry::make('dealership.name'),
                TextEntry::make('salesPerson.name')->placeholder('N/A'),
                TextEntry::make('funding_method')
                    ->label('Vehicle funding method')
                    ->badge()->formatStateUsing(fn (string $state): string => strtoupper($state)),
                TextEntry::make('start_date')->date(),
                TextEntry::make('last_service_date')->date()->placeholder('N/A'),
                TextEntry::make('last_service_mileage')->placeholder('N/A')->numeric(),
                TextEntry::make('delivery_mileage')->numeric(),
                TextEntry::make('invoiceLineItems.0.invoice')
                    ->label('Dealer Invoice')
                    ->visible(fn (?Invoice $state) => $state)
                    ->formatStateUsing(fn (?Invoice $state) => $state?->invoice_number)
                    ->url(fn (?Invoice $state) => $state->getAdminUrl()),
            ]);
    }
}
