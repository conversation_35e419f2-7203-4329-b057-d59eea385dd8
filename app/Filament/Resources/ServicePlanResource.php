<?php

namespace App\Filament\Resources;

use App\Enums\ProductStatus;
use App\Filament\Filters\DateRangeFilter;
use App\Filament\Resources\SaleResource\Pages\ViewServicePlan;
use App\Filament\Resources\ServicePlanResource\Pages;
use App\Filament\Tables\Columns\CustomerColumn;
use App\Filament\Tables\Columns\ProductStatusColumn;
use App\Filament\Tables\Columns\VehicleColumn;
use App\Models\ServicePlan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ServicePlanResource extends Resource
{
    protected static ?string $model = ServicePlan::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with([
            'sale.vehicle',
            'sale.customer',
            'sale.vehicle.manufacturer',
        ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('account_id')
                    ->relationship('account', 'name')
                    ->required(),
                Forms\Components\Select::make('sale_id')
                    ->relationship('sale', 'id')
                    ->required(),
                Forms\Components\TextInput::make('service_plan_product_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('duration_years')
                    ->numeric(),
                Forms\Components\DatePicker::make('end_date'),
                Forms\Components\TextInput::make('selling_price')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('admin_fee')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('vat')
                    ->required()
                    ->numeric(),
                Forms\Components\DateTimePicker::make('cancelled_at'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('sale_id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('sale_id')
                    ->label('Sale #')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date entered')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale.dealership.short_name')
                    ->label('Dealership')
                    ->toggleable()
                    ->visible(Auth::user()->isViewingAllRecords() || Auth::user()->account?->dealerships->count() > 1)
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.name')
                    ->description(fn (ServicePlan $servicePlan) => sprintf('%s years', $servicePlan->duration_years))
                    ->label('Product')
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale.start_date')
                    ->label('Start Date')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale.salesPerson.name')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),

                CustomerColumn::make('sale.customer'),

                VehicleColumn::make('vehicle'),

                ProductStatusColumn::make('status')
                    ->recurringBadge(fn (ServicePlan $servicePlan) => $servicePlan->isRecurring()),
                Tables\Columns\IconColumn::make('product.is_recurring')
                    ->label('Subscription')
                    ->boolean()
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('selling_price')
                    ->money()
                    ->alignCenter()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('admin_fee')
                    ->money()
                    ->alignCenter()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('vat')
                    ->label('VAT')
                    ->money()
                    ->alignCenter()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filtersFormColumns(['xl' => 3])
            ->filters([
                DateRangeFilter::make('sale.confirmed_at', 'Date entered'),
                SelectFilter::make('status')
                    ->options(ProductStatus::toSelectArray())
                    ->query(function (Builder $query, $state): void {
                        if (isset($state['value'])) {
                            match ($state['value']) {
                                ProductStatus::CANCELLED->value => $query->cancelled(),
                                ProductStatus::EXPIRED->value => $query->expired(),
                                ProductStatus::LIVE->value => $query->active(),
                                ProductStatus::PENDING->value => $query->pending(),
                                default => $query,
                            };
                        }
                    }),
                TernaryFilter::make('is_recurring')
                    ->label('Subscription')
                    ->query(function (Builder $query, $state) {
                        if (isset($state['value'])) {
                            return $query->whereHas('product', fn (Builder $query) => match ($state['value']) {
                                '1' => $query->where('is_recurring', true),
                                '0' => $query->where('is_recurring', false),
                                default => $query,
                            });
                        }
                    }),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\ViewAction::make()->url(fn (ServicePlan $servicePlan) => ViewServicePlan::getUrl([$servicePlan->sale_id])),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListServicePlans::route('/'),
            'edit' => Pages\EditServicePlan::route('/{record}/edit'),
        ];
    }
}
