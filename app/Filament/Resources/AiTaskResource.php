<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AiTaskResource\Pages;
use App\Filament\Resources\AiTaskResource\RelationManagers;
use App\Models\AiTask;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

class AiTaskResource extends Resource
{
    protected static ?string $model = AiTask::class;

    protected static ?string $label = 'AI Tasks';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->unique(
                                table: 'ai_tasks',
                                column: 'name',
                                ignoreRecord: true
                            )
                            ->maxLength(255),
                    ]),
                Forms\Components\Section::make(fn (AITask $aiTask) => 'Prompt version '.$aiTask->prompt->version_number)
                    ->columns(4)
                    ->relationship('prompt')
                    ->saveRelationshipsUsing(function (AiTask $aiTask, array $state) {
                        // This prevents excessive version numbers being saved to the database.
                        // It has to be done on the Eloquent builder like this, as the relation on the model
                        // creates a weird side effect where the UI doesn't update on save.
                        // Try it if you don't believe me :)

                        $existingPrompt = $aiTask->prompt()->first();
                        if (trim($existingPrompt->prompt) === trim($state['prompt']) && $existingPrompt->model === $state['model']) {
                            return;
                        }
                        $aiTask->prompts()->create([
                            ...Arr::except($state, ['id', 'version_number', 'created_at', 'updated_at']),
                            'version_number' => $aiTask->prompts()->count() + 1,
                        ]);
                    })
                    ->schema([
                        Forms\Components\Textarea::make('change_notes')
                            ->required()
                            ->helperText('Describe the changes made in this version.')
                            ->columnSpanFull(),
                        Forms\Components\Select::make('model')
                            ->options(fn () => collect([
                                'gpt-4o',
                                'gpt-4o-mini',
                                'o1',
                                'o1-mini',
                                'o3-mini',
                                'claude-3-7-sonnet-latest',
                            ])->mapWithKeys(fn ($value) => [$value => $value]))
                            ->required(),
                        Forms\Components\MarkdownEditor::make('prompt')
                            ->toolbarButtons(['bold', 'italic', 'bulletList', 'orderedList', 'heading', 'undo', 'redo'])
                            ->required()
                            ->minHeight('40vh')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('prompts_count')
                    ->counts('prompts'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        TextEntry::make('name'),
                    ]),
                Section::make(fn (AITask $aiTask) => 'Prompt version '.$aiTask->prompt->version_number)
                    ->columns(4)
                    ->relationship('prompt')
                    ->schema([
                        TextEntry::make('change_notes'),
                        TextEntry::make('model'),
                        TextEntry::make('prompt')
                            ->markdown()
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PromptsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAiTasks::route('/'),
            'view' => Pages\ViewAiTask::route('/{record}'),
            'edit' => Pages\EditAiTask::route('/{record}/edit'),
        ];
    }
}
