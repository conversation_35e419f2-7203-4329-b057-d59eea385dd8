<?php

namespace App\Filament\Resources;

use App\Enums\ClaimEstimateLineItemType;
use App\Infolists\Components\RepairerEntry;
use App\Livewire\AiAssistant;
use App\Livewire\Tables\ClaimEstimateLineItemsTable;
use App\Models\Account;
use App\Models\Claim;
use App\Models\ClaimEstimate;
use App\Models\Repairer;
use App\Services\AI\WarrantyClaimAssessorTask;
use App\Services\PlacesAutocomplete\GooglePlacesAutocomplete;
use App\Services\Tax\VatCalculator;
use Closure;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Livewire;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;

class ClaimEstimateResource extends Resource
{
    protected static ?string $model = ClaimEstimate::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->whereHas('claim.warranty');
    }

    public static function getChecklistItems(Account $account): array
    {
        return [
            'vehicle_servicing_requirements' => Lang::get('claims.checklist.vehicle_servicing_requirements'),
            'max_hourly_labour_rate' => Lang::get('claims.checklist.max_hourly_labour_rate', ['max_hourly_labour_rate' => $account->max_hourly_labour_rate]),
            'diagnostic_charge' => Lang::get('claims.checklist.diagnostic_charge'),
        ];
    }

    // TODO move form, and infolist into ViewClaimEstimate and EditClaimEstimate pages and delete this class
    public static function form(Form $form): Form
    {
        return static::getClaimForm($form, $form->getRecord()?->claim);
    }

    public static function getClaimForm(Form $form, ?Claim $claim): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->columns(2)
                    ->schema([
                        Forms\Components\Toggle::make('is_charged_internally')
                            ->label('Internal Claim')
                            ->helperText('Tick this if the repair is being done internally and not billed from an external workshop.')
                            ->live()
                            ->afterStateUpdated(static::afterStateUpdatedClosure()),
                        Forms\Components\Toggle::make('is_invoicing_dealer_direct')
                            ->hidden(fn (Forms\Get $get) => $get('is_charged_internally'))
                            ->label('Invoice to Dealer Directly')
                            ->helperText('Tick this if the external workshop will be billing the dealer directly rather than billing us.'),
                    ]),
                Forms\Components\Section::make('Repairer Information')
                    ->columns(2)
                    ->hidden(fn (Forms\Get $get) => $get('is_charged_internally'))
                    ->schema([
                        Forms\Components\Select::make('repairer_id')
                            ->relationship('repairer', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->live()
                            ->createOptionForm(fn ($form) => [
                                Forms\Components\Select::make('search')
                                    ->label('Google Search')
                                    ->placeholder('Start typing the name of the repairer...')
                                    ->dehydrated(false)
                                    ->searchable()
                                    ->debounce(500)
                                    ->getSearchResultsUsing(function (string $search, GooglePlacesAutocomplete $googlePlacesAutocomplete) {
                                        if (strlen($search) < 3) {
                                            return [];
                                        }

                                        return $googlePlacesAutocomplete->search($search, 'car_repair');
                                    })
                                    ->afterStateUpdated(function (?string $state, GooglePlacesAutocomplete $googlePlacesAutocomplete) use ($form) {
                                        if ($state) {
                                            $placeDetails = $googlePlacesAutocomplete->getPlaceDetails($state);
                                            $form->fill($placeDetails);
                                        }
                                    }),
                                Forms\Components\Grid::make(2)->schema([
                                    Forms\Components\TextInput::make('name')->required(),
                                    Forms\Components\TextInput::make('contact')->label('Contact'),
                                    Forms\Components\TextInput::make('phone')->label('Phone number'),
                                    Forms\Components\TextInput::make('email')->label('Email address')
                                        ->email(),
                                ]),
                                Forms\Components\Hidden::make('address_1'),
                                Forms\Components\Hidden::make('address_2'),
                                Forms\Components\Hidden::make('city'),
                                Forms\Components\Hidden::make('county'),
                                Forms\Components\Hidden::make('postcode'),
                                Forms\Components\Hidden::make('website'),
                                Forms\Components\Hidden::make('place_id'),
                            ])
                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                $repairerId = $get('repairer_id');
                                if ($repairerId) {
                                    $repairer = Repairer::query()->find($repairerId);
                                    $set('workshop_contact', $repairer->contact);
                                    $set('workshop_email', $repairer->email);
                                    $set('workshop_phone', $repairer->phone);
                                }
                            }),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('workshop_contact')->label('Contact')->required(),
                                Forms\Components\TextInput::make('workshop_phone')->label('Phone number')->required(),
                                Forms\Components\TextInput::make('workshop_email')->label('Email address')->email(),
                            ]),
                    ]),
                Forms\Components\Tabs::make()
                    ->activeTab(fn (?ClaimEstimate $record) => $record?->estimate_booking_date?->subDay()->isPast() ? 2 : 1)
                    ->columnSpanFull()
                    ->schema([
                        Forms\Components\Tabs\Tab::make('Book Estimate')
                            ->columns(3)
                            ->schema([
                                Forms\Components\DatePicker::make('estimate_booking_date')
                                    ->maxWidth(MaxWidth::ExtraSmall)
                                    ->minDate(fn ($operation) => $operation === 'create' ? today()->subWeek() : null)
                                    ->maxDate(fn ($operation) => $operation === 'create' ? today()->addMonth() : null)
                                    ->default(today())
                                    ->displayFormat('d/m/Y')
                                    ->native(false)
                                    ->label('Date of estimate booking'),
                                Forms\Components\Select::make('drop_off_time')
                                    ->native(false)
                                    ->searchable()
                                    ->options(static::dropOffTimes())
                                    ->maxWidth(MaxWidth::ExtraSmall),
                                Forms\Components\TextInput::make('reference')
                                    ->label('Job Reference')
                                    ->string()
                                    ->maxLength(50),
                            ]),
                        Forms\Components\Tabs\Tab::make('Add Estimate Details')
                            ->schema([
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\Checkbox::make('estimate_completed')
                                            ->formatStateUsing(fn ($state) => (bool) $state)
                                            ->label('Estimate Completed')
                                            ->hidden(fn (Get $get) => (bool) $get('estimate_completed_at')),

                                        Forms\Components\DateTimePicker::make('estimate_completed_at')
                                            ->maxWidth(MaxWidth::ExtraSmall)
                                            ->disabled()
                                            ->visible(fn (Get $get) => (bool) $get('estimate_completed_at')),

                                        Forms\Components\TextInput::make('current_mileage')
                                            ->maxWidth(MaxWidth::ExtraSmall)
                                            ->numeric()
                                            ->minValue($claim?->warranty->sale->delivery_mileage),
                                        Forms\Components\Textarea::make('work_required')->autosize(),
                                    ]),
                                Forms\Components\Repeater::make('line_items')
                                    ->defaultItems(0)
                                    ->addActionLabel('Add Item')
                                    ->relationship('lineItems')
                                    ->columnSpanFull()
                                    ->columns(6)
                                    ->schema([
                                        Forms\Components\Select::make('type')
                                            ->options(ClaimEstimateLineItemType::toSelectArray())
                                            ->native(false)
                                            ->live()
                                            ->required(),
                                        Forms\Components\Select::make('vehicle_component_id')
                                            ->requiredWithout('description')
                                            ->live()
                                            ->columnSpan(2)
                                            ->label('Component')
                                            ->visible(fn (Get $get) => $get('type') === ClaimEstimateLineItemType::PART->value)
                                            ->relationship('vehicleComponent', 'name')
                                            ->searchable()
                                            ->preload(),
                                        Forms\Components\TextInput::make('description')
                                            ->required(function (Get $get) {
                                                if ($get('type') === ClaimEstimateLineItemType::LABOUR->value) {
                                                    return false;
                                                }
                                                if ($get('vehicle_component_id')) {
                                                    return false;
                                                }

                                                return true;
                                            })
                                            ->validationMessages([
                                                'required_without' => 'Please enter a description',
                                            ])
                                            ->columnSpan(fn (Get $get) => $get('type') === ClaimEstimateLineItemType::PART->value ? 3 : 5),
                                        Forms\Components\TextInput::make('quantity')
                                            ->numeric()
                                            ->required()
                                            ->minValue(0)
                                            ->default(1)
                                            ->live()
                                            ->afterStateUpdated(function (Get $get, Set $set) {
                                                $set('total', number_format($get('quantity') * ($get('amount') + $get('vat')), 2, '.', ''));
                                            }),
                                        Forms\Components\TextInput::make('amount')
                                            ->columnStart(4)
                                            ->label('Unit Price (ex VAT)')
                                            ->numeric()
                                            ->prefix('£')
                                            ->required()
                                            ->minValue(0)
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(static::afterStateUpdatedClosure()),
                                        Forms\Components\TextInput::make('vat')
                                            ->label('VAT')
                                            ->numeric()
                                            ->prefix('£')
                                            ->required()
                                            ->minValue(0)
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(static::afterStateUpdatedClosure(calculateVat: false)),
                                        Forms\Components\TextInput::make('total')
                                            ->numeric()
                                            ->prefix('£')
                                            ->readOnly()
                                            ->dehydrated(false),
                                    ]),
                            ]),
                    ]),
            ]);
    }

    private static function afterStateUpdatedClosure(bool $calculateVat = true): Closure
    {
        // TODO test all flows for internal and direct invoicing
        return function (Forms\Get $get, Forms\Set $set, VatCalculator $vatCalculator) use ($calculateVat): void {
            $isChargedInternally = $get('data.is_charged_internally', true);

            foreach ($get('data.line_items', true) as $id => $lineItem) {
                if ($calculateVat) {
                    $vatAmount = $isChargedInternally ? 0 : $vatCalculator->getVatAmount((float) $lineItem['amount']);
                } else {
                    $vatAmount = $lineItem['vat'];
                }
                $set("data.line_items.{$id}.vat", number_format($vatAmount, 2, '.', ''), true);
                $set("data.line_items.{$id}.total", number_format((float) $lineItem['quantity'] * ((float) $lineItem['amount'] + $vatAmount), 2, '.', ''), true);
            }

            if ($isChargedInternally) {
                $set('is_invoicing_dealer_direct', false);
            }
        };
    }

    private static function formatCurrency($amount): ?string
    {
        return number_format($amount, 2, '.', '');
    }

    public static function table(Table $table, bool $showClaimColumns = true, bool $showWorkshopColumns = true): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('created_at')->label('Entered')->date(),
                Tables\Columns\TextColumn::make('claim.reference')
                    ->visible($showClaimColumns)
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('claim.warranty.account.short_name')
                    ->label('Account')
                    ->visible($showClaimColumns && Auth::user()->isViewingAllRecords())
                    ->toggleable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('billing_status')
                    ->badge()
                    ->state(fn (ClaimEstimate $claimEstimate) => $claimEstimate->billingStatus())
                    ->color(fn (ClaimEstimate $claimEstimate) => $claimEstimate->billingStatusColor()),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->state(fn (ClaimEstimate $claimEstimate) => $claimEstimate->status())
                    ->formatStateUsing(fn (ClaimEstimate $claimEstimate) => $claimEstimate->status()->value)
                    ->color(fn (ClaimEstimate $claimEstimate) => $claimEstimate->statusColor()),
                Tables\Columns\TextColumn::make('enteredBy.name')->label('Entered by'),
                Tables\Columns\TextColumn::make('workshop_name')->visible($showWorkshopColumns),
                Tables\Columns\TextColumn::make('total_estimate')
                    ->state(fn (ClaimEstimate $claimEstimate) => $claimEstimate->totalAmount())
                    ->money()->alignRight(),
                Tables\Columns\TextColumn::make('authorisation.authorised_gross')->label('Authorised Gross')->money()->alignRight(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make('view_estimate')
                    ->url(fn ($record) => ClaimResource::getUrl('view-estimate', ['claim' => $record->claim, 'record' => $record])),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()->schema([
                    Grid::make(4)->schema([
                        TextEntry::make('enteredBy.name')
                            ->label('Entered by')
                            ->placeholder('System'),
                        IconEntry::make('is_charged_internally')
                            ->label('Internal claim')
                            ->boolean(),
                        IconEntry::make('is_invoicing_dealer_direct')
                            ->label('Invoice to dealer directly')
                            ->boolean(),
                        TextEntry::make('reference')
                            ->label('Job Reference')
                            ->placeholder('N/A'),
                    ]),
                    Grid::make(3)->schema([
                        ViewEntry::make('claim.warranty.sale.customer')
                            ->label('Customer')
                            ->view('filament.pages.estimates.customer'),
                        TextEntry::make('estimate_booking_date')
                            ->label('Estimate Booking Date')
                            ->placeholder('N/A')
                            ->formatStateUsing(function (ClaimEstimate $claimEstimate) {
                                return $claimEstimate->estimate_booking_date?->formatLocal().' '.$claimEstimate->drop_off_time;
                            }),
                        TextEntry::make('estimate_completed_at')
                            ->label('Estimate Completed Date')
                            ->placeholder('N/A')
                            ->dateTime(),
                    ]),
                    Grid::make(3)->schema([
                        RepairerEntry::make('repairer')
                            ->visible(fn ($state) => $state),
                        TextEntry::make('workshop_name')
                            ->hidden(fn (ClaimEstimate $record) => (bool) $record->repairer_id),
                        TextEntry::make('work_required'),
                        TextEntry::make('current_mileage')->numeric(),
                    ]),
                    Livewire::make(ClaimEstimateLineItemsTable::class, [
                        'claimEstimate' => $infolist->getRecord(),
                    ]),
                ]),

                Section::make('Authorisation')
                    ->relationship('authorisation')
                    ->columns(3)
                    ->visible(fn (ClaimEstimate $record) => $record->authorisation)
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Authorised at')
                            ->dateTime(),
                        TextEntry::make('work_done'),
                        TextEntry::make('reason')
                            ->label('Reason for Authorisation')
                            ->formatStateUsing(fn ($state) => ucwords(strtolower(str_replace('_', ' ', $state)))),
                    ]),

                AiAssistant::infolistSection('Alex Claims assessor', WarrantyClaimAssessorTask::class)
                    ->columnSpanFull()
                    ->visible(Auth::user()->isAdmin()),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [];
    }

    private static function dropOffTimes(): array
    {
        return collect(range(7, 19))->flatMap(function ($hour) {
            return [
                sprintf('%02d:00', $hour),
                sprintf('%02d:15', $hour),
                sprintf('%02d:30', $hour),
                sprintf('%02d:45', $hour),
            ];
        })->mapWithKeys(fn ($time) => [$time => $time])->toArray();
    }
}
