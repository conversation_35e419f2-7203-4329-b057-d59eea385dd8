<?php

namespace App\Filament\Resources;

use App\Enums\PayLaterAgreementStatus;
use App\Filament\Filters\DateRangeFilter;
use App\Filament\Resources\PayLaterAgreementResource\Pages;
use App\Models\PayLaterAgreement;
use App\Models\PayLaterService;
use App\Models\Sale;
use Filament\Infolists\Components\Actions\Action;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Auth;

class PayLaterAgreementResource extends Resource
{
    protected static ?string $model = PayLaterAgreement::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with('payable', function (MorphTo $morphTo) {
            $morphTo->morphWith([
                Sale::class => ['customer'],
                PayLaterService::class => ['customer'],
            ]);
        });
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date created')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('account.short_name')
                    ->label('Account')
                    ->toggleable()
                    ->visible(fn () => Auth::user()->isViewingAllRecords())
                    ->sortable(),
                Tables\Columns\TextColumn::make('payable.customer.full_name')
                    ->searchable(query: fn (Builder $query, string $search) => $query
                        ->whereHas('payable', fn (Builder $q) => $q
                            ->whereHas('customer', fn (Builder $q) => $q->search($search))))
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_approved')
                    ->label('Pre-approved')
                    ->alignCenter()
                    ->boolean(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->alignCenter()
                    ->formatStateUsing(fn (?PayLaterAgreementStatus $state) => $state->label())
                    ->color(fn (?PayLaterAgreementStatus $state) => $state->color()),
                Tables\Columns\TextColumn::make('payable_type')
                    ->badge()
                    ->alignCenter()
                    ->color('success')
                    ->formatStateUsing(fn (string $state) => strtoupper(str_replace('-', ' ', $state))),
                Tables\Columns\TextColumn::make('payLaterPlan.name')
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                Tables\Columns\TextColumn::make('dealer_payment')
                    ->placeholder('-')
                    ->alignCenter()
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('loan_amount')
                    ->placeholder('-')
                    ->alignCenter()
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_rate')
                    ->label('Dealer Percentage')
                    ->tooltip('The percentage of the loan amount that the dealer pays to the financing provider.')
                    ->placeholder('-')
                    ->alignCenter()
                    ->numeric()
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_rate_margin')
                    ->label('Margin')
                    ->visible(auth()->user()->hasPermissionTo('pay-later-agreements.view-margins'))
                    ->tooltip('The percentage of the loan amount the financing provider rebates to Protego.')
                    ->placeholder('-')
                    ->alignCenter()
                    ->numeric()
                    ->suffix('%')
                    ->sortable(),
            ])
            ->filters([
                DateRangeFilter::make('created_at'),
                Tables\Filters\TernaryFilter::make('is_approved')
                    ->label('Approved')
                    ->query(fn (Builder $query, $state) => $query->when(isset($state['value']), fn (Builder $query) => $query->where('is_approved', $state['value'] ?? false))),
            ])
            ->actions([
            ])
            ->bulkActions([
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema(fn (PayLaterAgreement $record) => array_filter([
                $record->payable?->customer
                    ? CustomerResource::getCustomerDetailsSection($record->payable->customer)
                        ->relationship('payable.customer')
                        ->collapsed(false)
                        ->persistCollapsed()
                    : null,
                static::getAgreementDetailsSection()
                    ->collapsed(false)
                    ->persistCollapsed(),
                static::getPayableDetailsSection()
                    ->collapsed(false)
                    ->persistCollapsed(),
            ]));
    }

    public static function getAgreementDetailsSection(): Section
    {
        return Section::make('Agreement Details')
            ->columns(4)
            ->schema([
                TextEntry::make('created_at')
                    ->label('Date Created')
                    ->dateTime()
                    ->icon('heroicon-o-calendar'),
                IconEntry::make('is_approved')
                    ->label('Pre-approved')
                    ->boolean()
                    ->icon(fn (bool $state): string => $state ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle')
                    ->color(fn (bool $state): string => $state ? 'success' : 'danger'),
                TextEntry::make('status')
                    ->badge()
                    ->placeholder('Pending')
                    ->formatStateUsing(fn (?PayLaterAgreementStatus $state) => $state->label())
                    ->color(fn (?PayLaterAgreementStatus $state) => $state->color()),
                TextEntry::make('payLaterPlan.name')
                    ->label('Plan')
                    ->placeholder('N/A'),
                TextEntry::make('loan_amount')
                    ->label('Loan Amount')
                    ->money('GBP')
                    ->placeholder('N/A'),
                TextEntry::make('dealer_payment')
                    ->label('Dealer Payment')
                    ->money('GBP')
                    ->placeholder('N/A'),
                TextEntry::make('commission_rate')
                    ->label('Dealer Percentage')
                    ->suffix('%')
                    ->placeholder('N/A')
                    ->tooltip('The percentage of the loan amount that the dealer pays to the financing provider.'),
                TextEntry::make('commission_rate_margin')
                    ->label('Margin')
                    ->visible(auth()->user()->hasPermissionTo('pay-later-agreements.view-margins'))
                    ->suffix('%')
                    ->placeholder('N/A')
                    ->tooltip('The percentage of the loan amount the financing provider rebates to Protego.'),
                TextEntry::make('description')
                    ->placeholder('N/A')
                    ->columnSpanFull(),
            ]);
    }

    public static function getPayableDetailsSection(): Section
    {
        return Section::make('Product Information')
            ->columns(3)
            ->headerActions([
                Action::make('view_payable')
                    ->visible(fn (PayLaterAgreement $record) => $record->payable)
                    ->label(fn (PayLaterAgreement $record) => 'View '.$record->payable->getLabel())
                    ->url(fn (PayLaterAgreement $record) => $record->payable->getAdminUrl()),
            ])
            ->schema([
                TextEntry::make('payable_type')
                    ->label('Type')
                    ->badge()
                    ->color('success')
                    ->formatStateUsing(fn (string $state) => strtoupper(str_replace('-', ' ', $state))),
                TextEntry::make('payable.vehicle.vrm')
                    ->label('VRM')
                    ->placeholder('N/A'),
                TextEntry::make('payable.vehicle.make')
                    ->label('Vehicle Make')
                    ->placeholder('N/A')
                    ->visible(fn (?string $state) => $state),
                TextEntry::make('payable.vehicle.model')
                    ->label('Vehicle Model')
                    ->placeholder('N/A')
                    ->visible(fn (?string $state) => $state),
                TextEntry::make('payable.vehicle.registration_date')
                    ->label('Registration Date')
                    ->date()
                    ->placeholder('N/A')
                    ->visible(fn (?string $state) => $state),
                TextEntry::make('account.short_name')
                    ->label('Account')
                    ->placeholder('N/A')
                    ->visible(fn () => Auth::user()->isViewingAllRecords()),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayLaterAgreements::route('/'),
            'view' => Pages\ViewPayLaterAgreement::route('/{record}'),
        ];
    }
}
