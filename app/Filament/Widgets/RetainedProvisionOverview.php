<?php

namespace App\Filament\Widgets;

use App\Models\Account;
use App\Models\ClaimEstimate;
use App\Models\ClaimEstimateLineItem;
use App\Models\Payment;
use App\Models\PaymentLineItem;
use App\Models\Warranty;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class RetainedProvisionOverview extends BaseWidget
{
    public ?string $heading = '';

    public ?array $filters = [];

    public array $additionalFilters = [];

    protected function getStats(): array
    {
        // Merge the "magic" Filament query string filters with the additional filters
        $this->filters = array_merge(
            ['selfFunded' => true],
            $this->filters,
            $this->additionalFilters
        );

        $provision = $this->provision(auth()->user()->account);
        $claims = $this->authorisedClaims(auth()->user()->account);

        return [
            Stat::make('Retained Provision', number_format($provision->value))
                ->description(number_format($provision->count).' sales'),
            Stat::make('Authorised Claims', number_format($claims->value))
                ->description(number_format($claims->count).' claims'),
            Stat::make('P & L', number_format($provision->value - $claims->value)),
        ];
    }

    protected function provision(?Account $account = null)
    {
        $subQuery = Warranty::query()
            ->notCancelled()
            ->withoutGlobalScope('tenant')
            ->selectRaw('SUM(provision) as initial, sale_id')
            ->addSelect([
                'recurring' => PaymentLineItem::query()
                    ->selectRaw('SUM(unit_amount)')
                    ->whereColumn('payable_id', 'warranties.id')
                    ->where('payable_type', 'WARRANTY')
                    ->whereHas('payment', fn ($q) => $q->where('status', Payment::STATUS_PAID_OUT)),
            ])
            ->groupBy('sale_id', 'id')
            ->when($account, fn ($q) => $q->where('account_id', $account->id))
            ->when($this->filters['dates'] ?? false, fn ($q, $dates) => $q
                ->whereDate('warranties.created_at', '>=', $dates[0])
                ->whereDate('warranties.created_at', '<=', $dates[1])
            )
            ->when(isset($this->filters['selfFunded']), fn ($q) => $q->dealerFunded($this->filters['selfFunded']))
            ->when(isset($this->filters['expired']), fn ($q) => $q->expired($this->filters['expired']));

        return DB::table(DB::raw("({$subQuery->toSql()}) as sub"))
            ->mergeBindings($subQuery->getQuery())
            ->selectRaw('SUM(initial + COALESCE(recurring, 0)) as value')
            ->selectRaw('SUM(initial) as initial')
            ->selectRaw('SUM(recurring) as recurring')
            ->selectRaw('COUNT(*) as count')
            ->first();
    }

    protected function authorisedClaims(?Account $account = null)
    {
        $subquery = ClaimEstimate::query()
            ->whereHas('claim.warranty', fn ($q) => $q
                ->when(isset($this->filters['selfFunded']), fn ($q) => $q->dealerFunded($this->filters['selfFunded']))
                ->when(isset($this->filters['expired']), fn ($q) => $q->expired($this->filters['expired']))
                ->when($account, fn ($q) => $q->where('account_id', $account->id))
            )
            ->whereHas('authorisation', fn ($q) => $q->when($this->filters['dates'] ?? false, fn ($q, $dates) => $q
                ->whereDate('created_at', '>=', $dates[0])
                ->whereDate('created_at', '<=', $dates[1])
            ))
            ->select([
                'claim_estimates.claim_id',
                'value' => ClaimEstimateLineItem::query()
                    ->selectRaw('SUM(net)')
                    ->whereColumn('claim_estimate_id', 'claim_estimates.id'),
            ]);

        return DB::table(DB::raw("({$subquery->toSql()}) as sub"))
            ->mergeBindings($subquery->getQuery())
            ->selectRaw('SUM(value) as value')
            ->selectRaw('COUNT(DISTINCT(claim_id)) as count')
            ->first();
    }
}
