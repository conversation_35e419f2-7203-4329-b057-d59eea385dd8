<?php

namespace App\Filament\Widgets;

use App\Enums\FundType;
use Illuminate\Database\Eloquent\Builder;

class BurnRateByAccountSelfFundedChart extends BurnRateByAccountChart
{
    protected static ?string $heading = 'Burn Rate by Account (Dealer Funded)';

    protected function getWheres(Builder $query): Builder
    {
        return $query->whereIn('fund_type', [FundType::DEALER->value, FundType::DEALER_RETAINED->value]);
    }
}
