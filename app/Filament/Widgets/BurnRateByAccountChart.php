<?php

namespace App\Filament\Widgets;

use App\Models\WarrantyBurnRateView;
use Filament\Widgets\ChartWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class BurnRateByAccountChart extends ChartWidget
{
    protected static ?string $heading = 'Burn Rate by Account';

    protected static ?string $pollingInterval = null;

    protected int|string|array $columnSpan = 'full';

    protected static ?string $maxHeight = '250px';

    protected static ?array $options = [
        'scales' => [
            'Y' => [
                'type' => 'linear',
                'display' => true,
                'position' => 'left',
                'title' => [
                    'display' => true,
                    'text' => '£',
                ],
            ],
            'Y2' => [
                'type' => 'linear',
                'display' => true,
                'position' => 'right',
                'title' => [
                    'display' => true,
                    'text' => 'Burn Rate %',
                ],
                'grid' => [
                    'drawOnChartArea' => false, // Prevents grid lines from overlapping
                ],
            ],
        ],
    ];

    protected function getData(): array
    {
        $revenue = WarrantyBurnRateView::query()
            ->select('account_id')
            ->selectRaw('ROUND(SUM(earned_revenue)) as earned_revenue')
            ->selectRaw('ROUND(SUM(claims_total_net)) as claims_total_net')
            ->selectRaw('ROUND(SUM(claims_total_net) / SUM(earned_revenue) * 100) as burn_rate')
            ->groupBy('account_id')
            ->with('account:id,name')
            ->orderByDesc('burn_rate')
            ->limit(20)
            ->where(fn ($query) => $this->getWheres($query))
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Earned Revenue (£)',
                    'data' => $revenue->pluck('earned_revenue')->toArray(),
                    'backgroundColor' => '#36A2EB',
                    'borderColor' => '#9BD0F5',
                    'yAxisID' => 'Y',
                ],
                [
                    'label' => 'Total Claims (£)',
                    'data' => $revenue->pluck('claims_total_net')->toArray(),
                    'backgroundColor' => '#FFCE56',
                    'borderColor' => '#FFCE56',
                    'yAxisID' => 'Y',
                ],
                [
                    'label' => 'Burn Rate',
                    'data' => $revenue->pluck('burn_rate')->toArray(),
                    'backgroundColor' => '#FF6384',
                    'borderColor' => '#FF6384',
                    'yAxisID' => 'Y2',
                ],
            ],
            'labels' => $revenue->pluck('account.name')->map(fn (string $name) => Str::limit($name, 20))->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getWheres(Builder $query): Builder
    {
        return $query;
    }
}
