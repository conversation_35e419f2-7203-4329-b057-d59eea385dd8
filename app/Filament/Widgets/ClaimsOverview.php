<?php

namespace App\Filament\Widgets;

use App\Models\Claim;
use App\Models\ClaimAuthorisation;
use App\Models\ClaimRejection;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Flowframe\Trend\Trend;

class ClaimsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            $this->unitsSoldStat('New Claims', Claim::has('warranty'))
                ->url('/claims'),
            $this->unitsSoldStat('Claims Approved', ClaimAuthorisation::has('estimate.claim.warranty'))
                ->url('/claims?activeTab=approved'),
            $this->unitsSoldStat('Claims Rejected', ClaimRejection::has('claim.warranty'))
                ->url('/claims?activeTab=rejected'),
            Stat::make('New Claims', Claim::has('warranty')->whereBetween('created_at', [now()->startOfWeek(), now()])->count())
                ->description('Claims since start of week'),
            Stat::make('Pending Claims', Claim::has('warranty')->whereDoesntHave('authorisations')->whereDoesntHave('rejection')->count())
                ->url('/claims?activeTab=pending')
                ->description('Claims to be processed'),
        ];
    }

    protected function unitsSoldStat(string $title, $query)
    {
        $trend = Trend::query($query)
            ->between(
                start: now()->subYear(),
                end: now()->subDay(),
            )
            ->perWeek()
            ->count()
            ->pluck('aggregate');

        return Stat::make($title, $trend->sum())
            ->description('Total sales in the last 12 months')
            ->chart($trend->all());
    }
}
