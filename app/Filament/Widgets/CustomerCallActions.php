<?php

namespace App\Filament\Widgets;

use App\Actions\MakeVoipCall;
use App\Actions\RecheckVehicleData;
use App\Enums\CallOutcome;
use App\Jobs\AssociateCallOutcomeWithPhoneCall;
use App\Models\AccountWarrantyProduct;
use App\Models\Offer;
use App\Models\SalesLead;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Support\Enums\IconPosition;
use Filament\Widgets\Widget;
use Illuminate\Support\Carbon;

class CustomerCallActions extends Widget implements HasActions, HasForms
{
    use InteractsWithActions, InteractsWithForms;

    public SalesLead $salesLead;

    protected static string $view = 'filament.widgets.customer-call-actions';

    private static function callbackTimes()
    {
        return collect(range(7, 19))
            ->flatMap(fn ($hour) => collect(range(0, 55, 5))->map(fn ($minute) => sprintf('%02d:%02d', $hour, $minute)))->mapWithKeys(fn ($time) => [$time => $time])->toArray();
    }

    public function callCustomer()
    {
        return Action::make('callCustomer')
            ->color('primary')
            ->icon('heroicon-s-phone')
            ->action(function (MakeVoipCall $makeVoipCall) {
                $makeVoipCall->execute(auth()->user(), $this->salesLead->sale->customer);
            });
    }

    public function recordOutcome(): Action
    {
        return Action::make('recordOutcome')
            ->color('gray')
            ->icon('heroicon-s-calendar')
            ->slideOver()
            ->form(fn (Form $form) => $form
                ->columns(2)
                ->schema([
                    Radio::make('outcome')
                        ->options(CallOutcome::class)
                        ->columnSpanFull()
                        ->required()
                        ->live(),
                    Forms\Components\Fieldset::make('Schedule a Callback')->columns(2)
                        ->visible(fn (Get $get) => $get('outcome') !== CallOutcome::NOT_INTERESTED->value)
                        ->schema([
                            Forms\Components\Actions::make([
                                Forms\Components\Actions\Action::make('in_1_hour')
                                    ->action(function (Set $set) {
                                        $time = now()->timezone('Europe/London')->addHour();
                                        $set('date', $time);
                                        $set('time', $time->format('H:i'));
                                    }),
                                Forms\Components\Actions\Action::make('in_1_day')
                                    ->action(function (Set $set) {
                                        $time = now()->timezone('Europe/London')->addDay();
                                        $set('date', $time);
                                        $set('time', $time->format('H:i'));
                                    }),
                                Forms\Components\Actions\Action::make('in_1_week')
                                    ->action(function (Set $set) {
                                        $time = now()->timezone('Europe/London')->addWeek();
                                        $set('date', $time);
                                        $set('time', $time->format('H:i'));
                                    }),
                            ])->columnSpanFull(),
                            DatePicker::make('date')
                                ->minDate(today())
                                ->maxDate(today()->addMonth())
                                ->default(today())
                                ->native(false)
                                ->format('d/m/Y')
                                ->required(fn (Get $get) => $get('outcome') === CallOutcome::CALLBACK->value),
                            Forms\Components\TextInput::make('time')
                                ->datalist(static::callbackTimes())
                                ->mask('99:99')
                                ->placeholder('09:00')
                                ->required(fn (Get $get) => $get('outcome') === CallOutcome::CALLBACK->value),
                        ]),
                    Textarea::make('notes')->columnSpanFull()->rows(5),
                ]))
            ->action(function (array $data) {
                $callOutcome = $this->salesLead->callOutcomes()->create([
                    'outcome' => $data['outcome'],
                    'callback_date' => ($data['date'] ?? null) ? Carbon::createFromFormat('d/m/Y H:i', $data['date'].' '.$data['time']) : null,
                    'notes' => $data['notes'],
                ]);

                // If the call has already ended we will be able to associate the call outcome with the phone call
                AssociateCallOutcomeWithPhoneCall::dispatchSync($callOutcome);

                if (! $callOutcome->refresh()->phone_call_id) {
                    // If the outcome was recorded before the call was terminated, we will need to wait for the call to
                    // end before we can associate the call outcome with the phone call. Should be finished within 60 seconds
                    AssociateCallOutcomeWithPhoneCall::dispatch($callOutcome)->delay(60);
                }

                $this->dispatch('call-outcome-created');
            });
    }

    public function scheduleCallback(): Action
    {
        return $this->recordOutcome()
            ->name('scheduleCallback')
            ->icon('heroicon-s-document-text')
            ->fillForm([
                'outcome' => CallOutcome::CALLBACK->value,
            ]);
    }

    public function sendOffer(): Action
    {
        return Action::make('sendOffer')
            ->color('primary')
            ->icon('heroicon-s-chevron-right')
            ->iconPosition(IconPosition::After)
            ->form(function (Form $form, RecheckVehicleData $recheckVehicleData) {
                if (is_null($this->salesLead->sale->vehicle->body_type)) {
                    $recheckVehicleData->execute($this->salesLead->sale);
                }

                return $form
                    ->columns(2)
                    ->schema([
                        Forms\Components\Select::make('offer_id')
                            ->label('Select offer')
                            ->options(Offer::pluck('name', 'id')),
                    ]);
            })
            ->action(function (array $data) {
                $offer = $this->salesLead->offers()->create([
                    'offer_id' => $data['offer_id'],
                ]);

                $offer->send();

                $this->dispatch('sales-offer-created');
            });
    }
}
