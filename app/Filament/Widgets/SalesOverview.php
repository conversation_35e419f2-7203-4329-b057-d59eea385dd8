<?php

namespace App\Filament\Widgets;

use App\Models\BreakdownPlan;
use App\Models\ServicePlan;
use App\Models\Warranty;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Flowframe\Trend\Trend;

class SalesOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return array_filter([
            $this->unitsSoldStat('Warranties Sold', Warranty::class),
            $this->unitsSoldStat('Breakdown Plans Sold', BreakdownPlan::class),
            $this->unitsSoldStat('Service Plans Sold', ServicePlan::class),
        ]);
    }

    protected function unitsSoldStat(string $title, string $modelClass)
    {
        $trend = Trend::model($modelClass)
            ->between(
                start: now()->subYear(),
                end: now()->subDay(),
            )
            ->perWeek()
            ->count()
            ->pluck('aggregate');

        return Stat::make($title, $trend->sum())
            ->description('Total sales in the last 12 months')
            ->chart($trend->all());
    }
}
