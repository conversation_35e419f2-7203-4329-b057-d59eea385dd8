<?php

namespace App\Filament\Widgets;

use App\Actions\GenerateQrCode;
use App\Actions\SetupBillingRequest;
use App\Models\Concerns\BillableContract;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Filament\Widgets\Widget;
use Livewire\Attributes\Computed;

class MissingPaymentMethodAlert extends Widget implements HasForms, HasInfolists
{
    use InteractsWithForms;
    use InteractsWithInfolists;

    protected static string $view = 'filament.widgets.missing-payment-method-alert';

    protected int|string|array $columnSpan = 'full';

    public BillableContract $record;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                ImageEntry::make('qr_code')
                    ->label('Scan QR Code')
                    ->hint('Scan this QR code from a phone.')
                    ->getStateUsing(function (GenerateQrCode $generateQrCode) {
                        if ($this->record->getCompletePaymentUrl()) {
                            return $generateQrCode->execute($this->record->getCompletePaymentUrl());
                        }

                        return null;
                    })
                    ->width('200')
                    ->height('200'),
            ]);
    }

    #[Computed]
    public function requiresPaymentSetup(): bool
    {
        return $this->record->requiresPaymentSetup();
    }

    #[Computed]
    public function paymentMethodName(): string
    {
        return $this->record->getPaymentMethod()->getLabel();
    }

    #[Computed]
    public function paymentUrl(): ?string
    {
        return $this->record->getCompletePaymentUrl();
    }

    public function openModal(SetupBillingRequest $setupBillingRequest): void
    {
        $setupBillingRequest->execute($this->record, sendNotification: false);

        $this->dispatch('open-modal', id: 'missing-payment-method-modal');
    }

    public function sendEmail(): void
    {
        $this->record->sendPaymentSetupNotification();

        $this->dispatch('close-modal', id: 'missing-payment-method-modal');
    }
}
