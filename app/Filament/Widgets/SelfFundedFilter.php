<?php

namespace App\Filament\Widgets;

use Filament\Forms\Contracts\HasForms;
use Filament\Widgets\Widget;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Url;

class SelfFundedFilter extends Widget // implements HasForms
{
    #[Url]
    public $filters = [];

    #[Computed]
    public function isSelfFunded(): bool
    {
        return $this->filters['selfFunded'] ?? true;
    }

    protected static string $view = 'filament.widgets.filters';

    protected array|string|int $columnSpan = 'full';
}
