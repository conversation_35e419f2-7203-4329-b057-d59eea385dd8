<?php

namespace App\Filament\Widgets;

use App\Actions\ValidateClaim;
use App\DataTransferObjects\ClaimValidationResult;
use App\Models\Claim;
use Filament\Widgets\Widget;
use Livewire\Attributes\Computed;

class ClaimValidation extends Widget
{
    public Claim $claim;

    protected static string $view = 'filament.widgets.claim-validation';

    #[Computed]
    public function validationResult(): ?ClaimValidationResult
    {
        return app(ValidateClaim::class)->execute($this->claim);
    }
}
