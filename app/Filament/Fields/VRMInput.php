<?php

namespace App\Filament\Fields;

use Filament\Forms\Components\TextInput;

class VRMInput extends TextInput
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->placeholder('e.g. AB12 CDE')
            ->alphaNum()
            ->minLength(2)
            ->maxLength(7)
            ->mutateStateForValidationUsing(fn (?string $state) => strtoupper(str_replace(' ', '', $state)))
            ->dehydrateStateUsing(fn (?string $state) => strtoupper(str_replace(' ', '', $state)))
            ->autocapitalize('characters')
            ->extraInputAttributes([
                'class' => 'uppercase placeholder:normal-case',
            ]);
    }
}
