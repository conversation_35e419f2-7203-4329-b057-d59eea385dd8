<?php

namespace App\Filament\Fields;

use Filament\Forms\Components\TextInput;

class AddressLookup extends TextInput
{
    protected string $view = 'forms.components.address-lookup';

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->placeholder('Start typing address or postcode')
            ->label('Line 1')
            ->id('line_1')
            ->extraAttributes(['style' => 'overflow: visible;'])
            ->required()
            ->maxLength(255);
    }
}
