<?php

namespace App\Filament\Fields;

use App\Filament\Resources\CustomerResource;
use App\Models\Customer;
use Filament\Forms\Components\Select;
use Filament\Support\Enums\MaxWidth;

class CustomerLookup extends Select
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Customer')
            ->required()
            ->maxWidth(MaxWidth::ExtraLarge)
            ->placeholder('Search customer email or name')
            ->searchable()
            ->getSearchResultsUsing(fn (string $search) => Customer::query()
                ->where('account_id', auth()->user()->account_id)
                ->where(fn ($q) => $q
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                )
                ->limit(20)
                ->get(['id', 'first_name', 'last_name', 'email'])
                ->mapWithKeys(fn (Customer $customer) => [$customer->getKey() => $customer->getSearchResultLabel()])
            )
            ->getOptionLabelUsing(fn ($value) => Customer::find($value)?->getSearchResultLabel())
            ->live()
            ->createOptionForm(fn ($form) => CustomerResource::form($form))
            ->createOptionAction(fn (\Filament\Forms\Components\Actions\Action $action) => $action
                ->slideOver()
                ->color('primary')
            )
            ->createOptionUsing(fn (array $data) => Customer::create($data)->getKey());
    }
}
