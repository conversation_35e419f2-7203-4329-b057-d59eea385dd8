<?php

namespace App\Filament\Filters;

use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class DateRangeFilter
{
    public static function make(string $columnName, ?string $label = null): Filter
    {
        $label = $label ?: Str::ucfirst(Str::replace('_', ' ', $columnName));

        return Filter::make($columnName)
            ->label($label)
            ->form([
                Grid::make(2)
                    ->columnSpanFull()
                    ->schema([
                        DatePicker::make('date_from')->label($label.' from'),
                        DatePicker::make('date_to')->label($label.' to'),
                    ]),
            ])
            ->query(fn (Builder $query, array $data): Builder => $query
                ->when(
                    $data['date_from'],
                    fn (Builder $query, $date): Builder => $query->whereDate($columnName, '>=', $date),
                )
                ->when(
                    $data['date_to'],
                    fn (Builder $query, $date): Builder => $query->whereDate($columnName, '<=', $date),
                ))
            ->indicateUsing(function (array $data) use ($label): ?string {
                if (! $data['date_from'] && ! $data['date_to']) {
                    return null;
                }
                if (! $data['date_from']) {
                    return sprintf('%s to %s', $label, $data['date_to']);
                }
                if (! $data['date_to']) {
                    return sprintf('%s from %s', $label, $data['date_from']);
                }

                return sprintf(
                    '%s between %s and %s', $label,
                    Carbon::parse($data['date_from'])->format('d/m/Y'),
                    Carbon::parse($data['date_to'])->format('d/m/Y'),
                );
            });
    }
}
