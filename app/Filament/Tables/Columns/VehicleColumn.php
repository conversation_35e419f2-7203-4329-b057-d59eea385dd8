<?php

namespace App\Filament\Tables\Columns;

use App\Models\BreakdownPlan;
use App\Models\Claim;
use App\Models\Sale;
use App\Models\SalesLead;
use App\Models\ServicePlan;
use App\Models\Vehicle;
use App\Models\Warranty;
use Filament\Tables\Columns\Column;

class VehicleColumn extends Column
{
    protected string $view = 'filament.tables.columns.vehicle-column';

    public function getVehicle(): ?Vehicle
    {
        $record = $this->getRecord();

        return match (get_class($record)) {
            Claim::class => $record->warranty->sale->vehicle,
            Warranty::class, BreakdownPlan::class, ServicePlan::class, SalesLead::class => $record->sale->vehicle,
            Sale::class => $record->vehicle,
            Vehicle::class => $record,
            default => throw new \RuntimeException('Invalid record type'),
        };
    }

    public function configure(): static
    {
        $whereHas = fn ($q, $search) => $q->whereHas('vehicle', fn ($q) => $q->where(fn ($q) => $q
            ->orWhere('vrm', 'like', '%'.$search.'%')
            ->orWhere('private_plate', 'like', '%'.$search.'%')
            ->orWhere('make', 'like', '%'.$search.'%')
            ->orWhere('model', 'like', '%'.$search.'%')
        ));

        return $this
            ->searchable(
                query: fn ($query, $search) => match (get_class($query->getModel())) {
                    Sale::class => $whereHas($query, $search),
                    Claim::class => $query->whereHas('warranty.sale', fn ($q) => $whereHas($q, $search)),
                    default => $query->whereHas('sale', fn ($q) => $whereHas($q, $search)),
                });
    }
}
