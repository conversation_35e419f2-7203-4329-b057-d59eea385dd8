<?php

namespace App\Filament\Tables\Columns;

use App\Enums\ProductStatus;
use Closure;
use Filament\Tables\Columns\TextColumn;

class ProductStatusColumn extends TextColumn
{
    protected bool|Closure $recurringBadge = false;

    protected string $view = 'filament.tables.columns.product-status-column';

    public function configure(): static
    {
        return parent::configure()
            ->alignCenter()
            ->badge(true)
            ->color(fn (ProductStatus $state): string => $state->color())
            ->formatStateUsing(fn (ProductStatus $state): string => $state->label());
    }

    public function recurringBadge(bool|Closure $condition = false): static
    {
        $this->recurringBadge = $condition;

        return $this;
    }

    public function getRecurringBadge(): bool
    {
        return (bool) $this->evaluate($this->recurringBadge);
    }
}
