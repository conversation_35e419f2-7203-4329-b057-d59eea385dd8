<?php

namespace App\Filament\Tables\Columns;

use App\Models\BreakdownPlan;
use App\Models\Customer;
use App\Models\Sale;
use App\Models\ServicePlan;
use App\Models\Warranty;
use Carbon\Carbon;
use Filament\Tables\Columns\Column;

class DateRangeColumn extends Column
{
    protected string $view = 'filament.tables.columns.date-range-column';

    public function endDate(): Carbon
    {
        return $this->getRecord()->end_date;
    }

    public function getSale(): Sale
    {
        $record = $this->getRecord();

        return match (get_class($record)) {
            Sale::class => $record,
            Warranty::class, BreakdownPlan::class, ServicePlan::class => $record->sale,
            default => throw new \RuntimeException('Invalid record type'),
        };
    }

    public function configure(): static
    {
        $wheres = fn ($q, $search) => $q->where(fn ($q) => $q
            ->orWhere('email', 'like', '%'.$search.'%')
            ->orWhere('first_name', 'like', '%'.$search.'%')
            ->orWhere('last_name', 'like', '%'.$search.'%')
        );

        return $this
            ->label('Effective Date')
            ->searchable(
                true,
                function ($query, $search) use ($wheres) {
                    if ($this->getRecord() instanceof Customer) {
                        return $wheres($query, $search);
                    }

                    return $query->whereHas('customer', fn ($q) => $wheres($q, $search));
                });
    }
}
