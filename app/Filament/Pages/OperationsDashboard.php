<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\OperationsOverview;
use Filament\Pages\Dashboard;

class OperationsDashboard extends Dashboard
{
    protected static ?string $navigationLabel = 'Operations Dashboard';

    protected static ?string $title = 'Operations Dashboard';

    public function getWidgets(): array
    {
        return [
            OperationsOverview::class,
        ];
    }

    public function getColumns(): int|string|array
    {
        return 2;
    }
}
