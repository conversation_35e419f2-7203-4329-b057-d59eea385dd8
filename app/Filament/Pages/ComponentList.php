<?php

namespace App\Filament\Pages;

use App\Models\CoverLevel;
use App\Models\VehicleComponent;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Section;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\CheckboxColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Support\Facades\Auth;

class ComponentList extends Page implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pages.manage-component-list';

    public static function canAccess(): bool
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        return $user->hasPermissionTo('component-list.view');
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(VehicleComponent::query()->with('coverLevels'))
            ->paginated(false)
            ->defaultGroup('category.name')
            ->columns([
                TextColumn::make('name'),
                //                IconColumn::make('applicable_to_ice')->boolean(),
                //                IconColumn::make('applicable_to_ev')->boolean(),
                ...$this->getIconColumns(),
                //                ...$this->getCheckboxColumns(),
            ])
            ->filters([
                // ...
            ])
            ->actions([
                EditAction::make()
                    ->modalHeading(fn (VehicleComponent $record) => $record->name)
                    ->form(fn (Form $form) => $this->form($form))
                    ->slideOver(),
            ])
            ->bulkActions([
                //                BulkAction::make('update')
                //                    ->form(fn(Form $form) => $this->form($form))
                //                    ->slideOver(),
            ]);
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Section::make()
                ->schema([
                    CheckboxList::make('cover_levels')
                        ->relationship('coverLevels', 'name', fn ($query) => $query->orderBy('id')),
                ]),
        ]);
    }

    private function getIconColumns()
    {
        return CoverLevel::query()
            ->orderBy('id')
            ->get()
            ->map(function (CoverLevel $coverLevel) {
                return IconColumn::make(implode('-', [$coverLevel->name, 'platinum']))
                    ->label($coverLevel->name)
                    ->wrapHeader()
                    ->boolean()
                    ->alignCenter()
                    ->getStateUsing(function (VehicleComponent $record) use ($coverLevel) {
                        return $record->coverLevels->contains($coverLevel);
                    });
            });
    }

    private function getCheckboxColumns()
    {
        return CoverLevel::query()
            ->orderBy('id')
            ->get()
            ->map(function (CoverLevel $coverLevel) {
                return CheckboxColumn::make(implode('-', [$coverLevel->name, 'platinum']))
                    ->label($coverLevel->name)
                    ->getStateUsing(function (VehicleComponent $record) use ($coverLevel) {
                        return $record->coverLevels->contains($coverLevel);
                    })
                    ->updateStateUsing(function (CheckboxColumn $column, VehicleComponent $record, bool $state) use ($coverLevel) {
                        try {
                            if ($state) {
                                $coverLevel->vehicleComponent()->attach($record);
                            } else {
                                $coverLevel->vehicleComponent()->detach($record);
                            }
                        } catch (UniqueConstraintViolationException $e) {
                            // ignore
                        }
                    });
            });
    }
}
