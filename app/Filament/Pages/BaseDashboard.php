<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Str;

abstract class BaseDashboard extends Page
{
    protected static ?string $prefix = '/dashboards';

    protected static string $view = 'filament-panels::pages.dashboard';

    public static function canAccess(): bool
    {
        return auth()->user()->can('dashboard.view');
    }

    public static function getRoutePath(): string
    {
        return static::$prefix.parent::getRoutePath();
    }

    public static function getSlug(): string
    {
        return Str::before(parent::getSlug(), '-dashboard');
    }

    abstract public function getVisibleWidgets(): array;

    public function getColumns(): int|string|array
    {
        return 2;
    }
}
