<?php

namespace App\Filament\Exports;

use App\Enums\PayLaterAgreementStatus;
use App\Models\PayLaterAgreement;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class PayLaterAgreementExporter extends Exporter
{
    protected static ?string $model = PayLaterAgreement::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('ID'),
            ExportColumn::make('created_at')
                ->label('Date Created'),
            ExportColumn::make('account.short_name')
                ->label('Account'),
            ExportColumn::make('payable.customer.full_name')
                ->label('Customer Name'),
            ExportColumn::make('payable.customer.email')
                ->label('Customer Email'),
            ExportColumn::make('payable.customer.phone')
                ->label('Customer Phone'),
            ExportColumn::make('payable_type')
                ->label('Type')
                ->formatStateUsing(fn (string $state) => strtoupper(str_replace('-', ' ', $state))),
            ExportColumn::make('is_approved')
                ->label('Pre-approved')
                ->formatStateUsing(fn (bool $state) => $state ? 'Yes' : 'No'),
            ExportColumn::make('status')
                ->label('Status')
                ->formatStateUsing(fn (PayLaterAgreementStatus $state) => $state->label()),
            ExportColumn::make('payLaterPlan.name')
                ->label('Plan'),
            ExportColumn::make('dealer_payment')
                ->prefix('£')
                ->label('Dealer Payment'),
            ExportColumn::make('loan_amount')
                ->prefix('£')
                ->label('Loan Amount'),
            ExportColumn::make('commission_rate')
                ->suffix('%')
                ->label('Dealer Percentage'),
            ExportColumn::make('description')
                ->label('Description'),
            ExportColumn::make('provider_reference')
                ->label('Provider Reference'),
            ExportColumn::make('token')
                ->label('Token'),
            ExportColumn::make('url')
                ->label('URL'),
            ExportColumn::make('payable.vehicle.vrm')
                ->label('VRM'),
            ExportColumn::make('payable.vehicle.vin')
                ->label('VIN'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your pay later agreement export has completed and '.number_format($export->successful_rows).' '.str('row')->plural($export->successful_rows).' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' '.number_format($failedRowsCount).' '.str('row')->plural($failedRowsCount).' failed to export.';
        }

        return $body;
    }
}
