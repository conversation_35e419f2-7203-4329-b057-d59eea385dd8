<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BreakdownPlanSalesReport extends Mailable
{
    use Queueable, SerializesModels;

    protected string $rawAttachmentData;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(string $rawAttachmentData)
    {
        $this->rawAttachmentData = $rawAttachmentData;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this
            ->subject('Breakdown Plan - Current Live Policies')
            ->markdown('email.breakdown_plan_sales_report')
            ->attachData($this->rawAttachmentData, 'breakdown-plans.csv');
    }
}
