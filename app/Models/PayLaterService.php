<?php

namespace App\Models;

use App\Enums\PaymentMethod;
use App\Filament\Resources\PayLaterServiceResource;
use App\Models\Concerns\BillableContract;
use App\Models\Concerns\LinkableContract;
use App\Models\Concerns\PayLaterContract;
use App\Models\Traits\HasTenant;
use App\Notifications\CustomerRequiresPaymentMethodNotification;
use App\Services\Payments\DirectDebit\CustomerData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class PayLaterService extends Model implements BillableContract, LinkableContract, PayLaterContract
{
    /** @use HasFactory<\Database\Factories\PayLaterServiceFactory> */
    use HasFactory;

    use HasTenant;

    protected $casts = [
        'registration_date' => 'date',
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function payLaterAgreement(): MorphOne
    {
        return $this->morphOne(PayLaterAgreement::class, 'payable');
    }

    //    public function dealership()
    //    {
    //        return $this->belongsTo(Dealership::class);
    //    }

    public function vrm(): string
    {
        return $this->vehicle->vrm;
    }

    public function description(): string
    {
        return 'Some description';
    }

    public function toCustomerData(): CustomerData
    {
        return $this->customer->toCustomerData();
    }

    public function sendPaymentSetupNotification(): void
    {
        $this->notify(new CustomerRequiresPaymentMethodNotification($this));
    }

    public function requiresPaymentSetup(): bool
    {
        return $this->payLaterAgreement?->isActive() === false;
    }

    public function getCompletePaymentUrl(): ?string
    {
        return $this->payLaterAgreement->url;
    }

    public function getBillableEntityLabel(): string
    {
        return 'Pay Later Service';
    }

    public function getAccountHolderNameDefault(): string
    {
        return '';
    }

    public function metaDataForPaymentProvider(): array
    {
        return [];
    }

    public function notify($instance): void
    {
        $this->customer->notify($instance);
    }

    public function getPaymentMethod(): ?PaymentMethod
    {
        return PaymentMethod::PAY_LATER;
    }

    public function getPayLaterAgreement(): ?PayLaterAgreement
    {
        return $this->payLaterAgreement;
    }

    public function getAccount(): Account
    {
        return $this->account;
    }

    public function getCustomer(): Customer
    {
        return $this->customer;
    }

    public function getLabel(): string
    {
        return 'Pay Later Service #'.$this->id;
    }

    public function getAdminUrl(): string
    {
        return PayLaterServiceResource::getUrl('view', ['record' => $this]);
    }
}
