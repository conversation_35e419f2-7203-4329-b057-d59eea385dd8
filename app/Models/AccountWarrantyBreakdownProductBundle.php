<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AccountWarrantyBreakdownProductBundle extends Model
{
    /** @use HasFactory<\Database\Factories\AccountWarrantyBreakdownProductBundleFactory> */
    use HasFactory;

    public function breakdownProduct(): BelongsTo
    {
        return $this->belongsTo(BreakdownProduct::class);
    }
}
