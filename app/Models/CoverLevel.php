<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CoverLevel extends Model
{
    use HasFactory;

    protected $casts = [
        'is_ice' => 'boolean',
        'is_ev' => 'boolean',
    ];

    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    public function vehicleComponent()
    {
        return $this->belongsToMany(VehicleComponent::class)->withTimestamps();
    }

    public function scopeIsIce($query)
    {
        return $query->where('is_ice', true)->where('is_ev', false);
    }

    public function scopeIsEv($query)
    {
        return $query->where('is_ice', false)->where('is_ev', true);
    }

    public function scopeIsHybrid($query)
    {
        return $query->where('is_ev', true)->where('is_ice', true);
    }
}
