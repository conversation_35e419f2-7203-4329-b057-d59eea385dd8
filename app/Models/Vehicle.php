<?php

namespace App\Models;

use App\Models\Traits\HasTenant;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;
use OvonGroup\ManufacturerLogos\Models\Manufacturer;

class Vehicle extends Model
{
    /** @use HasFactory<\Database\Factories\VehicleFactory> */
    use HasFactory;

    use HasTenant;
    use Prunable;

    protected $casts = [
        'registration_date' => 'date',
        'mot_last_checked_at' => 'datetime',
        'vehicle_lookup_last_checked_at' => 'datetime',
    ];

    public function manufacturer(): BelongsTo
    {
        return $this->belongsTo(Manufacturer::class);
    }

    public function motTests(): HasMany
    {
        return $this->hasMany(MotTest::class)->orderByDesc('completed_at');
    }

    public function ownerChanges(): HasMany
    {
        return $this->hasMany(OwnerChange::class);
    }

    public function sale(): HasOne
    {
        return $this->hasOne(Sale::class);
    }

    public function payLaterService(): HasOne
    {
        return $this->hasOne(PayLaterService::class);
    }

    public function getDetailsAttribute()
    {
        return sprintf('%s %s', $this->make, $this->model);
    }

    public function formattedVrm($privatePlate = false): string
    {
        $vrm = $privatePlate && $this->private_plate ? $this->private_plate : $this->vrm;

        // https://gist.github.com/danielrbradley/7567269
        // Determine the format of the VRM
        if (! preg_match('/(?<Current>^([A-Z]{2}[0-9]{2})\s?([A-Z]{3})$)|(?<Prefix>^([A-Z][0-9]{1,3})\s?([A-Z]{3})$)|(?<Suffix>^([A-Z]{3})\s?([0-9]{1,3}[A-Z])$)/', str_replace(' ', '', $vrm), $matches)) {
            // no match on these formats, return the VRM as is
            return $vrm;
        }

        // Return the last two array elements concatenated with a space
        return implode(' ', array_slice(array_filter($matches), -2));
    }

    public function isHybrid(): bool
    {
        return Str::contains(strtolower($this->fuel_type), 'hybrid');
    }

    public function isPureElectric(): bool
    {
        return ! $this->isHybrid() && Str::contains(strtolower($this->fuel_type), 'electric');
    }

    public function isIce(): bool
    {
        return ! $this->isHybrid() && ! $this->isPureElectric();
    }

    /**
     * Get the prunable model query.
     */
    public function prunable(): Builder
    {
        return static::query()
            ->doesntHave('sale')
            ->doesntHave('payLaterService')
            ->where('created_at', '<=', now()->subMonth());
    }
}
