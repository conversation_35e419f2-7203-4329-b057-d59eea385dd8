<?php

namespace App\Models;

use App\Services\Accounting\ContactData;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait InvoiceableTrait
{
    public function invoices(): MorphMany
    {
        return $this->morphMany(Invoice::class, 'invoiceable');
    }

    public function accountingContact(): BelongsTo
    {
        return $this->belongsTo(AccountingContact::class);
    }

    abstract public function getContactData(): ContactData;

    abstract public function getBillingRequest(): ?BillingRequest;

    abstract public function getUrl(): string;
}
