<?php

namespace App\Models;

use App\Filament\Resources\CustomerResource;
use App\Models\Concerns\OTPAuthenticatable;
use App\Models\Concerns\VoipCallable;
use App\Models\Traits\HasTenant;
use App\Services\Accounting\ContactData;
use App\Services\Payments\DirectDebit\CustomerData;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\HtmlString;

class Customer extends Authenticatable implements OTPAuthenticatable, VoipCallable
{
    use HasFactory;
    use HasTenant;
    use InvoiceableTrait;
    use Notifiable;
    use Prunable;

    protected $casts = [
        'dob' => 'date',
    ];

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    public function oneTimePasscodes()
    {
        return $this->morphMany(OneTimePasscode::class, 'otp_authenticatable');
    }

    public function phoneCalls()
    {
        return $this->hasMany(PhoneCall::class);
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['search'] ?? null, fn ($query, $search) => $query->search($search))
            ->when($filters['trashed'] ?? null, fn ($query, $trashed) => match ($trashed) {
                'with' => $query->withTrashed(),
                'only' => $query->onlyTrashed(),
                default => $query,
            });
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(fn ($q) => $q
            ->where('email', 'like', $search.'%')
            ->orWhere('last_name', 'like', $search.'%')
            ->orWhere('postcode', 'like', $search.'%')
            ->orWhereHas('sales', fn ($q) => $q->search($search))
        );
    }

    public function scopeWithTrashed()
    {
        // This is so that the polymorphic "invoiceable" relationship works
        // as this model is not a soft-deletable model.
    }

    public function getName(): string
    {
        return sprintf('%s %s', $this->first_name, $this->last_name);
    }

    public function getLink()
    {
        return route('customers.show', $this);
    }

    public function getFullNameAttribute()
    {
        return $this->getName();
    }

    public function fullAddress($delimiter = '<br>')
    {
        return new HtmlString(
            collect([
                'address_1',
                'address_2',
                'city',
                'county',
                'country',
                'postcode',
            ])->map(fn ($k) => $this->$k)->filter()->join($delimiter)
        );
    }

    public function getContactData(): ContactData
    {
        return new ContactData(
            id: $this->accounting_software_id,
            name: "{$this->getName()} (#{$this->getKey()})",
            email: $this->email,
            addressLine1: $this->address_1,
            addressLine2: $this->address_2,
            addressCity: $this->city,
            addressRegion: $this->county,
            addressPostcode: $this->postcode,
        );
    }

    public function getBillingRequest(): ?BillingRequest
    {
        return $this->sales->first()?->billingRequest;
    }

    public function getUrl(): string
    {
        return CustomerResource::getUrl('view', [$this]);
    }

    public function getPhoneNumberForVoipCall(): string
    {
        return $this->phone;
    }

    /**
     * Get the prunable model query.
     */
    public function prunable(): Builder
    {
        return static::query()
            ->doesntHave('sales')
            ->where('created_at', '<=', now()->subDays(7));
    }

    public function getSearchResultLabel()
    {
        return $this->full_name.' - '.$this->email;
    }

    public function toCustomerData(): CustomerData
    {
        return new CustomerData(
            reference: $this->id,
            paymentProcessorId: null,
            firstName: $this->first_name,
            lastName: $this->last_name,
            email: $this->email,
            phone: $this->phone,
            line1: $this->address_1,
            line2: $this->address_2,
            city: $this->city,
            county: $this->county,
            postcode: $this->postcode,
            accountHolderName: $this->getName(),
        );
    }
}
