<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesLead extends Model
{
    /** @use HasFactory<\Database\Factories\SalesLeadFactory> */
    use HasFactory;

    public function sale()
    {
        return $this->belongsTo(Sale::class);
    }

    public function workflows()
    {
        return $this->belongsToMany(Workflow::class)->withTimestamps();
    }

    public function workflowActions()
    {
        return $this->belongsToMany(WorkflowAction::class, 'sales_lead_workflow_action')
            ->using(SalesLeadWorkflowAction::class)
            ->withPivot('executed_at')
            ->withTimestamps();
    }

    public function callOutcomes()
    {
        return $this->hasMany(SalesLeadCallOutcome::class);
    }

    public function offers()
    {
        return $this->hasMany(SalesOffer::class);
    }
}
