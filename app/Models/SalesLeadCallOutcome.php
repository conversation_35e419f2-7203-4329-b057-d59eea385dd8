<?php

namespace App\Models;

use App\Enums\CallOutcome;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesLeadCallOutcome extends Model
{
    /** @use HasFactory<\Database\Factories\SalesLeadCallOutcomeFactory> */
    use HasFactory;

    protected $casts = [
        'outcome' => CallOutcome::class,
        'callback_date' => 'datetime',
    ];

    public function salesLead()
    {
        return $this->belongsTo(SalesLead::class);
    }
}
