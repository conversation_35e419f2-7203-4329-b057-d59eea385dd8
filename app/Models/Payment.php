<?php

namespace App\Models;

use App\Enums\PaymentProvider;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Payment extends Model
{
    use HasFactory;

    const STATUS_DRAFT = 'draft';

    const STATUS_PENDING_SUBMISSION = 'pending_submission';

    const STATUS_PENDING = 'pending';

    const STATUS_SUBMITTED = 'submitted';

    const STATUS_FAILED = 'failed';

    const STATUS_CANCELLED = 'cancelled';

    const STATUS_PAID = 'paid';

    const STATUS_PAID_OUT = 'paid_out';

    protected $casts = [
        'period_start' => 'date',
        'charge_date' => 'date',
        'upcoming_payment_notification_sent_at' => 'datetime',
        'provider' => PaymentProvider::class,
    ];

    public function billingRequest(): BelongsTo
    {
        return $this->belongsTo(BillingRequest::class);
    }

    public function payout(): BelongsTo
    {
        return $this->belongsTo(Payout::class);
    }

    public function payable(): MorphTo
    {
        return $this->morphTo('payable');
    }

    public function lineItems(): HasMany
    {
        return $this->hasMany(PaymentLineItem::class);
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['start'] ?? null, fn ($query, $start) => $query->whereDate('charge_date', '>=', $start))
            ->when($filters['end'] ?? null, fn ($query, $end) => $query->whereDate('charge_date', '<=', $end));
    }

    public function scopePaid($query)
    {
        return $query->whereIn('status', [Payment::STATUS_PAID, Payment::STATUS_PAID_OUT]);
    }

    public function isDraft()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    public function paymentProcessorUrl(): ?string
    {
        if (! $this->processor_payment_id) {
            return null;
        }

        return match ($this->billingRequest->provider) {
            PaymentProvider::GO_CARDLESS => sprintf(
                '%s/payments/%s',
                config('payments.go_cardless.manage_url'),
                $this->processor_payment_id
            ),
            PaymentProvider::ACCESS_PAYSUITE => sprintf(
                '%s/CAdmin/Contracts/Details/Payments.aspx?.Customer=%s',
                config('payments.access_paysuite.manage_url'),
                $this->billingRequest->provider_customer_id
            ),
            default => null,
        };
    }

    public function statusForCustomer(): string
    {
        return match ($this->status) {
            self::STATUS_DRAFT, self::STATUS_PENDING_SUBMISSION => 'Pending',
            self::STATUS_SUBMITTED => 'Upcoming',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_PAID_OUT => 'Paid',
            default => 'Unknown',
        };
    }

    public function statusColor(): string
    {
        return match ($this->status) {
            self::STATUS_SUBMITTED => 'blue',
            self::STATUS_FAILED, self::STATUS_CANCELLED => 'red',
            self::STATUS_PAID_OUT => 'green',
            default => 'gray',
        };
    }

    public function xeroBankTransactionUrl()
    {
        if (! $this->accounting_software_bank_transfer_id) {
            return null;
        }

        return sprintf(config('services.xero.view_bank_transaction_url'), $this->accounting_software_bank_transfer_id);
    }

    public function isPaid(): bool
    {
        return in_array($this->status, [self::STATUS_PAID, self::STATUS_PAID_OUT]);
    }

    public function isUnpaid(): bool
    {
        return ! $this->isPaid();
    }
}
