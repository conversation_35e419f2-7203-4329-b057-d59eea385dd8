<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VehicleComponent extends Model
{
    /** @use HasFactory<\Database\Factories\VehicleComponentFactory> */
    use HasFactory;

    public function category()
    {
        return $this->belongsTo(VehicleComponentCategory::class, 'vehicle_component_category_id');
    }

    public function coverLevels()
    {
        return $this->belongsToMany(CoverLevel::class);
    }
}
