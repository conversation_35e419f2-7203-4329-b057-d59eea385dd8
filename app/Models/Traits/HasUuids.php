<?php

namespace App\Models\Traits;

trait HasUuids
{
    use \Illuminate\Database\Eloquent\Concerns\HasUuids;

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'uuid';
    }

    /**
     * Get the columns that should receive a unique identifier.
     *
     * @return array
     */
    public function uniqueIds()
    {
        return ['uuid'];
    }
}
