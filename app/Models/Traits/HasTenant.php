<?php

namespace App\Models\Traits;

use App\Models\Account;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

trait HasTenant
{
    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function bootHasTenant()
    {
        static::addGlobalScope('tenant', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->tenant();
        });

        static::creating(function ($model) {
            if (! $model->account_id && Auth::user()?->account_id) {
                $model->account_id = Auth::user()->account_id;
            }
        });
    }

    public function scopeTenant($query)
    {
        if (Auth::user()?->account_id) {
            $query->where('account_id', Auth::user()->account_id);
        }
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class)->withTrashed();
    }
}
