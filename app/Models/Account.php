<?php

namespace App\Models;

use App\Enums\FundType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Account extends Model
{
    use HasFactory, SoftDeletes;

    protected $casts = [
        'warranty_fund_type' => FundType::class,
        'breakdown_fund_type' => FundType::class,
    ];

    public function serviceCredentials()
    {
        return $this->hasMany(AccountServiceCredentials::class, 'account_id');
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function dealerships(): HasMany
    {
        return $this->hasMany(Dealership::class)->orderBy('name');
    }

    public function salesPeople(): HasMany
    {
        return $this->hasMany(SalesPerson::class);
    }

    public function repairers(): HasMany
    {
        return $this->hasMany(Repairer::class);
    }

    public function coverLevels(): HasMany
    {
        return $this->hasMany(CoverLevel::class);
    }

    public function products(): HasMany
    {
        return $this->hasMany(AccountProduct::class);
    }

    public function warrantyProducts(): HasMany
    {
        return $this->hasMany(AccountWarrantyProduct::class);
    }

    public function breakdownProducts(): HasMany
    {
        return $this->hasMany(AccountBreakdownProduct::class);
    }

    public function servicePlanProducts(): HasMany
    {
        return $this->hasMany(AccountServicePlanProduct::class);
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    public function warranties(): HasMany
    {
        return $this->hasMany(Warranty::class);
    }

    public function payLaterPlans(): HasMany
    {
        return $this->hasMany(PayLaterPlan::class);
    }

    public function files(): HasMany
    {
        return $this->hasMany(File::class);
    }

    public function manufacturerSurcharges(): HasMany
    {
        return $this->hasMany(ManufacturerSurcharge::class);
    }

    public function logoUrl()
    {
        if ($this->logo_path) {
            return Storage::url($this->logo_path);
        }
    }

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where('name', 'like', '%'.$search.'%');
        })->when($filters['trashed'] ?? null, function ($query, $trashed) {
            if ($trashed === 'with') {
                $query->withTrashed();
            } elseif ($trashed === 'only') {
                $query->onlyTrashed();
            }
        });
    }

    public function hasPayLater(): bool
    {
        return $this->serviceCredentials()
            ->where('provider', 'payment_assist')
            ->where('is_live', app()->isProduction())
            ->exists();
    }
}
