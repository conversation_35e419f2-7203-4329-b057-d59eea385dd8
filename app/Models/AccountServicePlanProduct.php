<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class AccountServicePlanProduct extends Model
{
    use HasFactory;

    protected $casts = [
        'service_types' => 'json',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(ServicePlanProduct::class, 'service_plan_product_id');
    }

    public function getLabel(): string
    {
        return sprintf(
            '%s - %s %s',
            $this->product->name,
            $this->duration_years,
            Str::plural('year', $this->duration_years)
        );
    }

    public function serviceTypes(): BelongsToMany
    {
        return $this->belongsToMany(ServiceType::class)
            ->withPivot('limit')
            ->withTimestamps();
    }
}
