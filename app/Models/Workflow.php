<?php

namespace App\Models;

use App\Workflows\Enums\WorkflowConditionType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Workflow extends Model
{
    /** @use HasFactory<\Database\Factories\WorkflowFactory> */
    use HasFactory;

    use SoftDeletes;

    protected $casts = [
        'is_active' => 'boolean',
        'filters' => 'array',
    ];

    public function runs(): HasMany
    {
        return $this->hasMany(WorkflowRun::class);
    }

    public function salesLeads()
    {
        return $this->belongsToMany(SalesLead::class)->withTimestamps();
    }

    public function actions(): HasMany
    {
        return $this->hasMany(WorkflowAction::class)->orderBy('order');
    }

    public function getQuery(): Builder
    {
        $query = Sale::query();

        foreach ($this->filters as $filterGroup) {
            $query = $query->where(function ($q) use ($filterGroup) {
                foreach ($filterGroup['conditions'] as $condition) {
                    $logic = $filterGroup['logic'] ?? 'AND';

                    WorkflowConditionType::from($condition['type'])->getWorkflowFilter()->execute($q, $condition, $logic);
                }
            });
        }

        return $query;
    }

    public function run(?Builder $query = null)
    {
        $workflowRun = $this->runs()->create([
            'filters' => $this->filters,
            'actions' => $this->actions->toArray(),
        ]);

        $query = $query ?: $this->getQuery();

        $query->each(function (Sale $sale) {
            /** @var SalesLead $salesLead */
            $salesLead = $sale->salesLead()->firstOrCreate([], [
                'assigned_to_user_id' => null,
            ]);

            $salesLead->workflows()->syncWithoutDetaching([$this]);

            $salesLead->workflowActions()->syncWithoutDetaching($this->actions);

            $salesLead->workflowActions()
                ->wherePivotNull('executed_at')
                ->each(function (WorkflowAction $action) use ($salesLead) {
                    $action->execute($salesLead);
                    $action->pivot->markAsExecuted();
                });
        });
    }

    public function filtersAsHumanReadable(): string
    {
        return collect($this->filters)->map(function ($filterGroup) {
            $group = collect($filterGroup['conditions'])
                ->map(fn ($condition) => WorkflowConditionType::from($condition['type'])
                    ->getWorkflowFilter()
                    ->label($condition))
                ->join("\n".($filterGroup['logic'] ?? '???').' ');
            if (($filterGroup['logic'] ?? '') === 'OR' && count($this->filters) > 1 && count($filterGroup['conditions']) > 1) {
                return "({$group})";
            }

            return $group;
        })->join("\nAND ");
    }
}
