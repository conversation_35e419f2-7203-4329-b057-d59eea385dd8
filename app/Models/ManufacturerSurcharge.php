<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OvonGroup\ManufacturerLogos\Models\Manufacturer;

class ManufacturerSurcharge extends Model
{
    /** @use HasFactory<\Database\Factories\ManufacturerSurchargeFactory> */
    use HasFactory;

    protected $fillable = [
        'account_id',
        'manufacturer_id',
        'warranty_admin_fee_percentage',
        'warranty_provision_percentage',
        'warranty_selling_price_percentage',
    ];

    protected $casts = [
        'warranty_admin_fee_percentage' => 'decimal:1',
        'warranty_provision_percentage' => 'decimal:1',
        'warranty_selling_price_percentage' => 'decimal:1',
    ];

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function manufacturer(): BelongsTo
    {
        return $this->belongsTo(Manufacturer::class);
    }

    public static function accountSpecific(Account $account, Manufacturer $manufacturer)
    {
        return static::where('account_id', $account->id)
            ->where('manufacturer_id', $manufacturer->id)
            ->first();
    }
}
