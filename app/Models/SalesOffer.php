<?php

namespace App\Models;

use App\Models\Traits\HasUuids;
use App\Notifications\SalesOfferNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SalesOffer extends Model
{
    /** @use HasFactory<\Database\Factories\SalesOfferFactory> */
    use HasFactory;

    use HasUuids;

    protected $casts = [
        'sent_at' => 'datetime',
    ];

    public function offer(): BelongsTo
    {
        return $this->belongsTo(Offer::class);
    }

    public function salesLead(): BelongsTo
    {
        return $this->belongsTo(SalesLead::class);
    }

    public function send()
    {
        $this->salesLead->sale->customer->notify(new SalesOfferNotification($this));
        $this->update(['sent_at' => $this->freshTimestamp()]);
    }
}
