<?php

namespace App\Models;

use App\Models\Concerns\VoipCallable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\HtmlString;

class Repairer extends Model implements VoipCallable
{
    use HasFactory;
    use SoftDeletes;

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where('name', 'like', '%'.$search.'%');
        })->when($filters['trashed'] ?? null, function ($query, $trashed) {
            if ($trashed === 'with') {
                $query->withTrashed();
            } elseif ($trashed === 'only') {
                $query->onlyTrashed();
            }
        });
    }

    public function fullAddress($delimiter = '<br>')
    {
        return new HtmlString(
            collect([
                'address_1',
                'address_2',
                'city',
                'county',
                'country',
                'postcode',
            ])->map(fn ($k) => $this->$k)->filter()->join($delimiter)
        );
    }

    public function claimEstimates()
    {
        return $this->hasMany(ClaimEstimate::class);
    }

    public function dealerships()
    {
        return $this->belongsToMany(Dealership::class)->withTimestamps();
    }

    public function getPhoneNumberForVoipCall(): string
    {
        return $this->phone;
    }
}
