<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Offer extends Model
{
    /** @use HasFactory<\Database\Factories\OfferFactory> */
    use HasFactory;

    public function warrantyProduct(): BelongsTo
    {
        return $this->belongsTo(WarrantyProduct::class);
    }

    public function breakdownProduct(): BelongsTo
    {
        return $this->belongsTo(BreakdownProduct::class);
    }

    public function servicePlanProduct(): BelongsTo
    {
        return $this->belongsTo(ServicePlanProduct::class);
    }

    public function getTotalSellingPrice(): float
    {
        return array_sum([
            $this->warranty_selling_price,
            $this->breakdown_selling_price,
            $this->service_plan_selling_price,
        ]);
    }
}
