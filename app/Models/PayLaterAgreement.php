<?php

namespace App\Models;

use App\Enums\PayLaterAgreementStatus;
use App\Models\Traits\HasTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class PayLaterAgreement extends Model
{
    /** @use HasFactory<\Database\Factories\PayLaterAgreementFactory> */
    use HasFactory;

    use HasTenant;

    protected $casts = [
        'is_approved' => 'boolean',
        'status' => PayLaterAgreementStatus::class,
    ];

    public function payable(): MorphTo
    {
        return $this->morphTo('payable')->withoutGlobalScope('confirmed');
    }

    public function payLaterPlan(): BelongsTo
    {
        return $this->belongsTo(PayLaterPlan::class);
    }

    public function scopeIncomplete($query)
    {
        return $query->where(fn ($q) => $q->whereNull('status')->orWhereIn('status', ['pending', 'failed']));
    }

    public function isActive(): bool
    {
        return $this->status === PayLaterAgreementStatus::COMPLETED;
    }

    public function sendPaymentSetupNotification()
    {
        return $this->payable->sendPaymentSetupNotification();
    }
}
