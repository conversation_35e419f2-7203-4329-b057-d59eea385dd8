<?php

namespace App\Models;

use App\Models\Traits\HasTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountProduct extends Model
{
    use HasFactory, HasTenant;

    public function productGroup()
    {
        return $this->belongsTo(ProductGroup::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
