<?php

namespace App\Models;

use App\Enums\PaymentMethod;
use App\Filament\Resources\SaleResource;
use App\Models\Concerns\BillableContract;
use App\Models\Concerns\LinkableContract;
use App\Models\Concerns\PayLaterContract;
use App\Models\Traits\HasFiles;
use App\Models\Traits\HasTenant;
use App\Notifications\CustomerRequiresPaymentMethodNotification;
use App\Notifications\CustomerWelcomeAndDocumentNotification;
use App\Services\Payments\DirectDebit\CustomerData;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class Sale extends Model implements BillableContract, LinkableContract, PayLaterContract
{
    use HasFactory;
    use HasFiles;
    use HasTenant;
    use Prunable;

    protected $casts = [
        'last_service_date' => 'date',
        'start_date' => 'date',
        'end_date' => 'date',
        'cancelled_at' => 'datetime',
        'confirmed_at' => 'datetime',
    ];

    protected static function booted()
    {
        // Global scope prevents unconfirmed sales from being returned.
        // There is an override on the addProducts page to allow unconfirmed sales to be confirmed.
        // Sales that have not been confirmed will be eventually cleaned up by a scheduled job.
        if (! app()->runningInConsole()) {
            static::addGlobalScope('confirmed', function (\Illuminate\Database\Eloquent\Builder $builder) {
                $builder->confirmed();
            });
        }
    }

    public function dealership(): BelongsTo
    {
        return $this->belongsTo(Dealership::class)->withTrashed();
    }

    public function warrantyClaims()
    {
        return $this->hasManyThrough(Claim::class, Warranty::class);
    }

    public function breakdownClaims()
    {
        return $this->hasManyThrough(BreakdownClaim::class, BreakdownPlan::class);
    }

    public function authorisedServices()
    {
        return $this->hasManyThrough(AuthorisedService::class, ServicePlan::class);
    }

    public function salesPerson(): BelongsTo
    {
        return $this->belongsTo(SalesPerson::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sold_by_id');
    }

    public function warranty(): HasOne
    {
        return $this->hasOne(Warranty::class);
    }

    public function breakdownPlan(): HasOne
    {
        return $this->hasOne(BreakdownPlan::class);
    }

    public function servicePlan(): HasOne
    {
        return $this->hasOne(ServicePlan::class);
    }

    public function products()
    {
        return $this->hasMany(SaleProduct::class);
    }

    public function invoiceLineItems(): HasMany
    {
        return $this->hasMany(InvoiceLineItem::class);
    }

    public function billingRequest(): BelongsTo
    {
        return $this->belongsTo(BillingRequest::class);
    }

    public function payments(): MorphMany
    {
        return $this->morphMany(Payment::class, 'payable');
    }

    public function payLaterAgreement(): MorphOne
    {
        return $this->morphOne(PayLaterAgreement::class, 'payable');
    }

    public function salesLead()
    {
        return $this->hasOne(SalesLead::class);
    }

    public function getVehicleDetailsAttribute()
    {
        return sprintf('%s %s', $this->vehicle_make, $this->vehicle_model);
    }

    public function scopeConfirmed($query)
    {
        return $query->whereNotNull('confirmed_at');
    }

    public function scopeUnconfirmed($query)
    {
        return $query->whereNull('confirmed_at');
    }

    public function isConfirmed(): bool
    {
        return $this->confirmed_at !== null;
    }

    public function isUnconfirmed(): bool
    {
        return ! $this->isConfirmed();
    }

    public function scopeActive($query)
    {
        return $query
            ->confirmed()
            ->whereDate('start_date', '<=', Carbon::today())
            ->where(fn ($q) => $q
                ->whereHas('warranty', fn ($q) => $q->active())
                ->orWhereHas('breakdownPlan', fn ($q) => $q->active())
                ->orWhereHas('servicePlan', fn ($q) => $q->active())
            );
    }

    public function scopeActiveRecurring($query)
    {
        return $query
            ->confirmed()
            ->whereHas('warranty', fn ($q) => $q
                ->notCancelled()
                ->recurring()
            )
            ->orWhereHas('servicePlan', fn ($q) => $q
                ->notCancelled()
                ->recurring()
            );
    }

    public function scopeNotCancelled($query)
    {
        return $query
            ->whereHas('warranty', fn ($q) => $q
                ->notCancelled()
            )
            ->orWhereHas('breakdownPlan', fn ($q) => $q
                ->notCancelled()
            )
            ->orWhereHas('servicePlan', fn ($q) => $q
                ->notCancelled()
            );
    }

    public function scopeNeedsDirectDebit($query)
    {
        return $query
            ->activeRecurring()
            ->whereDoesntHave('billingRequest', fn ($q) => $q->active());
    }

    public function scopeWithFailedSubscriptionPayments($query)
    {
        return $query->activeRecurring()
            ->whereHas('payments', fn ($q) => $q->where('status', Payment::STATUS_FAILED));
    }

    public function scopeWithIncompletePayLaterAgreements($query)
    {
        return $query
            ->confirmed()
            ->notCancelled()
            ->whereHas('payLaterAgreement', fn ($q) => $q->incomplete());
    }

    public function scopeSearch($query, string $search)
    {
        $query->when($search, fn ($query) => $query
            ->where(fn ($q) => $q
                ->orWhere('id', $search)
                ->orWhereHas('vehicle', fn ($q) => $q
                    ->orWhere('vrm', 'like', '%'.$search.'%')
                    ->orWhere('private_plate', 'like', '%'.$search.'%')
                    ->orWhere('make', 'like', '%'.$search.'%')
                    ->orWhere('model', 'like', '%'.$search.'%')
                )
                ->orWhereHas('customer', fn ($q) => $q
                    ->where('email', 'like', $search.'%')
                    ->orWhere('last_name', 'like', $search.'%')
                    ->orWhere('postcode', 'like', $search.'%')
                )
            )
        );
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['search'] ?? null, fn ($query, $search) => $query->search($filters['search']))
            ->when($filters['start'] ?? null, fn ($query, $start) => $query->whereDate('sales.created_at', '>=', $start))
            ->when($filters['end'] ?? null, fn ($query, $end) => $query->whereDate('sales.created_at', '<=', $end))
            ->when($filters['trashed'] ?? null, fn ($query, $trashed) => match ($trashed) {
                'with' => $query->withTrashed(),
                'only' => $query->onlyTrashed(),
                default => $query,
            });
    }

    public function initialCustomerInstallmentAmount(): ?float
    {
        return $this->warranty->initialCustomerInstallmentAmount();
    }

    public function ongoingMonthlySellingPrice()
    {
        return $this->warranty->monthly_selling_price;
    }

    public function isRecurring(): bool
    {
        return $this->warranty?->isRecurring() || $this->breakdownPlan?->isRecurring() || $this->servicePlan?->isRecurring();
    }

    public function isPayLater(): bool
    {
        return $this->payLaterAgreement?->is_approved && $this->payLaterAgreement->loan_amount > 0;
    }

    public function expiryDate(): ?Carbon
    {
        return max(
            $this->warranty?->end_date,
            $this->breakdownPlan?->end_date,
            $this->servicePlan?->end_date
        );
    }

    public function hasActivePayLaterAgreement(): bool
    {
        return $this->payLaterAgreement?->isActive() === true;
    }

    public function getCompletePaymentUrl(): ?string
    {
        if ($this->isPayLater() && ! $this->hasActivePayLaterAgreement()) {
            return $this->payLaterAgreement->url;
        }
        if ($this->isRecurring() && ! $this->billingRequest?->isActive()) {
            return $this->billingRequest?->mandate_url;
        }

        return null;
    }

    public function requiresPaymentSetup(): bool
    {
        if ($this->isPayLater() && ! $this->hasActivePayLaterAgreement()) {
            return true;
        }
        if ($this->isRecurring() && ! $this->billingRequest?->isActive()) {
            return true;
        }

        return false;
    }

    public function getBillableEntityLabel(): string
    {
        return 'customer';
    }

    public function getAccountHolderNameDefault(): string
    {
        return $this->customer->full_name;
    }

    public function getAccountId(): int
    {
        return $this->account_id;
    }

    public function toCustomerData(): CustomerData
    {
        return $this->customer->toCustomerData();
    }

    public function metaDataForPaymentProvider(): array
    {
        return [
            'customer_id' => (string) $this->customer_id,
            'sale_id' => (string) $this->id,
        ];
    }

    public function total(): string
    {
        return array_sum([
            $this->warranty?->selling_price,
            $this->breakdownPlan?->selling_price,
            $this->servicePlan?->selling_price,
            $this->products?->sum('selling_price'),
        ]);
    }

    public function notify($instance): void
    {
        $this->customer->notify($instance);
    }

    public function getEligibleWarrantyProducts()
    {
        return $this->account->warrantyProducts()
            ->with('product.coverLevel', 'bundledBreakdownProduct')
            ->orderBy(WarrantyProduct::select('position')
                ->whereColumn('product_id', 'warranty_products.id')
            )
            ->orderByRaw('max_engine_capacity IS NULL DESC')
            ->orderBy('max_engine_capacity')
            ->orderByRaw('max_mileage IS NULL DESC')
            ->orderBy('max_mileage')
            ->orderByRaw('max_age IS NULL DESC')
            ->orderBy('max_age')
            ->where(fn ($q) => $q->whereNull('max_mileage')->orWhere('max_mileage', '>=', $this->delivery_mileage))
            ->where(fn ($q) => $q->whereNull('max_age')->orWhere('max_age', '>=', today()->diffInMonths($this->vehicle->registration_date)))
            ->when($this->vehicle->engine_capacity, fn ($q, $value) => $q->where(fn ($q) => $q->whereNull('max_engine_capacity')->orWhere('max_engine_capacity', '>=', $value)))
            ->whereHas('product.coverLevel', fn ($q) => $q->where('vehicle_type', $this->vehicle->body_type))
            ->when(
                $this->vehicle->isPureElectric(),
                fn ($query) => $query->whereHas('product.coverLevel', fn ($q) => $q->isEv())
            )
            ->when(
                $this->vehicle->isHybrid(),
                fn ($query) => $query->whereHas('product.coverLevel', fn ($q) => $q->isHybrid())
            )
            ->when(
                $this->vehicle->isIce(),
                fn ($query) => $query->whereHas('product.coverLevel', fn ($q) => $q->isIce())
            )
            ->get();
    }

    public function sendPaymentSetupNotification(bool $isReminder = false): void
    {
        $this->notify(new CustomerRequiresPaymentMethodNotification($this, $isReminder));
    }

    public function sendWelcomeAndDocumentNotification(): void
    {
        if ($this->documentsCanBeGenerated()) {
            $this->customer->notify(new CustomerWelcomeAndDocumentNotification($this));
        }
    }

    public function getPaymentMethod(): ?PaymentMethod
    {
        return match (true) {
            $this->isPayLater() => PaymentMethod::PAY_LATER,
            $this->isRecurring() => PaymentMethod::DIRECT_DEBIT,
            default => null,
        };
    }

    public function description(): string
    {
        return implode(', ', array_filter([
            $this->warranty ? $this->warranty->product->getLabel().' Warranty' : null,
            $this->breakdownPlan ? $this->breakdownPlan->product->getLabel().' Breakdown Plan' : null,
            $this->servicePlan?->getLabel(),
        ]));
    }

    public function vrm(): string
    {
        return $this->vehicle->formattedVrm();
    }

    public function documentsCanBeGenerated(): bool
    {
        return ! $this->requiresPaymentSetup();
    }

    /**
     * Get the prunable model query.
     */
    public function prunable(): Builder
    {
        return static::query()
            ->unconfirmed()
            ->doesntHave('warranty')
            ->doesntHave('breakdownPlan')
            ->doesntHave('servicePlan')
            ->where('created_at', '<=', now()->subMonth());
    }

    public function getLabel(): string
    {
        return 'Sale #'.$this->id;
    }

    public function getAdminUrl(): string
    {
        return SaleResource::getUrl('view', [$this]);
    }

    public function getPayLaterAgreement(): ?PayLaterAgreement
    {
        return $this->payLaterAgreement;
    }

    public function getAccount(): Account
    {
        return $this->account;
    }

    public function getCustomer(): Customer
    {
        return $this->customer;
    }
}
