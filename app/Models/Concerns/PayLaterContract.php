<?php

namespace App\Models\Concerns;

use App\Models\Account;
use App\Models\Customer;
use App\Models\PayLaterAgreement;
use App\Services\Payments\DirectDebit\CustomerData;

interface PayLaterContract
{
    public function vrm(): string;

    public function description(): string;

    public function toCustomerData(): CustomerData;

    public function sendPaymentSetupNotification(): void;

    public function getPayLaterAgreement(): ?PayLaterAgreement;

    public function getAccount(): Account;

    public function getCustomer(): Customer;
}
