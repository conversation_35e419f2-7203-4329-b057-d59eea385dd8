<?php

namespace App\Models\Concerns;

use App\Enums\PaymentMethod;
use App\Services\Payments\DirectDebit\CustomerData;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

interface BillableContract
{
    public function requiresPaymentSetup(): bool;

    public function getCompletePaymentUrl(): ?string;

    public function getBillableEntityLabel(): string;

    public function getAccountHolderNameDefault(): string;

    public function toCustomerData(): CustomerData;

    public function metaDataForPaymentProvider(): array;

    public function notify($instance);

    public function sendPaymentSetupNotification(): void;

    public function getPaymentMethod(): ?PaymentMethod;

    //    public function billingRequest(): BelongsTo;
}
