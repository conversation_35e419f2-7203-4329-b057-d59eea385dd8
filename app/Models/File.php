<?php

namespace App\Models;

use App\Models\Traits\HasTenant;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class File extends Model
{
    use HasFactory, HasTenant, Prunable, SoftDeletes;

    public static function booted()
    {
        static::forceDeleted(function (self $instance) {
            $instance->deleteFromStorage();
        });
    }

    public function related()
    {
        return $this->morphTo();
    }

    public function getSizeForHumans(): string
    {
        $bytes = $this->size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB'];
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2).' '.$units[$i];
    }

    public function getBinaryContent(): string
    {
        return Storage::get($this->path);
    }

    public function getTemporaryUrl(array $options = []): string
    {
        return Storage::temporaryUrl(
            $this->path,
            now()->addHour(),
            $options
        );
    }

    public function getTemporaryDownloadUrl(): string
    {
        return $this->getTemporaryUrl([
            'ResponseContentType' => 'application/octet-stream',
            'ResponseContentDisposition' => "attachment; filename=\"{$this->name}\"",
        ]);
    }

    public function deleteFromStorage(): bool
    {
        return Storage::delete($this->path);
    }

    /**
     * Get the prunable model query.
     */
    public function prunable(): Builder
    {
        return static::query()
            ->onlyTrashed()
            ->where('deleted_at', '<', now()->subDays(30));
    }
}
