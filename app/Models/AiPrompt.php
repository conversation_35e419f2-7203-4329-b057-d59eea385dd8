<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AiPrompt extends Model
{
    /** @use HasFactory<\Database\Factories\AiPromptFactory> */
    use HasFactory;

    public function task(): BelongsTo
    {
        return $this->belongsTo(AiTask::class, 'ai_task_id');
    }

    public function requests()
    {
        return $this->hasMany(AiRequest::class)->latest();
    }
}
