<?php

namespace App\Models;

use App\Enums\PaymentMethod;
use App\Events\DealershipCreated;
use App\Events\DealershipUpdated;
use App\Filament\Resources\DealershipResource;
use App\Models\Concerns\BillableContract;
use App\Models\Traits\HasTenant;
use App\Notifications\DealerDirectDebitMandateNotification;
use App\Services\Accounting\ContactData;
use App\Services\Payments\DirectDebit\CustomerData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\HtmlString;

class Dealership extends Model implements BillableContract
{
    use HasFactory;
    use HasTenant;
    use InvoiceableTrait;
    use Notifiable;
    use SoftDeletes;

    protected function casts(): array
    {
        return [
            'no_direct_debit' => 'boolean',
        ];
    }

    protected $dispatchesEvents = [
        'created' => DealershipCreated::class,
        'updated' => DealershipUpdated::class,
    ];

    public function repairers(): BelongsToMany
    {
        return $this->belongsToMany(Repairer::class)->withTimestamps();
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    public function billingRequest(): BelongsTo
    {
        return $this->belongsTo(BillingRequest::class);
    }

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where('name', 'like', '%'.$search.'%');
        })->when($filters['trashed'] ?? null, function ($query, $trashed) {
            if ($trashed === 'with') {
                $query->withTrashed();
            } elseif ($trashed === 'only') {
                $query->onlyTrashed();
            }
        });
    }

    public function getName()
    {
        return $this->name;
    }

    public function getLink()
    {
        return route('dealerships.show', $this);
    }

    public function fullAddress($delimiter = '<br>')
    {
        return new HtmlString(
            collect([
                'address_1',
                'address_2',
                'city',
                'county',
                'country',
                'postcode',
            ])->map(fn ($k) => $this->$k)->filter()->join($delimiter)
        );
    }

    public function getContactData(): ContactData
    {
        return new ContactData(
            id: $this->accountingContact?->accounting_software_id,
            name: $this->getName(),
            email: $this->email,
            addressLine1: $this->address_1,
            addressLine2: $this->address_2,
            addressLine3: null,
            addressLine4: null,
            addressCity: $this->city,
            addressRegion: $this->county,
            addressPostcode: $this->postcode,
        );
    }

    public function getBillingRequest(): ?BillingRequest
    {
        return $this->billingRequest;
    }

    public function getCompletePaymentUrl(): ?string
    {
        return $this->billingRequest?->mandate_url;
    }

    public function requiresPaymentSetup(): bool
    {
        if ($this->getPaymentMethod() === PaymentMethod::MANUAL) {
            return false;
        }
        if ($this->billingRequest?->isActive() === true) {
            return false;
        }

        return true;
    }

    public function getUrl(): string
    {
        return DealershipResource::getUrl('view', [$this]);
    }

    public function getBillableEntityLabel(): string
    {
        return 'dealership';
    }

    public function getAccountHolderNameDefault(): string
    {
        return $this->contact_first_name.' '.$this->contact_last_name;
    }

    public function toCustomerData(): CustomerData
    {
        return new CustomerData(
            reference: $this->id,
            paymentProcessorId: null,
            firstName: $this->contact_first_name,
            lastName: $this->contact_last_name,
            email: $this->email,
            phone: $this->phone,
            line1: $this->address_1,
            line2: $this->address_2,
            city: $this->city,
            county: $this->county,
            postcode: $this->postcode,
            accountHolderName: $this->getAccountHolderNameDefault(),
        );
    }

    public function metaDataForPaymentProvider(): array
    {
        return [
            'dealership_id' => (string) $this->id,
        ];
    }

    public function sendPaymentSetupNotification(): void
    {
        $this->notify(new DealerDirectDebitMandateNotification($this));
    }

    public function getPaymentMethod(): ?PaymentMethod
    {
        if ($this->no_direct_debit) {
            return PaymentMethod::MANUAL;
        }

        return PaymentMethod::DIRECT_DEBIT;
    }
}
