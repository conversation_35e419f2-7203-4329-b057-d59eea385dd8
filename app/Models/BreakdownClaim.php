<?php

namespace App\Models;

use App\Models\Traits\HasFiles;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class BreakdownClaim extends Model
{
    use HasFiles;

    public static function boot()
    {
        parent::boot();

        static::creating(function ($instance) {
            $instance->fill([
                'entered_by_id' => Auth::id(),
                'claim_number' => sprintf(
                    '%s-BC%s',
                    $instance->plan->sale_id,
                    $instance->plan->claims()->count() + 1
                ),
            ]);
        });
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(BreakdownPlan::class, 'breakdown_plan_id');
    }

    public function enteredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'entered_by_id');
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['search'] ?? null, fn ($query, $search) => $query
                ->whereHas('plan.sale', fn ($q) => $q->search($search))
            )
            ->when($filters['start'] ?? null, fn ($query, $start) => $query->whereDate('created_at', '>=', $start))
            ->when($filters['end'] ?? null, fn ($query, $end) => $query->whereDate('created_at', '<=', $end));
    }

    public function getAccountId(): int
    {
        return $this->plan->account_id;
    }
}
