<?php

namespace App\Models;

use App\Enums\ClaimEstimateStatus;
use App\Events\ClaimEstimateCreated;
use App\Models\Traits\HasFiles;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ClaimEstimate extends Model
{
    use HasFactory;
    use HasFiles;

    const BILLING_STATUS_INTERNAL = 'internal';

    const BILLING_STATUS_DIRECT = 'direct';

    const BILLING_STATUS_OUTSTANDING = 'outstanding';

    const BILLING_STATUS_INVOICED = 'invoiced';

    const BILLING_STATUS_PAID = 'paid';

    protected $casts = [
        //        'total_parts' => 'float',
        //        'total_labour' => 'float',
        //        'vat' => 'float',
        'is_charged_internally' => 'boolean',
        'is_invoicing_dealer_direct' => 'boolean',
        'estimate_booking_date' => 'date',
        'estimate_completed_at' => 'datetime',
    ];

    protected $dispatchesEvents = [
        'created' => ClaimEstimateCreated::class,
    ];

    public function claim(): BelongsTo
    {
        return $this->belongsTo(Claim::class);
    }

    public function repairer(): BelongsTo
    {
        return $this->belongsTo(Repairer::class, 'repairer_id');
    }

    public function lineItems(): HasMany
    {
        return $this->hasMany(ClaimEstimateLineItem::class);
    }

    public function authorisation(): HasOne
    {
        return $this->hasOne(ClaimAuthorisation::class, 'estimate_id');
    }

    public function enteredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function totalAmount(): float
    {
        return $this->lineItems->sum('total');
    }

    public function vat(): float
    {
        return $this->lineItems->sum(fn (ClaimEstimateLineItem $lineItem) => $lineItem->quantity * $lineItem->vat);
    }

    public function status(): ClaimEstimateStatus
    {
        if ($this->authorisation) {
            return match (true) {
                $this->is_invoicing_dealer_direct || $this->is_charged_internally => ClaimEstimateStatus::SETTLED,
                default => $this->authorisation->status(),
            };
        }

        return match (true) {
            ! $this->estimate_completed_at && $this->estimate_booking_date?->isFuture() => ClaimEstimateStatus::BOOKED,
            ! $this->estimate_completed_at && $this->estimate_booking_date?->isBefore(today()) => ClaimEstimateStatus::OVERDUE,
            (bool) $this->estimate_completed_at => ClaimEstimateStatus::AWAITING_DECISION,
            default => ClaimEstimateStatus::PENDING,
        };
    }

    public function getAccountId(): int
    {
        return $this->claim->getAccountId();
    }

    public function scopeWithStatus($query, $status)
    {
        return match ($status) {
            ClaimEstimateStatus::SETTLED => $query->whereHas('authorisation', fn ($q) => $q->where(fn ($q) => $q
                ->where('is_invoicing_dealer_direct', true)
                ->orWhere('is_charged_internally', true)
                ->orWhereHas('invoice.payment')
            )),
            ClaimEstimateStatus::AUTHORISED => $query->whereHas('authorisation', fn ($q) => $q
                ->where('is_invoicing_dealer_direct', false)
                ->where('is_charged_internally', false)
                ->whereDoesntHave('invoice.payment')
            ),
            ClaimEstimateStatus::REJECTED => $query->whereHas('claim.rejection'),
            ClaimEstimateStatus::PENDING => $query->whereDoesntHave('authorisation')->whereDoesntHave('claim.rejection'),
        };
    }

    public function billingStatus()
    {
        if ($this->is_charged_internally) {
            return static::BILLING_STATUS_INTERNAL;
        }

        if ($this->is_invoicing_dealer_direct) {
            return static::BILLING_STATUS_DIRECT;
        }

        if ($this->authorisation) {
            return $this->authorisation->billingStatus();
        }

        return static::BILLING_STATUS_OUTSTANDING;
    }

    public function statusColor(): string
    {
        return $this->status()->color();

    }

    public function billingStatusColor()
    {
        return match ($this->billingStatus()) {
            static::BILLING_STATUS_OUTSTANDING => 'warning',
            static::BILLING_STATUS_INVOICED, static::BILLING_STATUS_PAID => 'success',
            static::BILLING_STATUS_INTERNAL, static::BILLING_STATUS_DIRECT => 'info',
        };
    }

    public function getWorkshopNameAttribute()
    {
        return $this->repairer ? $this->repairer->name : $this->attributes['workshop_name'];
    }

    public function net()
    {
        return $this->lineItems->sum('net');
    }

    public function total()
    {
        return $this->lineItems->sum('total');
    }
}
