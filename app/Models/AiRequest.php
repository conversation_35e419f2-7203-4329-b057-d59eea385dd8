<?php

namespace App\Models;

use App\Services\AI\AiTaskProcessor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class AiRequest extends Model
{
    /** @use HasFactory<\Database\Factories\AiRequestFactory> */
    use HasFactory;

    protected $casts = [
        'response' => 'json',
    ];

    public function prompt(): BelongsTo
    {
        return $this->belongsTo(AiPrompt::class, 'ai_prompt_id');
    }

    public function subject(): BelongsTo
    {
        return $this->morphTo();
    }

    public function votes(): HasMany
    {
        return $this->hasMany(AiVote::class);
    }

    public function score()
    {
        return $this->hasOne(AiRequestScore::class);
    }

    public function isProcessed(): bool
    {
        return $this->completion_tokens !== null;
    }

    public function process(AiTaskProcessor $processor): static
    {
        if ($this->isProcessed()) {
            return $this;
        }

        $response = $processor->getResponse();

        $this->fill([
            'completion_model' => $response->aiModel,
            'finish_reason' => $response->finishReason,
            'response' => $response->content,
            'prompt_tokens' => $response->promptTokens,
            'completion_tokens' => $response->completionTokens,
            'processing_ms' => $response->processingMs,
        ])->save();

        return $this;
    }

    public function vote(): HasOne
    {
        return $this->hasOne(AiVote::class)
            ->where('user_id', auth()->id());
    }

    public function voteUp(User $user): AiVote
    {
        return $this->castVote($user, 1);
    }

    public function voteDown(User $user): AiVote
    {
        return $this->castVote($user, -1);
    }

    public function castVote(User $user, $rating): AiVote
    {
        return $this->votes()->updateOrCreate([
            'user_id' => $user->id,
        ], [
            'rating' => $rating,
        ]);
    }
}
