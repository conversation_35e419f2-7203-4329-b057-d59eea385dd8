<?php

namespace App\Models;

use App\Enums\AuthorisedServiceStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AuthorisedService extends Model
{
    use HasFactory;

    protected $casts = [
        'status' => AuthorisedServiceStatus::class,
    ];

    public static function booted()
    {
        static::creating(callback: function ($instance) {
            $instance->fill([
                'user_id' => auth()->id(),
                'authorisation_code' => static::generateUniqueAuthorisationCode(),
            ]);
        });
    }

    public function servicePlan(): BelongsTo
    {
        return $this->belongsTo(ServicePlan::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }

    public static function generateUniqueAuthorisationCode(): string
    {
        do {
            $code = substr(str_shuffle(str_repeat('ABCDEFGHJKLMNPQRSTUVWXYZ', 10)), 0, 10);
        } while (static::where('authorisation_code', $code)->exists());

        return $code;
    }
}
