<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MotTest extends Model
{
    protected $casts = [
        'completed_at' => 'datetime',
        'expiry_date' => 'date',
    ];

    public function defects(): HasMany
    {
        return $this->hasMany(MotTestDefect::class);
    }

    public function scopeValid($query)
    {
        return $query
            ->where('result', 'PASSED')
            ->where('expiry_date', '>=', now())
            ->latest('expiry_date');
    }
}
