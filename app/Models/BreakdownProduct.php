<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class BreakdownProduct extends Model
{
    use HasFactory;

    public function isRecurring(): bool
    {
        return false;
    }

    public function getLabel(): string
    {
        return sprintf(
            '%s %d %s',
            $this->name,
            $this->period,
            Str::plural('month', $this->period)
        );
    }
}
