<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class WarrantyProduct extends Model
{
    use HasFactory;
    use SoftDeletes;

    public function accountProducts(): HasMany
    {
        return $this->hasMany(AccountWarrantyProduct::class);
    }

    public function coverLevel(): BelongsTo
    {
        return $this->belongsTo(CoverLevel::class);
    }

    public function variants(): HasMany
    {
        return $this->hasMany(WarrantyProductVariant::class, 'product_id');
    }

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where('cover', 'like', '%'.$search.'%');
        })->when($filters['trashed'] ?? null, function ($query, $trashed) {
            if ($trashed === 'with') {
                $query->withTrashed();
            } elseif ($trashed === 'only') {
                $query->onlyTrashed();
            }
        });
    }

    public function getLabel(): string
    {
        return sprintf(
            '%s %s',
            $this->coverLevel->name,
            $this->is_recurring ? 'Monthly' : $this->period.' '.Str::plural('month', $this->period)
        );
    }
}
