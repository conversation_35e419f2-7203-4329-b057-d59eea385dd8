<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class AccountWarrantyProduct extends Model
{
    use HasFactory;

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(WarrantyProduct::class);
    }

    public function bundledBreakdownProduct(): HasOne
    {
        return $this->hasOne(AccountWarrantyBreakdownProductBundle::class);
    }

    public function getLabel(): string
    {
        $label = $this->product->getLabel();
        if ($this->bundledBreakdownProduct) {
            $label .= ' with '.$this->bundledBreakdownProduct->breakdownProduct->getLabel();
        }

        return $label;
    }
}
