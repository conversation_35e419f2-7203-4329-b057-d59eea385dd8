<?php

namespace App\Models;

use App\Workflows\Enums\WorkflowActionType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkflowAction extends Model
{
    /** @use HasFactory<\Database\Factories\WorkflowActionFactory> */
    use HasFactory;

    protected $casts = [
        'type' => WorkflowActionType::class,
        'values' => 'array',
    ];

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    public function execute(SalesLead $salesLead): void
    {
        $this->type->getWorkflowAction()->execute($salesLead, $this->values);
    }

    public function label(): string
    {
        return $this->type->getWorkflowAction()->label($this->values);
    }
}
