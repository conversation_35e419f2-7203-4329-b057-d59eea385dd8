<?php

namespace App\Models;

use App\Enums\ClaimStatus;
use App\Events\ClaimCreated;
use App\Models\Traits\HasFiles;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Parallax\FilamentComments\Models\Traits\HasFilamentComments;

class Claim extends Model
{
    use HasFactory;
    use HasFilamentComments;
    use HasFiles;

    protected $casts = [
        'failure_date' => 'date',
    ];

    protected $dispatchesEvents = [
        'created' => ClaimCreated::class,
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($instance) {
            $instance->fill([
                'entered_by_id' => auth()->id(),
                'reference' => sprintf(
                    '%s-%s',
                    $instance->warranty->id,
                    $instance->warranty->claims()->count() + 1
                ),
            ]);
        });
    }

    public function getRouteKeyName()
    {
        return 'reference';
    }

    public function warranty(): BelongsTo
    {
        return $this->belongsTo(Warranty::class);
    }

    public function faultType(): BelongsTo
    {
        return $this->belongsTo(FaultType::class);
    }

    public function estimates(): HasMany
    {
        return $this->hasMany(ClaimEstimate::class);
    }

    public function estimateLineItems(): HasManyThrough
    {
        return $this->hasManyThrough(ClaimEstimateLineItem::class, ClaimEstimate::class, null, 'claim_estimate_id');
    }

    public function authorisations(): HasManyThrough
    {
        return $this->hasManyThrough(ClaimAuthorisation::class, ClaimEstimate::class, null, 'estimate_id');
    }

    public function rejection(): HasOne
    {
        return $this->hasOne(ClaimRejection::class);
    }

    public function enteredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'entered_by_id');
    }

    public function getStatusAttribute(): ClaimStatus
    {
        return match (true) {
            $this->rejection()->exists() => ClaimStatus::REJECTED,
            $this->authorisations()->exists() => ClaimStatus::AUTHORISED,
            $this->estimates()->whereDoesntHave('authorisation')
                ->whereNotNull('estimate_completed_at')
                ->exists() => ClaimStatus::ESTIMATE_RECEIVED,
            $this->estimates()->whereDoesntHave('authorisation')
                ->whereNull('estimate_completed_at')
                ->whereDate('estimate_booking_date', '<=', today())
                ->exists() => ClaimStatus::AWAITING_ESTIMATE,
            default => ClaimStatus::AWAITING_ESTIMATE,
        };
    }

    public function aiRequests()
    {
        return $this->morphMany(AiRequest::class, 'subject');
    }

    public function scopeWithStatus($query, $status)
    {
        return match ($status) {
            ClaimStatus::SETTLED => $query->whereHas('authorisations', fn ($q) => $q->whereNotNull('settled_at')),
            ClaimStatus::AUTHORISED => $query->whereHas('authorisations'),
            ClaimStatus::REJECTED => $query->whereHas('rejection'),
            ClaimStatus::AWAITING_ESTIMATE => $query->whereDoesntHave('authorisations')->whereDoesntHave('rejection'),
        };
    }

    public function scopeFilter($query, array $filters): Builder
    {
        return $query
            ->when($filters['start'] ?? null, fn ($query, $start) => $query->whereDate('failure_date', '>=', $start))
            ->when($filters['end'] ?? null, fn ($query, $end) => $query->whereDate('failure_date', '<=', $end));
    }

    public function getAccountId(): int
    {
        return $this->warranty->getAccountId();
    }

    public function authorisedNet()
    {
        return $this->estimates->sum->net();
    }

    public function getClaimDataForBrowserPlugin(): array
    {
        return [
            'customer' => [
                'name' => $this->warranty->sale->customer->full_name,
                'address_1' => $this->warranty->sale->customer->address_1,
                'address_2' => $this->warranty->sale->customer->address_2,
                'city' => $this->warranty->sale->customer->city,
                'county' => $this->warranty->sale->customer->county,
                'postcode' => $this->warranty->sale->customer->postcode,
                'phone' => $this->warranty->sale->customer->phone,
                'email' => $this->warranty->sale->customer->email,
            ],
            'vehicle' => [
                'mileage' => $this->current_mileage,
                'vrm' => $this->warranty->sale->vehicle->vrm,
                'make' => $this->warranty->sale->vehicle->make,
                'model' => $this->warranty->sale->vehicle->model,
                'year' => $this->warranty->sale->vehicle->registration_date,
                'fuel_type' => $this->warranty->sale->vehicle->fuel_type,
                'transmission' => $this->warranty->sale->vehicle->transmission_type,
            ],
            'claim' => [
                'fault_description' => $this->fault_description,
            ],
            'plugin' => [
                'version' => config('browser-plugin.version'),
            ],
        ];
    }
}
