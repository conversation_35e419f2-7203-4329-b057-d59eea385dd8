<?php

namespace App\Models;

use App\Enums\ClaimEstimateStatus;
use App\Models\Traits\HasFiles;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class ClaimAuthorisation extends Model
{
    use HasFactory;
    use HasFiles;

    public static function boot()
    {
        parent::boot();

        static::creating(function ($instance) {
            $instance->fill([
                'user_id' => Auth::id(),
            ]);
        });
    }

    public function estimate(): BelongsTo
    {
        return $this->belongsTo(ClaimEstimate::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function getAccountId(): int
    {
        return $this->estimate->getAccountId();
    }

    public function status(): ClaimEstimateStatus
    {
        if ($this->invoice()->has('payment')->exists()) {
            return ClaimEstimateStatus::SETTLED;
        }

        return ClaimEstimateStatus::AUTHORISED;
    }

    public function billingStatus()
    {
        if ($this->invoice?->status === Invoice::STATUS_PAID) {
            return ClaimEstimate::BILLING_STATUS_PAID;
        }

        if ($this->invoice) {
            return ClaimEstimate::BILLING_STATUS_INVOICED;
        }

        return ClaimEstimate::BILLING_STATUS_OUTSTANDING;
    }
}
