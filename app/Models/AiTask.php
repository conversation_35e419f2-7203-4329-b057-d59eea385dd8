<?php

namespace App\Models;

use App\Services\AI\AiTaskProcessor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;

class AiTask extends Model
{
    /** @use HasFactory<\Database\Factories\AiTaskFactory> */
    use HasFactory;

    public function prompts()
    {
        return $this->hasMany(AiPrompt::class);
    }

    public function prompt(): HasOne
    {
        return $this->hasOne(AiPrompt::class, 'ai_task_id')->latestOfMany();
    }

    public function requests(): HasManyThrough
    {
        return $this->hasManyThrough(AiRequest::class, AiPrompt::class);
    }

    public function reprocessRequest(Model $subject): AiRequest
    {
        return $this->prompt
            ->requests()
            ->create([
                'subject_type' => $subject->getMorphClass(),
                'subject_id' => $subject->getKey(),
            ])
            ->process($this->getProcessor($subject));
    }

    public function fetchOrProcessRequest(Model $subject): AiRequest
    {
        return $this->prompt
            ->requests()
            ->firstOrCreate([
                'subject_type' => $subject->getMorphClass(),
                'subject_id' => $subject->getKey(),
            ])
            ->process($this->getProcessor($subject));
    }

    public function getProcessor(Model $subject): AiTaskProcessor
    {
        return app($this->processor_class, [
            'aiModel' => $this->prompt->model,
            'systemPrompt' => $this->prompt->prompt,
            'subject' => $subject,
        ]);
    }
}
