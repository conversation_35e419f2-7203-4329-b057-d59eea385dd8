<?php

namespace App\Models;

use App\Enums\FundType;
use App\Enums\ProductStatus;
use App\Models\Traits\HasTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BreakdownPlan extends Model
{
    use HasFactory, HasTenant;

    protected $casts = [
        'fund_type' => FundType::class,
        'end_date' => 'date',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(BreakdownProduct::class, 'breakdown_product_id');
    }

    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class)->withoutGlobalScope('confirmed');
    }

    public function claims(): HasMany
    {
        return $this->hasMany(BreakdownClaim::class);
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['search'] ?? null, fn ($query, $search) => $query->whereHas('warranty', fn ($q) => $q->filter($filters)))
            ->when($filters['start'] ?? null, fn ($query, $start) => $query->whereHas('sale', fn ($q) => $q->whereDate('confirmed_at', '>=', $start)))
            ->when($filters['end'] ?? null, fn ($query, $end) => $query->whereHas('sale', fn ($q) => $q->whereDate('confirmed_at', '<=', $end)))
            ->when(isset($filters['cancelled']), fn ($query) => $filters['cancelled'] ? $query->cancelled() : $query->notCancelled())
            ->when(isset($filters['expired']), fn ($query) => $filters['expired'] ? $query->expired() : $query->notExpired());
    }

    public function scopePending($query)
    {
        return $query->notExpired()->notCancelled()->whereDate('start_date', '>', today());
    }

    public function scopeActive($query)
    {
        return $query->notExpired()->notCancelled()->whereDate('start_date', '<=', today());
    }

    public function scopeExpired($query, $expired = true)
    {
        return $query->whereDate('end_date', $expired ? '<' : '>=', today());
    }

    public function scopeNotExpired($query)
    {
        return $this->scopeExpired($query, false);
    }

    public function scopeCancelled($query)
    {
        return $query->whereNotNull('cancelled_at');
    }

    public function scopeNotCancelled($query)
    {
        return $query->whereNull('cancelled_at');
    }

    public function scopeDealerFunded($query, $dealerFunded = true)
    {
        return $query->when(
            $dealerFunded,
            fn ($q) => $q->whereIn('fund_type', FundType::dealerFundedValues()),
            fn ($q) => $q->whereNotIn('fund_type', FundType::dealerFundedValues()),
        );
    }

    public function getStatusAttribute(): ProductStatus
    {
        if ($this->cancelled_at) {
            return ProductStatus::CANCELLED;
        }
        if ($this->end_date?->endOfDay()->isPast()) {
            return ProductStatus::EXPIRED;
        }
        if ($this->sale?->start_date->startOfDay()->isPast()) {
            return ProductStatus::LIVE;
        }

        return ProductStatus::PENDING;
    }

    public function isRecurring(): bool
    {
        return false;
    }

    public function isDealerFunded(): bool
    {
        return $this->fund_type->isDealerFunded();
    }

    public function nominalAccountCode(): string
    {
        return match ($this->isDealerFunded()) {
            true => config('accounting.nominal_codes.breakdown.self_funded_revenue.code'),
            false => config('accounting.nominal_codes.breakdown.managed_fund_revenue.code'),
        };
    }
}
