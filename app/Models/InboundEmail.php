<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;
use ZBateson\MailMimeParser\Message;

class InboundEmail extends Model
{
    /** @use HasFactory<\Database\Factories\InboundEmailFactory> */
    use HasFactory;

    protected $casts = [
        'timestamp' => 'datetime',
        'destinations' => 'array',
    ];

    public function getContent(): string
    {
        return Storage::createS3Driver([
            'bucket' => $this->s3_bucket_name,
            'key' => config('filesystems.disks.s3.key'),
            'secret' => config('filesystems.disks.s3.secret'),
            'region' => config('filesystems.disks.s3.region'),
        ])->get($this->s3_object_key);
    }

    public function getParsedContent(): Message
    {
        return Message::from($this->getContent(), attached: false);
    }

    public function attachments(): HasMany
    {
        return $this->hasMany(File::class, 'inbound_email_id');
    }
}
