<?php

namespace App\Models;

use App\Filament\Resources\PayoutResource;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Payout extends Model
{
    use HasFactory;

    protected $casts = [
        'arrival_date' => 'date',
    ];

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function lineItems(): HasMany
    {
        return $this->hasMany(PayoutLineItem::class);
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['start'] ?? null, fn ($query, $start) => $query->whereDate('arrival_date', '>=', $start))
            ->when($filters['end'] ?? null, fn ($query, $end) => $query->whereDate('arrival_date', '<=', $end));
    }

    public function accountingSoftwareViewTransactionUrl(): ?string
    {
        if ($this->accounting_software_to_transaction_id === null) {
            return null;
        }

        return sprintf(config('services.xero.view_bank_transaction_url'), $this->accounting_software_to_transaction_id);
    }

    public function goCardlessUrl(): ?string
    {
        if ($this->processor_payout_id === null) {
            return null;
        }

        return config('payments.go_cardless.manage_url').'/payouts/'.$this->processor_payout_id;
    }

    public function getUrl()
    {
        return PayoutResource::getUrl('view', ['record' => $this]);
    }
}
