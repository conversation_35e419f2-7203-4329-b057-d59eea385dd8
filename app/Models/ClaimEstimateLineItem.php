<?php

namespace App\Models;

use App\Enums\ClaimEstimateLineItemType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClaimEstimateLineItem extends Model
{
    /** @use HasFactory<\Database\Factories\ClaimEstimateLineItemFactory> */
    use HasFactory;

    protected $casts = [
        'type' => ClaimEstimateLineItemType::class,
    ];

    public function estimate(): BelongsTo
    {
        return $this->belongsTo(ClaimEstimate::class, 'claim_estimate_id');
    }

    public function vehicleComponent(): BelongsTo
    {
        return $this->belongsTo(VehicleComponent::class);
    }
}
