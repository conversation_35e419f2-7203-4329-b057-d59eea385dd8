<?php

namespace App\Http\Middleware;

use App\Models\Account;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;
use Symfony\Component\HttpFoundation\Response;

class BindTenantToRouteGenerator
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        $tenant = $request->route()->parameter('tenant');

        if ($tenant) {
            //            dd($tenantId);
            //            $tenant = Account::findOrFail($tenantId);
            URL::defaults(['tenant' => $tenant]);

            filament()->setTenant($tenant);
        }

        return $next($request);
    }
}
