<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ValidateXeroWebhook
{
    public function handle(Request $request, Closure $next): Response
    {
        // Xero webhook secret key (get it from your environment)
        $webhookKey = config('services.xero.webhook_key');

        // Xero provides the signature in the `x-xero-signature` header
        $xeroSignature = $request->header('x-xero-signature');

        // Ensure the header is present
        if (! $xeroSignature) {
            Log::warning('Xero webhook: missing signature');

            return response('Unauthorized', Response::HTTP_UNAUTHORIZED);
        }

        // Calculate the expected signature
        $payload = $request->getContent(); // Get the raw JSON payload
        $expectedSignature = base64_encode(hash_hmac('sha256', $payload, $webhookKey, true));

        // Validate the signature
        if (! hash_equals($expectedSignature, $xeroSignature)) {
            Log::warning('Xero webhook: signature mismatch', [
                'expected' => $expectedSignature,
                'provided' => $xeroSignature,
            ]);

            return response('Unauthorized', Response::HTTP_UNAUTHORIZED);
        }

        // Proceed to the next middleware or controller if validation passes
        return $next($request);
    }
}
