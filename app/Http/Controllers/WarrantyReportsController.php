<?php

namespace App\Http\Controllers;

use App\Exports\WarrantiesExport;
use App\Models\Warranty;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class WarrantyReportsController extends Controller
{
    public function __invoke()
    {
        $this->authorize('viewAny', Warranty::class);

        ini_set('memory_limit', '512M');

        return (new WarrantiesExport)
            ->showAccount(! Auth::user()->account_id)
            ->filters(Request::only('search', 'trashed', 'start', 'end'))
            ->download('warranties.xlsx');
    }
}
