<?php

namespace App\Http\Controllers;

use App\Actions\GenerateSaleDocument;
use App\Actions\SetupBillingRequest;
use App\Models\Sale;
use Illuminate\Support\Facades\Response;

class DocumentsController extends Controller
{
    public function __invoke(
        Sale $sale,
        SetupBillingRequest $setupBillingRequest,
        GenerateSaleDocument $generateSaleDocument
    ) {
        $setupBillingRequest->execute($sale);

        $preview = (bool) request('preview');

        $document = $generateSaleDocument->generate($sale, $preview);

        if ($preview) {
            return $document;
        }

        return Response::make($document, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => "inline; filename=\"warranty_{$sale->id}.pdf\"",
        ]);
    }
}
