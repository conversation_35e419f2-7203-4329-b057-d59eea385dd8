<?php

namespace App\Http\Controllers\CustomerPortal;

use App\Http\Controllers\Controller;
use App\Models\Claim;

class ClaimsController extends Controller
{
    public function __invoke()
    {
        return view('customer-portal.claims', [
            'claims' => Claim::query()
                ->withWhereHas('warranty.sale', fn ($query) => $query->where('customer_id', auth()->id()))
                ->latest()
                ->get(),

            // TODO add these to a policy to guard the /create route also

            'hasLiveWarranty' => auth()->user()->sales()->whereHas('warranty', fn ($q) => $q->live())->exists(),
            'hasInProgressClaim' => Claim::query()
                ->withWhereHas('warranty.sale', fn ($query) => $query->where('customer_id', auth()->id()))
                ->whereDoesntHave('authorisations')
                ->whereDoesntHave('rejection')
                ->exists(),

            // temp always show new claim screen
            'hasInProgressClaim' => false,
        ]);
    }
}
