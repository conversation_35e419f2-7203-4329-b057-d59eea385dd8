<?php

namespace App\Http\Controllers\CustomerPortal;

use App\Http\Controllers\Controller;

class WelcomeController extends Controller
{
    public function __invoke()
    {
        return view('customer-portal.index', [
            'customer' => auth()
                ->user()
                ->load([
                    'sales' => fn ($query) => $query
                        ->with([
                            'account',
                            'vehicle',
                            'warranty.product',
                            'breakdownPlan.product',
                            'servicePlan.product',
                        ])
                        ->latest(),
                ]),
        ]);
    }
}
