<?php

namespace App\Http\Controllers\CustomerPortal;

use App\Http\Controllers\Controller;
use App\Services\MagicLinks\MagicLink;
use Illuminate\Http\Request;

class MagicLinkController extends Controller
{
    public function __invoke(Request $request, string $token, MagicLink $magicLink)
    {
        if (! $request->hasValidSignature()) {
            abort(403);
        }

        $url = $magicLink->decode($token);

        return redirect($url);
    }
}
