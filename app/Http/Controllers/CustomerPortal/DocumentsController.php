<?php

namespace App\Http\Controllers\CustomerPortal;

use App\Http\Controllers\Controller;

class DocumentsController extends Controller
{
    public function __invoke()
    {
        return view('customer-portal.documents', [
            'customer' => auth()->user()
                ->load([
                    'sales' => fn ($query) => $query
                        ->with([
                            'vehicle',
                            'warranty',
                            'breakdownPlan',
                            'servicePlan',
                        ])
                        ->latest(),
                ]),
        ]);
    }
}
