<?php

namespace App\Http\Controllers;

use App\Exports\BreakdownPlansExport;
use App\Models\BreakdownPlan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class BreakdownPlanReportsController extends Controller
{
    public function __invoke()
    {
        $this->authorize('viewAny', BreakdownPlan::class);

        return (new BreakdownPlansExport)
            ->showAccount(! Auth::user()->account_id)
            ->filters(Request::only('search', 'trashed', 'start', 'end'))
            ->download('breakdown-plans.xlsx');
    }
}
