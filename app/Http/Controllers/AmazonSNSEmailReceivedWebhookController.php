<?php

namespace App\Http\Controllers;

use App\Actions\ProcessEmailAttachments;
use App\Jobs\ProcessClaimEstimate;
use App\Models\InboundEmail;

class AmazonSNSEmailReceivedWebhookController extends Controller
{
    public function __construct(
        private readonly ProcessEmailAttachments $processEmailAttachments
    ) {}

    public function __invoke()
    {
        if (request()->json('Type') === 'SubscriptionConfirmation') {
            $this->confirmSubscription();
        } elseif (request()->json('Type') === 'Notification') {
            $this->processNotification();
        }

        return response()->noContent();
    }

    private function confirmSubscription()
    {
        file_get_contents(request()->json('SubscribeURL'));
    }

    private function processNotification()
    {
        $message = json_decode(request()->json('Message'));

        if ($message->notificationType !== 'Received') {
            return;
        }

        $inboundEmail = InboundEmail::firstOrCreate([
            'message_id' => $message->mail->messageId,
        ], [
            'subject' => $message->mail->commonHeaders->subject,
            'timestamp' => $message->mail->timestamp,
            'source' => $message->mail->source,
            'destinations' => $message->mail->destination,
            's3_bucket_name' => $message->receipt->action->bucketName,
            's3_object_key' => $message->receipt->action->objectKey,
        ]);

        if ($inboundEmail->wasRecentlyCreated) {
            $this->processEmailAttachments->execute($inboundEmail);
        }

        ProcessClaimEstimate::dispatch($inboundEmail);
    }
}
