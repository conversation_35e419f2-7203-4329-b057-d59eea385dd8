<?php

namespace App\Http\Controllers;

use App\Filament\Pages\InstallBrowserPlugin;
use Illuminate\Http\RedirectResponse;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class DownloadBrowserPluginController extends Controller
{
    public function __invoke(): BinaryFileResponse|RedirectResponse
    {
        // Path to the plugin file
        $path = resource_path('browser-plugin/protegoautocare-browser-plugin.zip');

        // If file doesn't exist, redirect to instructions page with error
        if (! file_exists($path)) {
            return redirect()->to(InstallBrowserPlugin::getUrl())
                ->with('error', 'Plugin download is not available at this time.');
        }

        return response()->download($path);
    }
}
