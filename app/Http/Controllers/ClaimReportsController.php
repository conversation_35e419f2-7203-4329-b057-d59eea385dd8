<?php

namespace App\Http\Controllers;

use App\Exports\ClaimsExport;
use App\Models\Claim;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class ClaimReportsController extends Controller
{
    public function __invoke()
    {
        $this->authorize('viewAny', Claim::class);

        return (new ClaimsExport)
            ->showAccount(! Auth::user()->account_id)
            ->filters(Request::only('search', 'trashed', 'start', 'end'))
            ->download('warranty-claims.xlsx');
    }
}
