<?php

namespace App\Http\Controllers;

use App\Jobs\SyncPayLaterAgreementStatus;
use App\Models\PayLaterAgreement;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

class PaymentAssistWebhookController extends Controller
{
    public function __invoke()
    {
        Storage::disk('local')->put('payment-assist-webhooks/'.Carbon::now()->toDateTimeString().'.json', request()->getContent());

        request()->validate([
            'event_type' => 'required|string',
            'event_data' => 'required|array',
            'event_time' => 'required|string',
            'signature' => 'required|string',
        ]);

        [$entity, $event] = explode('.', request()->json('event_type'));
        match ($entity) {
            'application' => $this->processApplication(),
        };

        return response()->noContent(200);
    }

    private function processApplication()
    {
        $token = request()->json('event_data.token');

        $payLaterAgreement = PayLaterAgreement::query()
            ->where('token', $token)
            ->first();

        if ($payLaterAgreement) {
            SyncPayLaterAgreementStatus::dispatch($payLaterAgreement);
        }
    }
}
