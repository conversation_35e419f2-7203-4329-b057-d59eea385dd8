<?php

namespace App\Http\Controllers;

use App\Jobs\SyncPaymentFromProcessor;
use App\Models\BillingRequest;
use App\Models\Payment;
use App\Services\Payments\DirectDebit\AccessPaysuiteDirectDebitPaymentProcessor;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

class AccessPaysuiteWebhookController extends Controller
{
    public function __construct(public AccessPaysuiteDirectDebitPaymentProcessor $paymentProcessor) {}

    public function __invoke(string $entity)
    {
        Storage::disk('local')->put("access-paysuite-webhooks/{$entity}-".Carbon::now()->toDateTimeString().'.json', request()->getContent());

        match ($entity) {
            'contract' => $this->processContract(),
            'payment' => $this->processPayment(),
        };

        return response()->noContent(200);
    }

    private function processContract()
    {
        BillingRequest::query()
            ->where('provider', 'access_paysuite')
            ->where('mandate_id', request()->json('Id'))
            ->first()?->update([
                'status' => strtolower(request()->json('NewStatus')),
            ]);
    }

    private function processPayment()
    {
        $payment = Payment::query()
            ->where('provider', 'access_paysuite')
            ->where('processor_payment_id', request()->json('Id'))
            ->first();

        if ($payment) {
            SyncPaymentFromProcessor::dispatch($payment);
        }
    }
}
