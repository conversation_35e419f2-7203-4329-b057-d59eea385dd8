<?php

namespace App\Http\Controllers;

use App\Actions\PullContactFromAccountingSoftware;
use App\Actions\PullInvoiceFromAccountingSoftware;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class XeroWebhookController extends Controller
{
    public function __invoke(Request $request)
    {
        $request->validate([
            'events.*.resourceId' => 'required|string',
            'events.*.eventCategory' => 'required|string',
            'events.*.eventType' => 'required|string',
        ]);

        foreach ($request->input('events') as $event) {
            $method = Str::camel(sprintf(
                '%s %s',
                strtolower($event['eventType']),
                strtolower($event['eventCategory']),
            ));

            if (method_exists($this, $method)) {
                try {
                    $this->{$method}($event['resourceId']);
                } catch (\Throwable $e) {
                    report($e);
                }
            }
        }

        return response()->noContent();
    }

    protected function updateContact($resourceId)
    {
        app(PullContactFromAccountingSoftware::class)->onQueue()->execute($resourceId);
    }

    protected function updateInvoice($resourceId)
    {
        app(PullInvoiceFromAccountingSoftware::class)->onQueue()->execute($resourceId);
    }
}
