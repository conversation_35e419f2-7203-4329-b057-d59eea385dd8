<?php

namespace App\Listeners;

use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Password;

class SendUserInviteNotification
{
    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        if (! $event->user->getAuthPassword()) {
            Password::broker('invite')
                ->sendResetLink(
                    ['email' => $event->user->getEmailForVerification()],
                    function (User $user, $token) {
                        $user->sendInviteNotification($token);
                    }
                );
        }
    }
}
