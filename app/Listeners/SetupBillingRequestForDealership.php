<?php

namespace App\Listeners;

use App\Actions\SetupBillingRequest;
use App\Events\DealershipCreated;
use Illuminate\Contracts\Queue\ShouldQueue;

class SetupBillingRequestForDealership implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct(private readonly SetupBillingRequest $setupBillingRequest) {}

    /**
     * Handle the event.
     */
    public function handle(DealershipCreated $event): void
    {
        $this->setupBillingRequest->execute($event->dealership, sendNotification: true);
    }
}
