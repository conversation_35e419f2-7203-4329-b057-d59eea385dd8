<?php

namespace App\Listeners;

use App\Events\ClaimEstimateCreated;
use App\Notifications\Claims\CustomerClaimEstimateReceived;

class SendEstimateReceivedNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ClaimEstimateCreated $event): void
    {
        // $event->claimEstimate->claim->warranty->sale->customer->notify(new CustomerClaimEstimateReceived($event->claimEstimate));
    }
}
