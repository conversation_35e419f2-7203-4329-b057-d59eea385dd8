<?php

namespace App\Listeners;

use App\Actions\CreateNewDealershipContactInAccountingSoftware;
use App\Events\DealershipCreated;
use App\Events\DealershipUpdated;
use App\Models\Dealership;
use Illuminate\Contracts\Queue\ShouldQueue;

class CreateOrUpdateDealershipContactInXero implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct(private readonly CreateNewDealershipContactInAccountingSoftware $createNewDealershipContactInAccountingSoftware) {}

    /**
     * Handle the event.
     */
    public function handle(DealershipCreated|DealershipUpdated $event): void
    {
        Dealership::withoutEvents(function () use ($event) {
            $this->createNewDealershipContactInAccountingSoftware->execute($event->dealership);
        });
    }
}
