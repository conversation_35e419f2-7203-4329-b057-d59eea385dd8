<?php

namespace App\Listeners;

use App\Actions\SetupBillingRequest;
use App\Events\SaleConfirmed;
use Illuminate\Contracts\Queue\ShouldQueue;

readonly class SetupCustomerForDirectDebitBilling implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(
        private SetupBillingRequest $setupBillingRequest,
    ) {}

    /**
     * Handle the event.
     */
    public function handle(SaleConfirmed $event): void
    {
        if ($event->sale->isRecurring()) {
            // This must be run before the customer paperwork PDF is generated
            // so that the GoCardless QR code link is included in the PDF
            $this->setupBillingRequest->execute($event->sale, true);
        }
    }
}
