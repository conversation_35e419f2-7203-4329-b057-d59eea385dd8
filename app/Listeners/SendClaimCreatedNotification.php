<?php

namespace App\Listeners;

use App\Events\ClaimCreated;
use App\Notifications\Claims\CustomerClaimStarted;

class SendClaimCreatedNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ClaimCreated $event): void
    {
        $event->claim->warranty->sale->customer->notify(new CustomerClaimStarted($event->claim));
    }
}
