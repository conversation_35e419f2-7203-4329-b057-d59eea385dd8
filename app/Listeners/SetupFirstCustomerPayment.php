<?php

namespace App\Listeners;

use App\Actions\CreateCustomerPayment;
use App\Events\BillingRequestFulfilled;

class SetupFirstCustomerPayment
{
    /**
     * Create the event listener.
     */
    public function __construct(
        private readonly CreateCustomerPayment $createCustomerPayment,
    ) {}

    /**
     * Handle the event.
     */
    public function handle(BillingRequestFulfilled $event): void
    {
        if (! $event->billingRequest->sale) {
            // This is a new mandate for a dealership so no payment needed
            return;
        }

        if ($event->billingRequest->sale->initialCustomerInstallmentAmount() > 0) {
            // If payment was not taken by the dealer, set up the first payment
            $this->createCustomerPayment->execute(
                sale: $event->billingRequest->sale,
                chargeDate: $event->billingRequest->sale->start_date,
                isFirstPayment: true
            );
        }

        // Set up the second payment
        $this->createCustomerPayment->execute(
            sale: $event->billingRequest->sale,
            chargeDate: $event->billingRequest->sale->start_date->copy()->addMonth(),
            isFirstPayment: false
        );
    }
}
