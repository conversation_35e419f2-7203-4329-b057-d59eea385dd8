<?php

namespace App\Workflows\WorkflowFilters;

use App\Models\SalesLead;
use Illuminate\Database\Eloquent\Builder;

interface WorkflowFilterInterface
{
    public function formFields(): array;

    public function getValueOptions(): array;

    public function getOperatorEnumClass():string;

    public function execute(Builder $query, array $condition, string $logic): void;

    public function label(array $values): string;

}
