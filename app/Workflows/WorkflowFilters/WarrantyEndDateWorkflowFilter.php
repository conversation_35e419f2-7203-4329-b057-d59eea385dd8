<?php

namespace App\Workflows\WorkflowFilters;

use App\Workflows\Enums\WorkflowDateOperators;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class WarrantyEndDateWorkflowFilter implements WorkflowFilterInterface
{
    public function formFields(): array
    {
        // TODO: Implement formFields() method.
    }

    public function getOperatorEnumClass(): string
    {
        return WorkflowDateOperators::class;
    }

    public function execute(Builder $query, array $condition, string $logic): void
    {
        $query->has(relation: 'warranty', boolean: $logic, callback: fn ($q) => $q->whereDate('end_date', $condition['operator'], Carbon::today()->addDays((int) $condition['value'])));
    }

    public function getValueOptions(): array
    {
        return collect(range(-60, 60))
            ->mapWithKeys(function ($value) {
                if ($value === 0) {
                    $label = 'same day';
                } else {
                    $days = abs($value);
                    $label = sprintf(
                        '%s %s %s',
                        $days,
                        Str::plural('day', $days),
                        $value < 0 ? 'previous' : 'from now');
                }

                return [$value => $label];
            })->all();
    }

    public function label(array $values): string
    {
        return sprintf('Warranty end date %s %d %s %s',
            $values['operator'],
            abs($values['value']),
            Str::plural('day', $values['value']),
            $values['value'] < 0 ? 'ago' : 'from now',
        );
    }
}
