<?php

namespace App\Workflows\WorkflowFilters;

use App\Workflows\Enums\WorkflowBooleanOperators;
use Illuminate\Database\Eloquent\Builder;

class BreakdownPlanExistsWorkflowFilter implements WorkflowFilterInterface
{
    public function formFields(): array
    {
        // TODO: Implement formFields() method.
    }

    public function getOperatorEnumClass(): string
    {
        return WorkflowBooleanOperators::class;
    }

    public function execute(Builder $query, array $condition, string $logic): void
    {
        $query->has(relation: 'breakdownPlan', operator: $condition['operator'] === 'true' ? '>=' : '<', boolean: $logic);
    }

    public function getValueOptions(): array
    {
        return [];
    }

    public function label(array $values): string
    {
        return $values['operator'] === 'true' ? 'Has breakdown cover' : 'No breakdown cover';
    }
}
