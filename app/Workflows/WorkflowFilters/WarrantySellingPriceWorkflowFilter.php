<?php

namespace App\Workflows\WorkflowFilters;

use App\Workflows\Enums\WorkflowDateOperators;
use Illuminate\Database\Eloquent\Builder;

class WarrantySellingPriceWorkflowFilter implements WorkflowFilterInterface
{
    public function formFields(): array
    {
        // TODO: Implement formFields() method.
    }

    public function getOperatorEnumClass(): string
    {
        return WorkflowDateOperators::class;
    }

    public function execute(Builder $query, array $condition, string $logic): void
    {
        $query->has(relation: 'warranty', boolean: $logic, callback: fn ($q) => $q->where('selling_price', $condition['operator'], $condition['value']));
    }

    public function getValueOptions(): array
    {
        return range(0, 1200, 50);
    }

    public function label(array $values): string
    {
        return sprintf('Warranty selling price %s %s', $values['operator'], $values['value']);
    }
}
