<?php

namespace App\Workflows\WorkflowActions;

use App\Models\Offer;
use App\Models\SalesLead;
use Filament\Forms\Components\Select;

class SendOfferWorkflowAction implements WorkflowActionInterface
{
    public function formFields(): array
    {
        return [
            Select::make('offer_id')
                ->required()
                ->label('Select offer')
                ->options(Offer::pluck('name', 'id')),
        ];
    }

    public function execute(SalesLead $salesLead, array $values): void
    {
        $salesLead->offers()->create([
            'offer_id' => $values['offer_id'],
        ])->send();
    }

    public function label(array $values): string
    {
        return sprintf('Send offer: "%s"', Offer::find($values['offer_id'])->name);
    }
}
