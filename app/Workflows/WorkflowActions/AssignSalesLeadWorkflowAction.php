<?php

namespace App\Workflows\WorkflowActions;

use App\Enums\CallOutcome;
use App\Models\SalesLead;
use App\Models\User;
use Filament\Forms\Components\Select;
use Illuminate\Support\Str;

class AssignSalesLeadWorkflowAction implements WorkflowActionInterface
{
    public function formFields(): array
    {
        return [
            Select::make('assign_to')
                ->required()
                ->options(
                    User::query()->permission('sales-leads.call')->get([
                        'id',
                        'first_name',
                        'last_name',
                    ])->mapWithKeys(fn (User $user) => [$user->id => $user->name])->all()
                ),
            Select::make('callback_days')
                ->required()
                ->label('Set callback in days')
                ->options(fn () => collect(range(0, 14))
                        ->mapWithKeys(fn (int $days) => [$days => sprintf('%s %s', $days, Str::plural('day', $days))])->all()
                ),
        ];
    }

    public function execute(SalesLead $salesLead, array $values): void
    {
        if ($salesLead->assigned_to_user_id) {
            return;
        }

        $salesLead->update([
            'assigned_to_user_id' => $values['assign_to'],
        ]);

        $salesLead->callOutcomes()->create([
            'outcome' => CallOutcome::AUTO_ASSIGNED->value,
            'callback_date' => today()->addDays((int) $values['callback_days'])->setHour(9),
        ]);
    }

    public function label(array $values): string
    {
        return sprintf('Assign to %s, call in %d days', User::find($values['assign_to'])->name, $values['callback_days']);
    }
}
