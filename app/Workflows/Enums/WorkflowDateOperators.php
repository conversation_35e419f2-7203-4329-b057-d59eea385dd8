<?php

namespace App\Workflows\Enums;

enum WorkflowDateOperators: string implements WorkflowOperators
{
    case BEFORE = '<';
    case AFTER = '>';
    case EQUALS = '=';

    public function label(): string
    {
        return match ($this) {
            self::BEFORE => 'before',
            self::AFTER => 'after',
            self::EQUALS => 'is',
        };
    }

    public static function toSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn (self $case) => [$case->value => $case->label()])->all();
    }
}
