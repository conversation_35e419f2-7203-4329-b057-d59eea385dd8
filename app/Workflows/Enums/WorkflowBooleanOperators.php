<?php

namespace App\Workflows\Enums;

enum WorkflowBooleanOperators: string implements WorkflowOperators
{
    case TRUE = 'true';
    case FALSE = 'false';

    public function label(): string
    {
        return sprintf('is %s', $this->value);
    }

    public static function toSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn (self $case) => [$case->value => $case->label()])->all();
    }
}
