<?php

namespace App\Workflows\Enums;

enum WorkflowNumericOperators: string implements WorkflowOperators
{
    case LESS_THAN = '<';
    case GREATER_THAN = '>';
    case EQUALS = '=';

    public function label(): string
    {
        return $this->value;
    }

    public static function toSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn (self $case) => [$case->value => $case->label()])->all();
    }
}
