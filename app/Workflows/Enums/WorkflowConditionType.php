<?php

namespace App\Workflows\Enums;

use App\Workflows\WorkflowFilters\BreakdownPlanExistsWorkflowFilter;
use App\Workflows\WorkflowFilters\ServicePlanExistsWorkflowFilter;
use App\Workflows\WorkflowFilters\StartDateWorkflowFilter;
use App\Workflows\WorkflowFilters\WarrantyEndDateWorkflowFilter;
use App\Workflows\WorkflowFilters\WarrantySellingPriceWorkflowFilter;
use App\Workflows\WorkflowFilters\WorkflowFilterInterface;

enum WorkflowConditionType: string
{
    case BREAKDOWN_PLAN = 'breakdown';
    case SERVICE_PLAN = 'service';
    case START_DATE = 'start_date';
    case WARRANTY_END_DATE = 'warranty.end_date';
    case WARRANTY_SELLING_PRICE = 'warranty.selling_price';

    public function label(): string
    {
        return match ($this) {
            self::BREAKDOWN_PLAN => 'Has breakdown cover',
            self::SERVICE_PLAN => 'Has service plan',
            self::START_DATE => 'Start date',
            self::WARRANTY_END_DATE => 'Warranty end date',
            self::WARRANTY_SELLING_PRICE => 'Warranty selling price',
        };
    }

    public static function toSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn (self $case) => [$case->value => $case->label()])->all();
    }

    public function getWorkflowFilter(): WorkflowFilterInterface
    {
        $className = match ($this) {
            self::BREAKDOWN_PLAN => BreakdownPlanExistsWorkflowFilter::class,
            self::SERVICE_PLAN => ServicePlanExistsWorkflowFilter::class,
            self::START_DATE => StartDateWorkflowFilter::class,
            self::WARRANTY_END_DATE => WarrantyEndDateWorkflowFilter::class,
            self::WARRANTY_SELLING_PRICE => WarrantySellingPriceWorkflowFilter::class,
        };

        return app($className);
    }

    public function getOperators(): string
    {
        return $this->getWorkflowFilter()->getOperatorEnumClass();
    }

    public function getValueOptions(): array
    {
        return $this->getWorkflowFilter()->getValueOptions();
    }
}
