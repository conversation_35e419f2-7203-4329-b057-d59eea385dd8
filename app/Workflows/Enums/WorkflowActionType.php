<?php

namespace App\Workflows\Enums;

use App\Workflows\WorkflowActions\AssignSalesLeadWorkflowAction;
use App\Workflows\WorkflowActions\SendOfferWorkflowAction;
use App\Workflows\WorkflowActions\WorkflowActionInterface;

enum WorkflowActionType: string
{
    case SEND_OFFER = 'send_offer';
    case SET_CALLBACK = 'set_callback';

    public function label(): string
    {
        return match ($this) {
            self::SEND_OFFER => 'Send offer',
            self::SET_CALLBACK => 'Set callback',
        };
    }

    public static function toSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn (self $case) => [$case->value => $case->label()])->all();
    }

    public function getWorkflowAction(): WorkflowActionInterface
    {
        $className = match ($this) {
            self::SEND_OFFER => SendOfferWorkflowAction::class,
            self::SET_CALLBACK => AssignSalesLeadWorkflowAction::class,
        };

        return app($className);
    }
}
