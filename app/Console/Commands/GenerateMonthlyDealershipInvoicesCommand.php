<?php

namespace App\Console\Commands;

use App\Actions\InvoiceDealershipAndCreateGoCardlessPaymentForMonth;
use App\Models\Dealership;
use Carbon\Carbon;
use Illuminate\Console\Command;

class GenerateMonthlyDealershipInvoicesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'accounting:generate-monthly-dealership-invoices {start?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate monthly invoices for all dealerships';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(InvoiceDealershipAndCreateGoCardlessPaymentForMonth $invoiceDealershipAndCreateGoCardlessPaymentForMonth): int
    {
        $start = $this->argument('start')
            ? Carbon::parse($this->argument('start'))->startOfMonth()
            : Carbon::today()->startOfMonth()->subMonth();

        Dealership::query()
            ->withWhereHas('accountingContact')
            ->each(function (Dealership $dealership) use ($invoiceDealershipAndCreateGoCardlessPaymentForMonth, $start) {
                $this->info("Generate invoice for {$dealership->name}");
                try {
                    $invoiceDealershipAndCreateGoCardlessPaymentForMonth->execute($dealership, $start);
                } catch (\Exception $e) {
                    $this->error("Failed to generate invoice for {$dealership->name}: {$e->getMessage()}");
                }
            });

        return 0;
    }
}
