<?php

namespace App\Console\Commands;

use App\Models\Sale;
use Illuminate\Console\Command;

class SendCustomerPaymentMethodSetupReminderNotifications extends Command
{
    protected $signature = 'app:send_customer_payment_method_reminders';

    public function handle(): int
    {
        $this->sendDirectDebitReminders();
        $this->sendPayLaterReminders();

        return 0;
    }

    protected function sendDirectDebitReminders(): void
    {
        $salesQuery = Sale::query()
            ->with('customer')
            ->needsDirectDebit();

        $this->info("Found {$salesQuery->count()} sales that require Direct Debit mandate setup");

        $this->sendNotifications($salesQuery);
    }

    protected function sendPayLaterReminders(): void
    {
        $salesQuery = Sale::query()
            ->with('customer')
            ->withIncompletePayLaterAgreements();

        $this->info("Found {$salesQuery->count()} sales that require Pay Later agreement setup");

        $this->sendNotifications($salesQuery);
    }

    protected function sendNotifications($salesQuery): void
    {
        $salesQuery->each(function (Sale $sale) {
            $this->info("Sending reminder for sale ID: {$sale->id}");
            $sale->sendPaymentSetupNotification(isReminder: true);
        });
    }
}
