<?php

namespace App\Console\Commands;

use App\Actions\Voip\ProcessWebsocketData;
use App\Services\Voip\Voip3cxProvider;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Ratchet\Client\Connector;
use Ratchet\Client\WebSocket;
use React\EventLoop\Loop;
use React\Socket\Connector as SocketConnector;

class WebSocketListener extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'websocket:listen';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Listen to 3CX WebSocket for real-time call control events';

    /**
     * Connection attempts counter
     */
    protected $connectionAttempts = 0;

    private ?\React\EventLoop\LoopInterface $loop;

    public function __construct(
        private readonly Voip3cxProvider $voip3cxProvider,
        private readonly ProcessWebsocketData $processWebsocketData
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting WebSocket listener...');

        // Create event loop
        $this->loop = Loop::get();

        // Add memory usage monitoring
        $this->monitorMemory();

        // Connect to WebSocket server
        $this->connect();

        // Run the loop
        $this->loop->run();

        return 0;
    }

    /**
     * Set up memory monitoring.
     *
     * @param  \React\EventLoop\LoopInterface  $loop
     * @return void
     */
    protected function monitorMemory()
    {
        $memoryLimit = config('websocket.memory_limit', 100) * 1024 * 1024; // MB to bytes
        $lastCheck = time();

        $this->loop->addPeriodicTimer(60, function () use (&$lastCheck, $memoryLimit) {
            $lastCheck = time();
            $memoryUsage = memory_get_usage(true);

            $this->info('Memory usage: '.round($memoryUsage / 1024 / 1024, 2).' MB');
            Log::info('WebSocket client memory usage: '.round($memoryUsage / 1024 / 1024, 2).' MB');

            // If memory usage exceeds the limit, exit gracefully
            if ($memoryUsage > $memoryLimit) {
                $this->warn('Memory limit exceeded. Shutting down gracefully...');
                Log::warning('WebSocket client memory limit exceeded. Shutting down gracefully...');
                exit(0);
            }
        });
    }

    /**
     * Set up keep-alive to prevent timeout
     *
     * @return void
     */
    protected function setupKeepAlive(WebSocket $conn)
    {
        $this->loop->addPeriodicTimer(55, function () use ($conn) {
            $conn->send('keep-alive');
            Log::info('Sent keep-alive message');
        });
    }

    /**
     * Connect to WebSocket server.
     *
     * @param  \React\EventLoop\LoopInterface  $loop
     * @return void
     */
    protected function connect()
    {
        $this->connectionAttempts++;
        $wsUrl = config('websocket.url', 'wss://minstercarco.my3cx.uk/callcontrol/ws');
        $reconnectInterval = config('websocket.reconnect_interval', 5);

        $this->info("Attempting to connect to WebSocket (attempt #{$this->connectionAttempts}): {$wsUrl}");
        Log::info("Attempting to connect to WebSocket (attempt #{$this->connectionAttempts}): {$wsUrl}");

        $reactConnector = new SocketConnector($this->loop, [
            'timeout' => 10,
            'tls' => [
                'verify_peer' => config('websocket.verify_ssl', false),
                'verify_peer_name' => config('websocket.verify_ssl', false),
            ],
        ]);

        $connector = new Connector($this->loop, $reactConnector);

        // Optional headers for authentication
        $headers = array_filter([
            'Authorization' => sprintf('Bearer %s', $this->voip3cxProvider->getToken()),
        ]);

        $connector($wsUrl, [], $headers)->then(
            function (WebSocket $conn) {
                $this->connectionAttempts = 0;
                $this->info('WebSocket connection established');
                Log::info('WebSocket connection established');

                // Set up keep-alive to prevent timeout
                $this->setupKeepAlive($conn);

                // Handle incoming messages
                $conn->on('message', function ($msg) {
                    try {
                        // Parse message
                        $data = json_decode($msg, true);

                        if (json_last_error() !== JSON_ERROR_NONE) {
                            Log::error('Failed to parse WebSocket message as JSON: '.json_last_error_msg());

                            return;
                        }

                        // Log the received message for debugging
                        Log::debug('WebSocket message received', ['data' => $data]);
                        $this->info('WebSocket message received'.$msg);

                        $this->processWebsocketData->execute($data);
                    } catch (\Exception $e) {
                        Log::error('Error processing WebSocket message: '.$e->getMessage());
                    }
                });

                // Handle connection close
                $conn->on('close', function ($code = null, $reason = null) {
                    $this->warn("WebSocket connection closed: {$code} - {$reason}");
                    Log::warning("WebSocket connection closed: {$code} - {$reason}");

                    // Reconnect after a delay
                    $reconnectInterval = config('websocket.reconnect_interval', 5);
                    $delay = min($this->connectionAttempts * $reconnectInterval, 60);
                    $this->info("Reconnecting in {$delay} seconds...");

                    $this->loop->addTimer($delay, function () {
                        $this->connect($this->loop);
                    });
                });

                // Handle errors
                $conn->on('error', function (\Exception $e) {
                    $this->error('WebSocket error: '.$e->getMessage());
                    Log::error('WebSocket error: '.$e->getMessage());
                    // The close event will also be fired after an error
                });
            },
            function (\Exception $e) use ($reconnectInterval) {
                $this->error('Could not connect to WebSocket: '.$e->getMessage());
                Log::error('Could not connect to WebSocket: '.$e->getMessage());

                if (str($e->getMessage())->contains('401 Unauthorized')) {
                    // Clear token and retry immediately
                    $this->voip3cxProvider->authenticate();
                    $this->connect();

                    return;
                }

                // Reconnection with exponential backoff
                $delay = min(pow(1.5, $this->connectionAttempts - 1) * $reconnectInterval, 60);
                $this->info("Reconnecting in {$delay} seconds...");

                $this->loop->addTimer($delay, function () {
                    $this->connect();
                });
            }
        );
    }
}
