<?php

namespace App\Console\Commands;

use App\Models\Payment;
use App\Models\Sale;
use App\Notifications\UpcomingPaymentNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\Relation;

class SendPaymentUpcomingPaymentNotifications extends Command
{
    protected $signature = 'app:send_upcoming_payment_notifications';

    protected $description = 'Send payment due reminders to customers 3 days before payment is due';

    public function handle(): int
    {
        $date = Carbon::now()->addDays(3)->startOfDay();

        $payments = Payment::query()
            ->whereDate('charge_date', $date)
            ->whereNull('upcoming_payment_notification_sent_at')
            ->whereNotNull('processor_payment_id')
            ->where('payable_type', Relation::getMorphAlias(Sale::class))
            ->where('provider', 'access_paysuite')
            ->with('payable', fn (MorphTo $morphTo) => $morphTo->morphWith([
                Sale::class => ['customer'],
            ]));

        $this->info("Found {$payments->count()} un-notified payments due on {$date->format('Y-m-d')}");

        $payments->each(function (Payment $payment) {
            $this->info("Sending reminder for payment ID: {$payment->id}");
            $payment->payable->customer->notify(new UpcomingPaymentNotification($payment));
            $payment->update(['upcoming_payment_notification_sent_at' => now()]);
        });

        return 0;
    }
}
