<?php

namespace App\Console\Commands;

use App\Models\Sale;
use App\Notifications\DealerCustomerPaymentSetupOverdueNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class SendDealerPaymentMethodSetupReminderNotifications extends Command
{
    protected $signature = 'app:send_dealer_payment_method_reminders';

    public function handle(): int
    {
        $salesNeedingAction = Sale::query()
            ->where('start_date', '<=', today())
            ->needsDirectDebit()
            ->orWhere(fn ($q) => $q->withIncompletePayLaterAgreements())
            ->with('user')
            ->get()
            ->groupBy('sold_by_id');

        $salesNeedingAction->each(function (Collection $sales) {
            $sales->first()->user->notify(new DealerCustomerPaymentSetupOverdueNotification($sales));
        });

        return 0;
    }
}
