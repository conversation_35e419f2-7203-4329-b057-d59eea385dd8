<?php

namespace App\Console\Commands;

use App\Models\Customer;
use Illuminate\Console\Command;

class AnonymiseCustomers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:anonymise-customers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'For demoing purposes, this command anonymises all customers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (app()->isProduction()) {
            $this->error('This command can not be run in production');

            return 1;
        }

        Customer::each(function (Customer $customer) {
            $this->info("Anonymising {$customer->id}");
            $firstName = fake()->firstName();
            $lastName = fake()->lastName();
            $mailbox = strtolower(sprintf('%s.%s', $firstName, $lastName));
            $email = $mailbox.'@example.com';
            while (Customer::where('email', $email)->exists()) {
                $mailbox .= mt_rand(1, 999);
                $email = $mailbox.'@example.com';
            }
            $customer->update([
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $email,
                'address_1' => fake()->streetAddress(),
                'address_2' => fake()->secondaryAddress(),
                'city' => fake()->city(),
                'county' => fake()->city(),
                'postcode' => strtoupper(fake()->bothify('??## ???')),
            ]);
        });

        $this->info('Anonymised all customers');

        return 0;
    }
}
