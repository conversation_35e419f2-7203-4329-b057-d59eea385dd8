<?php

namespace App\Console\Commands;

use App\Actions\CreateXeroBankTransferForPayout;
use App\Models\Payout;
use Illuminate\Console\Command;

class CreateOutstandingBankTransfersInXero extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-outstanding-bank-transfers-in-xero';

    /**
     * Execute the console command.
     */
    public function handle(CreateXeroBankTransferForPayout $createXeroPayoutBankTransfer): void
    {
        Payout::query()
            ->orderBy('arrival_date')
            ->each(function (Payout $payout) use ($createXeroPayoutBankTransfer) {
                $this->info("Creating bank transfer for payout {$payout->id} for amount {$payout->amount}");
                $transfer = $createXeroPayoutBankTransfer->execute($payout);
                $this->info("Bank transfer created with ID {$transfer->id} for amount {$transfer->amount}");
                $this->info('---------------------------------------------');
            });
    }
}
