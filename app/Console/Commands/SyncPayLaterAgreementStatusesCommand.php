<?php

namespace App\Console\Commands;

use App\Jobs\SyncPayLaterAgreementStatus;
use App\Models\PayLaterAgreement;
use Illuminate\Console\Command;

class SyncPayLaterAgreementStatusesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-pay-later-agreement-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync any pending pay later agreement statuses';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        PayLaterAgreement::query()
            ->whereNotNull('token')
            ->whereNull('status')
            ->orWhere('status', 'pending')
            ->each(function (PayLaterAgreement $agreement) {
                $this->info(sprintf('Syncing agreement %s', $agreement->id));
                SyncPayLaterAgreementStatus::dispatch($agreement);
            });

        $this->info('Syncing complete');

        return 0;
    }
}
