<?php

namespace App\Console\Commands;

use App\Exports\BreakdownPlansExport;
use App\Mail\BreakdownPlanSalesReport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class SendDailyBreakdownPlanSalesReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'breakdown-plans:send-daily-report';

    protected BreakdownPlansExport $breakdownPlansExport;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(BreakdownPlansExport $breakdownPlansExport)
    {
        parent::__construct();
        $this->breakdownPlansExport = $breakdownPlansExport;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $recipients = config('mail.breakdown_plan_reports_email_address');
        if (! count($recipients)) {
            return 1;
        }

        Mail::to($recipients)->send(
            new BreakdownPlanSalesReport(
                $this->breakdownPlansExport
                    ->filters([
                        'cancelled' => false,
                        'expired' => false,
                    ])
                    ->raw(\Maatwebsite\Excel\Excel::CSV),
            )
        );

        $this->info('Breakdown plan sales report sent to: '.implode(', ', $recipients));

        return 0;
    }
}
