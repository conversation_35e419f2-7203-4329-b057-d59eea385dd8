<?php

namespace App\Console\Commands\AI;

use App\Actions\AI\CreateAndProcessAiRequest;
use App\Models\AiTask;
use App\Models\Claim;
use App\Services\AI\WarrantyClaimAssessorTask;
use Illuminate\Console\Command;

class AssessHistoricWarrantyClaims extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:access-historic-warranty-claims';

    /**
     * Execute the console command.
     */
    public function handle(CreateAndProcessAiRequest $createAndProcessAiRequest)
    {
        $aiTask = AiTask::query()->where('processor_class', WarrantyClaimAssessorTask::class)->first();

        // Process all claims that have been authorised or rejected, but have not yet been processed by the AI task
        Claim::query()
            ->where(fn ($q) => $q->has('authorisations')->orHas('rejection'))
            ->whereDoesntHave('aiRequests', fn ($q) => $q
                ->whereNotNull('response')
                ->whereHas('prompt', fn ($q) => $q->where('ai_task_id', $aiTask->id))
            )
            ->each(function (Claim $claim) use ($aiTask, $createAndProcessAiRequest) {
                $this->info("Processing claim {$claim->id}");
                $createAndProcessAiRequest->onQueue()->execute($aiTask, $claim);
            });
    }
}
