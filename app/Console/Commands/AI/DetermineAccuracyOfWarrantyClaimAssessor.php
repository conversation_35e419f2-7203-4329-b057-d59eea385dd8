<?php

namespace App\Console\Commands\AI;

use App\Actions\AI\CreateAndProcessAiRequest;
use App\Models\AiTask;
use App\Models\Claim;
use App\Services\AI\WarrantyClaimAssessorTask;
use Illuminate\Console\Command;

class DetermineAccuracyOfWarrantyClaimAssessor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:determine-accuracy-of-warranty-claim-assessor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected int $correct = 0;

    protected int $incorrect = 0;

    protected int $undetermined = 0;

    /**
     * Execute the console command.
     */
    public function handle(CreateAndProcessAiRequest $createAndProcessAiRequest)
    {
        $aiTask = AiTask::query()->where('processor_class', WarrantyClaimAssessorTask::class)->first();

        Claim::query()
            // only process claims that have approval reasons. Claims prior to this did not record reason
            ->where('id', '>=', 717)
            ->where(fn ($q) => $q->has('authorisations')->orHas('rejection'))
            ->with(['authorisations', 'rejection'])
            ->withWhereHas('aiRequests', fn ($q) => $q
                ->whereNotNull('response')
                ->whereHas('prompt', fn ($q) => $q->where('ai_task_id', $aiTask->id))
                ->doesntHave('score')
                ->limit(1)
                ->latest()
            )
            ->each(function (Claim $claim) {
                $this->info("Processing claim {$claim->id}");

                $aiRequest = $claim->aiRequests->first();
                $aiDecision = $aiRequest->response['decision'];

                if ($claim->rejection) {
                    $claimDecision = 'rejected';
                } elseif ($claim->authorisations->contains('reason', 'COVERED_BY_WARRANTY')) {
                    $claimDecision = 'approved';
                } else {
                    $claimDecision = 'goodwill';
                }

                $rating = match ($claimDecision) {
                    'approved' => $aiDecision === 'approved' ? 1 : -1,
                    'rejected' => $aiDecision === 'rejected' ? 1 : -1,
                    default => 0,
                };

                if ($claimDecision === 'goodwill') {
                    $this->undetermined++;
                } elseif ($rating === 1) {
                    $this->correct++;
                } else {
                    $this->incorrect++;
                }

                $aiRequest->score()->create([
                    'rating' => $rating,
                ]);
            });
    }
}
/**
 * Query to retrieve the stats from this command:
 * SELECT
 * count(*),
 * count(rating),
 * count(if(rating = 0, 1, NULL)),
 * count(if(rating = 1, 1, NULL)),
 * count(if(rating = -1, 1, NULL))
 * FROM ai_request_scores;
 *
 * Results 06/02/2025: 1257, 1257, 621, 431, 205
 * Percentages: 49.4% correct, 34.2% incorrect, 16.3% undetermined
 * Percentages of undetermined claims: 59% correct, 41% incorrect
 */
