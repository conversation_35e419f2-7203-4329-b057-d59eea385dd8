<?php

namespace App\Console\Commands;

use App\Models\Sale;
use App\Models\SalesLead;
use App\Models\Workflow;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RunWorkflows extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:run-workflows {--pretend}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Runs the lead generation workflows';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Workflow::query()
            ->where('is_active', true)
            ->with('actions')
            ->each(function (Workflow $workflow) {
                $this->info("Running workflow {$workflow->name}");
                $query = $workflow->getQuery();
                $count = $query->count();
                $this->info("Found {$count} sales to generate leads for");
                if ($this->option('pretend')) {
                    return;
                }

                try {
                    $workflow->run($query);
                    $this->info("Generated {$count} leads");
                } catch (\Throwable $th) {
                    $this->error($th->getMessage());
                    Log::error($th->getMessage());
                }
            });

    }
}
