<?php

namespace App\Console\Commands;

use App\Actions\UpdateInvoiceFromAccountingSoftware;
use App\Models\Invoice;
use Illuminate\Console\Command;

class SyncInvoiceStatusesWithAccountingSoftware extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'accounting:sync-invoice-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Invoice Statuses With Accounting Software';

    /**
     * Execute the console command.
     */
    public function handle(UpdateInvoiceFromAccountingSoftware $updateInvoiceFromAccountingSoftware): void
    {
        Invoice::query()
            ->whereNotNull('accounting_software_id')
//            ->whereNotIn('status', ['PAID', 'VOIDED', 'DELETED'])
            ->each(function (Invoice $invoice) use ($updateInvoiceFromAccountingSoftware) {
                $this->info(sprintf('Syncing invoice %s', $invoice->id));
                try {
                    $a = $updateInvoiceFromAccountingSoftware->execute($invoice);
                } catch (\Throwable $e) {
                    if (app()->isLocal()) {
                        throw $e;
                    }
                    $this->error(sprintf('Error syncing invoice %s: %s', $invoice->id, $e->getMessage()));
                }
            });
    }
}
