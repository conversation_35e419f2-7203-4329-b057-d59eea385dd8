<?php

namespace App\Console\Commands;

use App\Actions\CreateNewDealershipContactInAccountingSoftware;
use App\Models\Dealership;
use App\Services\Accounting\AccountingServiceException;
use Illuminate\Console\Command;

class GenerateContactsInAccountingSoftware extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-contacts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Used to populate the Xero Sandbox with contacts';

    /**
     * Execute the console command.
     */
    public function handle(CreateNewDealershipContactInAccountingSoftware $createNewDealershipContactInAccountingSoftware): void
    {
        if (app()->environment('production')) {
            $this->error('This command can not be run in production');

            return;
        }

        Dealership::withoutEvents(function () use ($createNewDealershipContactInAccountingSoftware) {
            Dealership::query()
                ->whereNull('accounting_contact_id')
                ->each(function (Dealership $dealership) use ($createNewDealershipContactInAccountingSoftware) {
                    $dealership->email = sprintf('<EMAIL>', str($dealership->name)->slug()->substr(0, 20));
                    $dealership->save();
                    $this->info("Generating contact for {$dealership->name}");
                    try {
                        $createNewDealershipContactInAccountingSoftware->execute($dealership);
                        $this->info("Generated contact {$dealership->accounting_contact_id} for {$dealership->name}");
                    } catch (AccountingServiceException $e) {
                        $this->error("Failed to generate contact for {$dealership->name}: {$e->getMessage()}");
                    }
                    $this->info('-----------------------------------');
                });
        });
    }
}
