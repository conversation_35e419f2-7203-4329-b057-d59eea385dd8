<?php

namespace App\Console\Commands;

use App\Actions\CreatePaymentInProcessor;
use App\Models\Payment;
use Illuminate\Console\Command;

class ProcessDraftPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:process-draft-payments';

    /**
     * Execute the console command.
     */
    public function handle(CreatePaymentInProcessor $createPaymentInProcessor): void
    {
        Payment::query()
            ->where('status', Payment::STATUS_DRAFT)
            ->whereNull('processor_payment_id')
            ->each(function (Payment $payment) use ($createPaymentInProcessor) {
                $this->info("Processing payment ID: {$payment->id}");
                try {
                    $createPaymentInProcessor->execute($payment);
                } catch (\Exception $e) {
                    $this->error("Failed to process payment ID: {$payment->id}");
                    $this->error($e->getMessage());
                }
                $this->info("Processed payment ID: {$payment->id}");
                $this->line('');
            });
    }
}
