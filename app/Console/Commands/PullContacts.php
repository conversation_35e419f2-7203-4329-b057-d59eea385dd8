<?php

namespace App\Console\Commands;

use App\Actions\PullAllContactsFromAccountingSoftware;
use Illuminate\Console\Command;

class PullContacts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'accounting:pull-contacts';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(PullAllContactsFromAccountingSoftware $pullContactsFromAccountingSoftware): int
    {
        $pullContactsFromAccountingSoftware
            ->onQueue()
            ->execute();

        return 0;
    }
}
