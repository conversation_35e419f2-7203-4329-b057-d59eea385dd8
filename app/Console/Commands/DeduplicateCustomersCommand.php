<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Sale;
use Illuminate\Console\Command;
use Illuminate\Console\ConfirmableTrait;
use Illuminate\Support\Facades\DB;

class DeduplicateCustomersCommand extends Command
{
    use ConfirmableTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:deduplicate-customers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Find duplicate customers and update sales to reference only the first customer';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Find duplicates by comparing all fields except timestamps and id
        $duplicates = Customer::select(
            'account_id', 'first_name', 'last_name', 'email', 'address_1', 'address_2',
            'city', 'county', 'postcode', 'phone',
            DB::raw('COUNT(*) as count'),
            DB::raw('MIN(id) as first_id')
        )
            ->groupBy('account_id', 'first_name', 'last_name', 'email', 'address_1', 'address_2', 'city', 'county', 'postcode', 'phone')
            ->having('count', '>', 1)
            ->get();

        $this->info("Found {$duplicates->count()} sets of duplicate customers");

        foreach ($duplicates as $duplicate) {
            // Get all customers with these exact details
            $customers = Customer::query()
                ->where('account_id', $duplicate->account_id)
                ->where('first_name', $duplicate->first_name)
                ->where('last_name', $duplicate->last_name)
                ->where('email', $duplicate->email)
                ->where('address_1', $duplicate->address_1)
                ->where('address_2', $duplicate->address_2)
                ->where('city', $duplicate->city)
                ->where('county', $duplicate->county)
                ->where('postcode', $duplicate->postcode)
                ->where('phone', $duplicate->phone)
                ->orderBy('id')
                ->get();

            $firstCustomer = $customers->first();
            $duplicateIds = $customers->slice(1)->pluck('id')->values();

            $this->info("Processing duplicate set: keeping {$firstCustomer->id} ({$firstCustomer->first_name} {$firstCustomer->last_name}) and updating sales for ".$duplicateIds->implode(', '));

            // Update sales to reference the first customer
            $salesUpdated = Sale::whereIn('customer_id', $duplicateIds)->update([
                'customer_id' => $firstCustomer->id,
            ]);

            $this->info("Updated {$salesUpdated} sales to reference customer {$firstCustomer->id}");

            // Delete duplicate customers
            //            Customer::whereIn('id', $duplicateIds)->delete();
            $this->info("Deleted {$duplicateIds->count()} duplicate customers");
            $this->info('-----------------------------------');
        }

        $this->info('Deduplication complete');

        return 0;
    }
}
