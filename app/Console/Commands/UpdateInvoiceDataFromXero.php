<?php

namespace App\Console\Commands;

use App\Actions\PullInvoiceFromAccountingSoftware;
use App\Models\Invoice;
use Illuminate\Console\Command;

class UpdateInvoiceDataFromXero extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'accounting:update-invoices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update invoice data from Xero';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(PullInvoiceFromAccountingSoftware $pullInvoiceFromAccountingSoftware): int
    {
        $invoices = Invoice::query()
            ->withoutGlobalScope('tenant')
            ->whereNotNull('accounting_software_id')
            ->get();

        foreach ($invoices as $invoice) {
            try {
                $this->info(sprintf('Updating invoice %s (%s)', $invoice->invoice_number, $invoice->accounting_software_id));
                $pullInvoiceFromAccountingSoftware->execute($invoice->accounting_software_id);
            } catch (\Throwable $e) {
                $this->error(sprintf(
                    'Failed to update invoice %s (%s): %s',
                    $invoice->name,
                    $invoice->accounting_software_id,
                    $e->getMessage()
                ));
            }
        }

        return 0;
    }
}
