<?php

namespace App\Console\Commands;

use App\Services\Payments\DirectDebit\AccessPaysuiteDirectDebitPaymentProcessor;
use Illuminate\Console\Command;

class SetupAccessPaysuiteWebhooks extends Command
{
    protected $entities = [
        'bulkPayment',
        'customer',
        'contract',
        'payment',
        'schedule',
    ];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setup-access-paysuite-webhooks {--clear}';

    /**
     * Execute the console command.
     */
    public function handle(AccessPaysuiteDirectDebitPaymentProcessor $accessPaysuite)
    {
        foreach ($this->entities as $entity) {
            $url = $accessPaysuite->getWebhookUrl($entity)['Message'];
            $this->info("Current webhook URL for {$entity}: {$url}");
        }

        foreach ($this->entities as $entity) {
            if ($this->option('clear')) {
                $accessPaysuite->deleteWebhookUrl($entity);
                $this->info("Cleared webhook URL for {$entity}");
            } else {
                $accessPaysuite->setWebhookUrl($entity, route('webhooks.access-paysuite', $entity));
            }

            $url = $accessPaysuite->getWebhookUrl($entity)['Message'];
            $this->info("New webhook URL for {$entity}: {$url}");
        }
    }
}
