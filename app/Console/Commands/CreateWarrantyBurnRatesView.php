<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CreateWarrantyBurnRatesView extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create_warranty_burn_rates_view';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $daysOnCover = 'GREATEST(0, DATEDIFF(LEAST(COALESCE(end_date, CURDATE()), CURDATE()), sales.start_date))';
        $totalDays = 'DATEDIFF(COALESCE(end_date, sales.start_date), sales.start_date)';
        $earnedRevenue = "IF(monthly_provision IS NULL, {$daysOnCover} / {$totalDays} * provision, {$daysOnCover} * monthly_provision * 12/365)";
        $claimsTotal = 'COALESCE((select SUM(authorised_net) from `claim_authorisations` where exists (select * from `claim_estimates` where `claim_authorisations`.`estimate_id` = `claim_estimates`.`id` and exists (select * from `claims` where `claim_estimates`.`claim_id` = `claims`.`id` and `warranty_id` = `warranties`.`id`))), 0)';

        $subquery = \App\Models\Warranty::query()
            ->select([
                'warranties.id',
                'warranties.account_id',
                'warranties.sale_id',
                'warranties.product_id',
                'sales.start_date',
                'warranties.end_date',
                'warranties.cancelled_at',
                'warranties.fund_type',
                'warranties.admin_fee',
                'warranties.provision',
                'warranties.selling_price',
                'warranties.monthly_admin_fee',
                'warranties.monthly_provision',
                'warranties.monthly_selling_price',
            ])
            ->join('sales', 'sales.id', '=', 'warranties.sale_id')
            ->withCount('claims')
            ->selectRaw("{$claimsTotal} AS claims_total_net")
            ->selectRaw("{$daysOnCover} AS days_on_cover")
            ->selectRaw("{$totalDays} AS total_days")
            ->selectRaw("{$earnedRevenue} AS earned_revenue")
            ->selectRaw("ROUND(COALESCE({$claimsTotal} / {$earnedRevenue}, 0) * 100) AS burn_rate");

        DB::statement("CREATE OR REPLACE VIEW warranty_burn_rates_view AS {$subquery->toRawSql()}");
    }
}
