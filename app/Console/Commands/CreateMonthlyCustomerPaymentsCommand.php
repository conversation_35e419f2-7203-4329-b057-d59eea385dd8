<?php

namespace App\Console\Commands;

use App\Actions\CreateCustomerPayment;
use App\Models\Sale;
use Carbon\CarbonImmutable;
use Illuminate\Console\Command;

class CreateMonthlyCustomerPaymentsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-monthly-customer-payments {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate monthly payments for all customers';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(CreateCustomerPayment $createCustomerPayment): int
    {
        $calendarMonthStart = $this->argument('date')
            ? CarbonImmutable::parse($this->argument('date'))->startOfMonth()
            : CarbonImmutable::now()->addMonth()->startOfMonth();

        $isDraft = (bool) $this->argument('date');

        Sale::query()
            ->whereDate('start_date', '<', $calendarMonthStart)
            ->activeRecurring()
            ->withWhereHas('billingRequest', fn ($q) => $q->active())
            ->whereDoesntHave('payments', fn ($q) => $q->whereBetween('period_start', [$calendarMonthStart, $calendarMonthStart->endOfMonth()]))
            ->each(function (Sale $sale) use ($createCustomerPayment, $calendarMonthStart, $isDraft) {
                $periodStart = $calendarMonthStart->setDay($sale->start_date->day);
                if (! $periodStart->isSameMonth($calendarMonthStart)) {
                    $periodStart = $calendarMonthStart->endOfMonth();
                }
                $this->info("Executing Generate payment for sale ID: {$sale->id}, period start: {$periodStart->toDateString()}");
                try {
                    $this->info("Creating payment for Sale ID: {$sale->id}");
                    $payment = $createCustomerPayment->execute(
                        sale: $sale,
                        chargeDate: $periodStart,
                        isDraft: $isDraft
                    );
                    $this->info("Created payment ID: {$payment->id}");
                } catch (\Exception $e) {
                    $this->error("Failed to generate payment for {$sale->id}: {$e->getMessage()}");
                    report($e);
                    if (app()->environment('local')) {
                        throw $e;
                    }
                }
            });

        return 0;
    }
}
