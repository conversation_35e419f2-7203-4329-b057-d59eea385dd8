<?php

namespace App\Console\Commands;

use App\Actions\ProcessGoCardlessPayoutFromWebhook;
use App\Services\Payments\DirectDebit\GoCardlessDirectDebitPaymentProcessor;
use GoCardlessPro\Core\ListResponse;
use Illuminate\Console\Command;

class FetchGoCardlessPayouts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gocardless:fetch-payouts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch all payouts from the GoCardless account';

    /**
     * Execute the console command.
     */
    public function handle(
        GoCardlessDirectDebitPaymentProcessor $goCardlessPaymentProcessor,
        ProcessGoCardlessPayoutFromWebhook $processGoCardlessPayout
    ): void {
        $client = $goCardlessPaymentProcessor->getClient();

        $this->info('Fetching payouts from GoCardless...');

        //        try {
        // Fetch payouts, with pagination if necessary
        $afterCursor = null;

        do {
            // Fetch a page of payouts (with cursor-based pagination)
            /** @var ListResponse $payouts */
            $payouts = $client->payouts()->list([
                'params' => [
                    'limit' => 50, // Fetch 50 records per page
                    'after' => $afterCursor, // Fetch the next page if available
                ],
            ]);

            foreach ($payouts->records as $index => $payout) {
                $processGoCardlessPayout->execute($payout->id);
                $this->info("Payout ID: {$payout->id}");
                $this->info('Amount: '.($payout->amount / 100).' '.$payout->currency);
                $this->info("Status: {$payout->status}");
                $this->info("Created at: {$payout->created_at}");
                $this->info("Arrival date: {$payout->arrival_date}");
                $this->info('------------------------------------');
            }

            $afterCursor = $payouts->after;

        } while ($afterCursor); // Continue fetching pages if there's a next page

        $this->info('Finished fetching all payouts.');

        //        } catch (\Exception $e) {
        //            $this->error('Error fetching payouts: ' . $e->getMessage());
        //        }
    }
}
