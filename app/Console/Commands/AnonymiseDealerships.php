<?php

namespace App\Console\Commands;

use App\Models\Dealership;
use Illuminate\Console\Command;

class AnonymiseDealerships extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:anonymise-dealerships';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'For demoing purposes, this command anonymises all dealerships';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (app()->isProduction()) {
            $this->error('This command can not be run in production');

            return 1;
        }

        Dealership::each(function (Dealership $dealership) {
            $this->info("Anonymising {$dealership->id}");
            $firstName = fake('en_GB')->firstName();
            $lastName = fake('en_GB')->lastName();
            $mailbox = strtolower(sprintf('%s.%s', $firstName, $lastName));
            $email = $mailbox.'@example.com';
            while (Dealership::where('email', $email)->exists()) {
                $mailbox .= mt_rand(1, 999);
                $email = $mailbox.'@example.com';
            }
            $dealership->update([
                'name' => fake('en_GB')->company(),
                'contact_first_name' => $firstName,
                'contact_last_name' => $lastName,
                'email' => $email,
                'address_1' => fake('en_GB')->streetAddress(),
                'address_2' => fake('en_GB')->secondaryAddress(),
                'city' => fake('en_GB')->city(),
                'county' => fake('en_GB')->city(),
                'postcode' => strtoupper(fake('en_GB')->bothify('??## ???')),
            ]);
        });

        $this->info('Anonymised all dealerships');

        return 0;
    }
}
