<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Vite;

class UploadStylesheetToS3ForBrowsershot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:upload-stylesheet';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Upload the stylesheet to S3 for Browsershot to use';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Storage::disk('public')->put('assets/app.css', Vite::content('resources/css/app.css'));
        $this->info('Stylesheet uploaded to S3, accessible at: '.Storage::disk('public')->url('assets/app.css'));
    }
}
