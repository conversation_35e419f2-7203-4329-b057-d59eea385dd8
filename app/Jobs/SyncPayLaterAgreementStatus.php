<?php

namespace App\Jobs;

use App\Models\PayLaterAgreement;
use App\Services\Payments\PayLater\PayLaterPaymentProcessor;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncPayLaterAgreementStatus implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public PayLaterAgreement $payLaterAgreement) {}

    /**
     * Execute the job.
     */
    public function handle(PayLaterPaymentProcessor $payLaterPaymentProcessor): void
    {
        $applicationStatus = $payLaterPaymentProcessor
            ->forAccount($this->payLaterAgreement->payable->account)
            ->getApplicationStatus($this->payLaterAgreement->token);

        $this->payLaterAgreement->update([
            'status' => $applicationStatus->status,
            'provider_reference' => $applicationStatus->providerReference,
        ]);
    }
}
