<?php

namespace App\Jobs;

use App\Actions\AI\CreateClaimEstimatesFromAIParsedData;
use App\Models\AiTask;
use App\Models\ClaimEstimate;
use App\Models\File;
use App\Models\InboundEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ProcessClaimEstimate implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public InboundEmail|File $subject) {}

    /**
     * Execute the job.
     */
    public function handle(CreateClaimEstimatesFromAIParsedData $createClaimEstimateFromAiParsedData): void
    {
        $request = AiTask::where('processor_class', \App\Services\AI\WarrantyClaimEstimateParser::class)
            ->first()
            ->fetchOrProcessRequest($this->subject);

        /** @var ClaimEstimate $claimEstimate */
        $claimEstimate = $createClaimEstimateFromAiParsedData->execute($request->response)->first();

        //        if ($this->file->account_id && $this->file->account_id !== $claimEstimate->getAccountId()) {
        //            throw new \RuntimeException('File account ID does not match claim estimate account ID');
        //        }

        //        $this->file->related()->associate($claimEstimate);

        $file = match (get_class($this->subject)) {
            File::class => $this->subject,
            InboundEmail::class => $this->subject->attachments()
                ->whereIn('mime_type', [
                    'application/pdf',
                    'message/rfc822',
                ])
                ->first(),
        };

        if ($file) {
            $file->related()->associate($claimEstimate->claim);
            $file->account_id = $claimEstimate->getAccountId();
            $file->save();
        }
        // TODO attach inbound email if no file

        // TODO notify if not matched
    }
}
