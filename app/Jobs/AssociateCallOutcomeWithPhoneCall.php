<?php

namespace App\Jobs;

use App\Models\PhoneCall;
use App\Models\SalesLeadCallOutcome;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class AssociateCallOutcomeWithPhoneCall implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public SalesLeadCallOutcome $salesLeadCallOutcome) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $phoneCall = PhoneCall::query()
            ->where('customer_id', $this->salesLeadCallOutcome->salesLead->sale->customer_id)
            ->where('user_id', auth()->id())
            ->where('started_at', '<', $this->salesLeadCallOutcome->created_at)
            ->first();

        $this->salesLeadCallOutcome->update([
            'phone_call_id' => $phoneCall?->id,
        ]);
    }
}
