<?php

namespace App\Jobs;

use App\Models\PhoneCall;
use App\Services\Voip\Voip3cxProvider;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use OpenAI\Responses\Audio\TranscriptionResponse;

class TranscribePhoneCall implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(private PhoneCall $phoneCall) {}

    /**
     * Get the middleware the job should pass through.
     *
     * @return array<int, object>
     */
    public function middleware(): array
    {
        return [new WithoutOverlapping($this->phoneCall->id)->dontRelease()];
    }

    /**
     * Execute the job.
     */
    public function handle(Voip3cxProvider $voip): void
    {
        if ($this->phoneCall->transcript()->exists()) {
            return;
        }

        $source = null;

        try {
            $url = $voip->recordingUrl($this->phoneCall->recording_id);
            $source = fopen($url, 'r');

            $filename = sys_get_temp_dir().DIRECTORY_SEPARATOR.str()->uuid().'.wav';
            $dest = fopen($filename, 'w+');

            if (stream_copy_to_stream($source, $dest) === false) {
                throw new \RuntimeException('Failed to copy audio to temp file');
            }

            fseek($dest, 0);

            /** @var TranscriptionResponse $response */
            $response = \OpenAI\Laravel\Facades\OpenAI::audio()->transcribe([
                'model' => 'gpt-4o-transcribe',
                'file' => $dest,
            ]);
        } catch (\Throwable $e) {
            if (app()->isLocal()) {
                throw $e;
            }
            report($e);
        } finally {
            if ($source) {
                fclose($source);
            }
        }

        $this->phoneCall->transcript()->create([
            'text' => $response->text,
            'processing_ms' => $response->meta()->openai->processingMs,
        ]);
    }
}
