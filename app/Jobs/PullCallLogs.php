<?php

namespace App\Jobs;

use App\Models\Customer;
use App\Models\Dealership;
use App\Models\PhoneCall;
use App\Models\VoipUser;
use App\Services\Voip\Voip3cxProvider;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\Middleware\WithoutOverlapping;

class PullCallLogs implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(private ?Carbon $date = null, private ?string $uniqueKey = null)
    {
        if (! $this->date) {
            $this->date = Carbon::today();
        }
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array<int, object>
     */
    public function middleware(): array
    {
        if ($this->uniqueKey) {
            return [new WithoutOverlapping($this->uniqueKey)->dontRelease()];
        }

        return [];
    }

    /**
     * Execute the job.
     */
    public function handle(Voip3cxProvider $voipProvider): void
    {
        $callLogs = collect($voipProvider->getCallLogs($this->date->copy()->startOfDay(), $this->date->copy()->endOfDay()))
            ->reject(fn ($item) => $item['SourceType'] === 0 && $item['DestinationType'] === 0)
            ->reverse()
            ->values();

        $extensions = $callLogs->flatMap(function ($item) {
            $items = [];
            if ($item['SourceType'] === 0) {
                $items[] = $item['SourceDn'];
            }
            if ($item['DestinationType'] === 0) {
                $items[] = $item['DestinationDn'];
            }
            if (($item['ActionDnType'] ?? null) === 0) {
                $items[] = $item['actionDnDn'];
            }

            return $items;
        })->unique()->values();
        $users = VoipUser::whereIn('number', $extensions)->pluck('user_id', 'number');

        $phoneNumbers = $callLogs
            ->flatMap(fn ($item) => [$item['SourceCallerId'], $item['DestinationCallerId']])
            ->filter(fn ($item) => str($item)->startsWith(['07', '01']))
            ->unique()
            ->values();

        $customers = Customer::whereIn('phone', $phoneNumbers)->pluck('id', 'phone');
        $dealerships = Dealership::whereIn('phone', $phoneNumbers)->pluck('id', 'phone');

        $calls = collect($callLogs)
            ->map(fn ($item) => [
                'call_log_call_id' => $item['CallId'],
                'call_log_segment_id' => $item['SegmentId'],
                'started_at' => Carbon::parse($item['StartTime']),
                'source_type' => $item['SourceType'],
                'source_dn' => $item['SourceDn'],
                'source_caller_id' => $item['SourceCallerId'],
                'source_display_name' => $item['SourceDisplayName'],
                'destination_type' => $item['DestinationType'],
                'destination_dn' => $item['DestinationDn'],
                'destination_caller_id' => $item['DestinationCallerId'],
                'destination_display_name' => $item['DestinationDisplayName'],
                'action_type' => $item['ActionType'],
                'action_dn_type' => $item['ActionDnType'] ?? null,
                'action_dn' => $item['actionDnDn'] ?? null,
                'action_dn_caller_id' => $item['ActionDnCallerId'] ?? null,
                'action_dn_display_name' => $item['ActionDnDisplayName'] ?? null,

                'ringing_duration' => CarbonInterval::fromString($item['RingingDuration'])->totalSeconds,
                'talking_duration' => CarbonInterval::fromString($item['TalkingDuration'])->totalSeconds,
                'recording_url' => $item['RecordingUrl'] ?? null,
                'recording_id' => $item['SrcRecId'] ?? null,

                'reason' => $item['Reason'] ?? null,
                'answered' => $item['Answered'],

                'user_id' => $users->get($item['SourceDn']) ?: $users->get($item['DestinationDn']),
                'customer_id' => $customers->get($item['SourceCallerId']) ?: $customers->get($item['DestinationCallerId']),
                'dealership_id' => $dealerships->get($item['SourceCallerId']) ?: $dealerships->get($item['DestinationCallerId']),
            ]);

        PhoneCall::upsert($calls->all(), ['call_log_segment_id']);

        $this->queueTranscriptions();
    }

    private function queueTranscriptions()
    {
        PhoneCall::query()
            ->whereDate('started_at', $this->date)
            ->whereNotNull('recording_id')
            ->where(fn ($q) => $q->whereNotNull('dealership_id')->orWhereNotNull('customer_id'))
            ->whereDoesntHave('transcript')
            ->each(function (PhoneCall $call) {
                TranscribePhoneCall::dispatch($call);
            });
    }
}
