<?php

namespace App\Jobs;

use App\Models\Account;
use App\Models\WarrantyProductVariant;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PopulateAccountWithDefaultProductVariants
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private Account $account;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Account $account)
    {
        $this->account = $account;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        WarrantyProductVariant::query()->with('product')->each(function (WarrantyProductVariant $variant) {
            $this->account->warrantyProducts()->firstOrCreate($variant->only([
                'product_id',
                'max_engine_capacity',
                'max_mileage',
                'max_age',
                'individual_claim_limit',
                'total_claim_limit',
                'provision',
                'selling_price',
                'admin_fee',
                'monthly_selling_price',
                'monthly_admin_fee',
            ]));
        });
    }
}
