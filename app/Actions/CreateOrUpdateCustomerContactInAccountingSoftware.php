<?php

namespace App\Actions;

use App\Models\AccountingContact;
use App\Models\Customer;
use App\Services\Accounting\AccountingService;
use App\Services\Accounting\ContactData;

class CreateOrUpdateCustomerContactInAccountingSoftware
{
    public function __construct(
        private readonly AccountingService $accountingService,
        private readonly UpdateOrCreateContactInDatabase $updateOrCreateContactInDatabase,
    ) {}

    public function execute(Customer $customer)
    {
        $contactData = $this->accountingService->createContact(new ContactData(
            id: $customer->accountingContact?->accounting_software_id,
            status: 'ACTIVE',
            name: $this->generateUniqueContactName($customer),
            firstName: $customer->first_name,
            lastName: $customer->last_name,
            email: $customer->email,
            addressLine1: $customer->address_1,
            addressLine2: $customer->address_2,
            addressLine3: null,
            addressLine4: null,
            addressCity: $customer->city,
            addressRegion: $customer->county,
            addressPostcode: $customer->postcode,
        ));

        $accountingContact = $this->updateOrCreateContactInDatabase->execute($contactData);

        $customer->update([
            'accounting_contact_id' => $accountingContact->id,
        ]);
    }

    private function generateUniqueContactName(Customer $customer): string
    {
        return $customer->name.' ('.$customer->getKey().')';
        //        $name = $customer->first_name.' '.$customer->last_name;
        //        if (AccountingContact::query()->where('name', $name)->exists()) {
        //        }
        //
        //        return $customer->name;
    }
}
