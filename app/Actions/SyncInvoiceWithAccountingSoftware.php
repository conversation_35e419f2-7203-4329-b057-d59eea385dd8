<?php

namespace App\Actions;

use App\Filament\Resources\InvoiceResource;
use App\Models\Invoice;
use App\Models\InvoiceLineItem;
use App\Services\Accounting\AccountingService;
use App\Services\Accounting\ContactData;
use App\Services\Accounting\InvoiceData;
use App\Services\Accounting\InvoiceLineItemData;
use Spatie\QueueableAction\QueueableAction;

class SyncInvoiceWithAccountingSoftware
{
    use QueueableAction;

    public function __construct(
        private readonly AccountingService $accountingService,
        private readonly UpdateDatabaseInvoiceFromValueObject $updateDatabaseInvoiceFromValueObject,
        private readonly SendInvoiceEmailFromAccountingSoftware $sendInvoiceEmailFromAccountingSoftware,
    ) {}

    public function execute(Invoice $invoice, $status = null): InvoiceData
    {
        $invoiceData = $this->accountingService->createInvoice(invoiceValueObject: new InvoiceData(
            id: $invoice->accounting_software_id,
            contact: new ContactData(
                id: $invoice->invoiceable->accountingContact->accounting_software_id,
            ),
            status: $status,
            date: $invoice->date->toDateString(),
            dueDate: $invoice->due_date->toDateString(),
            invoiceNumber: null,
            reference: $invoice->reference,
            url: InvoiceResource::getUrl('view', ['record' => $invoice]),
            lineItems: $invoice
                ->lineItems()
                ->orderBy('sale_id')
                ->get()
                ->map(fn (InvoiceLineItem $lineItem) => new InvoiceLineItemData(
                    quantity: $lineItem->quantity,
                    unitAmount: $lineItem->unit_amount,
                    taxAmount: $lineItem->quantity * $lineItem->tax,
                    accountCode: $lineItem->account_code,
                    description: $lineItem->description,
                    id: $lineItem->accounting_software_id,
                )),
        ));

        $this->updateDatabaseInvoiceFromValueObject->execute($invoice, $invoiceData, firstSync: true);

        $this->sendInvoiceEmailFromAccountingSoftware->onQueue()->execute($invoice);

        return $invoiceData;
    }
}
