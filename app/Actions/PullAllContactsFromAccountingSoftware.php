<?php

namespace App\Actions;

use App\Models\AccountingContact;
use App\Services\Accounting\AccountingService;
use App\Services\Accounting\ContactData;
use Spatie\QueueableAction\QueueableAction;

class PullAllContactsFromAccountingSoftware
{
    use QueueableAction;

    public function __construct(
        private readonly AccountingService $accountingService,
        private readonly UpdateOrCreateContactInDatabase $updateOrCreateContactFromAccountingSoftware,
    ) {}

    public function execute()
    {
        $contacts = $this->accountingService->getContacts();

        AccountingContact::query()
            ->whereNotIn('accounting_software_id', $contacts->pluck('id'))
            ->delete();

        /** @var ContactData $contact */
        foreach ($contacts as $contact) {
            $this->updateOrCreateContactFromAccountingSoftware->execute($contact);
        }
    }
}
