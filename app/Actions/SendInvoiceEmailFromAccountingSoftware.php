<?php

namespace App\Actions;

use App\Models\Invoice;
use App\Services\Accounting\AccountingService;
use Spatie\QueueableAction\QueueableAction;

class SendInvoiceEmailFromAccountingSoftware
{
    use QueueableAction;

    public function __construct(protected AccountingService $accountingService) {}

    public function execute(Invoice $invoice): bool
    {
        if (! $invoice->accounting_software_id) {
            throw new \RuntimeException('Invoice has not been sent to accounting software yet.');
        }
        if ($this->accountingService->emailInvoice($invoice->accounting_software_id)) {
            $invoice->markEmailSent();

            return true;
        }

        return false;
    }
}
