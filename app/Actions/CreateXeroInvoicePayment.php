<?php

namespace App\Actions;

use App\Enums\PaymentProvider;
use App\Models\Invoice;
use App\Services\Accounting\AccountingService;
use App\Services\Accounting\PaymentData;
use Spatie\QueueableAction\QueueableAction;

class CreateXeroInvoicePayment
{
    use QueueableAction;

    public function __construct(
        protected AccountingService $accountingService,
    ) {}

    public function execute(Invoice $invoice): PaymentData
    {
        if ($invoice->accounting_software_payment_id) {
            throw new \RuntimeException('Invoice already has an accounting software payment ID.');
        }

        $paymentData = $this->accountingService->createInvoicePayment(new PaymentData(
            id: null,
            invoiceId: $invoice->accounting_software_id,
            accountId: match ($invoice->payment->provider) {
                PaymentProvider::GO_CARDLESS => config('services.xero.go_cardless_bank_account_id'),
                PaymentProvider::ACCESS_PAYSUITE => config('services.xero.access_paysuite_bank_account_id'),
            },
            date: $invoice->payment->charge_date,
            amount: $invoice->payment->amount,
        ));

        $invoice->update([
            'accounting_software_payment_id' => $paymentData->id,
            'status' => 'PAID',
        ]);

        return $paymentData;
    }
}
