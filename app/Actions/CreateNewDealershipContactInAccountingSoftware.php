<?php

namespace App\Actions;

use App\Models\AccountingContact;
use App\Models\Dealership;
use App\Services\Accounting\AccountingService;
use App\Services\Accounting\ContactData;

class CreateNewDealershipContactInAccountingSoftware
{
    public function __construct(
        private readonly AccountingService $accountingService,
        private readonly UpdateOrCreateContactInDatabase $updateOrCreateContactInDatabase,
    ) {}

    public function execute(Dealership $dealership)
    {
        $dealership->load('accountingContact');

        $contactData = $this->accountingService->createContact(new ContactData(
            id: $dealership->accountingContact?->accounting_software_id,
            status: 'ACTIVE',
            //            name: $this->generateUniqueContactName($customer),
            name: $dealership->name,
            firstName: $dealership->contact_first_name,
            lastName: $dealership->contact_last_name,
            email: $dealership->email,
            addressLine1: $dealership->address_1,
            addressLine2: $dealership->address_2,
            addressLine3: null,
            addressLine4: null,
            addressCity: $dealership->city,
            addressRegion: $dealership->county,
            addressPostcode: $dealership->postcode,
        ));

        $accountingContact = $this->updateOrCreateContactInDatabase->execute($contactData);

        $dealership->update([
            'accounting_contact_id' => $accountingContact->id,
        ]);
    }

    private function generateUniqueContactName(Dealership $dealership)
    {
        if (AccountingContact::query()->where('name', $dealership->name)->exists()) {
            return $dealership->name.' ('.$dealership->getKey().')';
        }

        return $dealership->name;
    }
}
