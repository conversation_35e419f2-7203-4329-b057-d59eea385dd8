<?php

namespace App\Actions;

use App\Models\Concerns\OTPAuthenticatable;
use App\Notifications\OneTimePasscodeNotification;
use Illuminate\Support\Facades\Hash;

class SendOTPAuthenticationCode
{
    const OTP_TTL = 5;

    public function execute(OTPAuthenticatable $OTPAuthenticatable)
    {
        if ($this->isThrottled($OTPAuthenticatable)) {
            throw new \RuntimeException('Too many attempts. Please try again later.');
        }

        $OTPAuthenticatable->notify(new OneTimePasscodeNotification($this->generateAndStoreOneTimePasscode($OTPAuthenticatable)));
    }

    protected function generateAndStoreOneTimePasscode(OTPAuthenticatable $OTPAuthenticatable): string
    {
        $passcode = random_int(100000, 999999);

        $OTPAuthenticatable->oneTimePasscodes()->create([
            'passcode' => Hash::make($passcode),
            'expires_at' => now()->addMinutes(self::OTP_TTL),
        ]);

        return (string) $passcode;
    }

    protected function isThrottled(OTPAuthenticatable $OTPAuthenticatable): bool
    {
        return $OTPAuthenticatable->oneTimePasscodes()->valid()->count() >= 3;
    }
}
