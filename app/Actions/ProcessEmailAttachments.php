<?php

namespace App\Actions;

use App\Models\InboundEmail;
use Illuminate\Support\Facades\Storage;
use Spatie\QueueableAction\QueueableAction;
use Z<PERSON>ateson\MailMimeParser\Message\MimePart;

class ProcessEmailAttachments
{
    use QueueableAction;

    public function execute(InboundEmail $inboundEmail): void
    {
        foreach ($inboundEmail->getParsedContent()->getAllAttachmentParts() as $attachment) {
            $this->processAttachment($inboundEmail, $attachment);
        }

        $inboundEmail->update(['processed_at' => now()]);
    }

    private function processAttachment(InboundEmail $inboundEmail, MimePart $attachment): void
    {
        $filename = $attachment->getFilename();
        $extension = pathinfo($filename, PATHINFO_EXTENSION);

        $path = 'vault/'.str()->uuid().'.'.$extension;
        $content = $attachment->getContent();
        Storage::put($path, $content);

        $inboundEmail->attachments()->create([
            'name' => $attachment->getFilename(),
            'path' => $path,
            'size' => strlen($content),
            'mime_type' => str($attachment->getContentType())->before(';')->toString(),
        ]);
    }
}
