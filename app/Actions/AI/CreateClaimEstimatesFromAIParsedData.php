<?php

namespace App\Actions\AI;

use App\Enums\ClaimEstimateLineItemType;
use App\Enums\ClaimStatus;
use App\Models\Claim;
use App\Models\ClaimEstimate;
use App\Models\Repairer;
use App\Models\VehicleComponent;
use App\Models\Warranty;
use App\Services\Tax\VatCalculator;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Spatie\QueueableAction\QueueableAction;

class CreateClaimEstimatesFromAIParsedData
{
    use QueueableAction;

    public function execute(array $data): Collection
    {
        return collect($data['items'])->map(function (array $item) {
            // Create or update repairer
            $repairer = $this->createOrUpdateRepairer($item);

            // Find or create claim
            $claim = $this->findOrCreateClaim($item);

            // Create claim estimate
            return $this->createClaimEstimate($item, $claim, $repairer);
        });
    }

    private function createOrUpdateRepairer(array $data): Repairer
    {
        $repairer = Repairer::firstOrNew([
            'name' => $data['name'],
        ]);

        // Only update fields if they are null
        $repairer->phone = $repairer->phone ?? $data['phone'] ?? null;
        $repairer->website = $repairer->website ?? $data['website'] ?? null;
        $repairer->vat_number = $repairer->vat_number ?? $data['vat_number'] ?? null;
        $repairer->company_number = $repairer->company_number ?? $data['company_number'] ?? null;
        $repairer->bank_sort_code = $repairer->bank_sort_code ?? $data['bank_sort_code'] ?? null;
        $repairer->bank_account_number = $repairer->bank_account_number ?? $data['bank_sort_code'] ?? null;

        if (isset($data['postal_address'])) {
            $repairer->address_1 = $repairer->address_1 ?? $data['postal_address']['line_1'] ?? null;
            $repairer->address_2 = $repairer->address_2 ?? $data['postal_address']['line_2'] ?? null;
            $repairer->city = $repairer->city ?? $data['postal_address']['city'] ?? null;
            $repairer->county = $repairer->county ?? $data['postal_address']['county'] ?? null;
            $repairer->country = $repairer->country ?? $data['postal_address']['county'] ?? null;
            $repairer->postcode = $repairer->postcode ?? $data['postal_address']['postcode'] ?? null;
        }

        $repairer->save();

        return $repairer;
    }

    private function findOrCreateClaim(array $data): Claim
    {
        // Find vehicle by VRM
        $warranty = Warranty::query()
            ->whereHas('sale', fn ($query) => $query
                ->where('vrm', str_replace(' ', '', $data['vrm']))
                ->orWhere('private_plate', $data['vrm'])
                ->orWhere('vin', $data['vin'])
            )
            ->live()
            ->latest()
            ->first();

        if (! $warranty) {
            throw new \RuntimeException('Warranty not found');
        }

        // Look for existing pending claim
        $pendingClaim = $warranty->claims()
            ->withStatus(ClaimStatus::AWAITING_ESTIMATE)
            ->latest()
            ->first();

        if ($pendingClaim) {
            return $pendingClaim;
        }

        // Create the new claim
        return $warranty->claims()->create([
            'fault_description' => $data['work_required'],
            'current_mileage' => $data['mileage'] ?? null,
            'fault_type_id' => 1, // TODO
            'failure_date' => now(), // TODO,
            'vehicle_location' => 'Unknown', // TODO
        ]);
    }

    private function createClaimEstimate(array $data, Claim $claim, Repairer $repairer): ClaimEstimate
    {
        return DB::transaction(function () use ($data, $claim, $repairer) {
            $vatCalculator = app(VatCalculator::class);

            $estimate = $claim->estimates()->create([
                'repairer_id' => $repairer->getKey(),
                'estimate_completed_at' => $data['timestamp'] ? Carbon::parse($data['timestamp']) : null,
                'workshop_name' => $repairer->name,
                'workshop_phone' => $repairer->phone,
                'workshop_email' => $repairer->email,
                'workshop_contact' => $repairer->contact,
                //            'vrm' => $data['vrm'], // TODO store this also and check against claim using AI?
                //            'vin' => $data['vin'],
                //            'vehicle_make' => $data['vehicle_make'],
                //            'vehicle_model' => $data['vehicle_model'],
                //            'vehicle_year' => $data['vehicle_year'],
                'current_mileage' => $data['mileage'],
                'work_required' => $data['work_required'],
            ]);

            // Create line items
            foreach ($data['line_items'] as $item) {
                $estimate->lineItems()->create([
                    'type' => $item['type'] ?? ClaimEstimateLineItemType::PART,
                    'vehicle_component_id' => (($item['type'] ?? null) === ClaimEstimateLineItemType::PART->value && isset($item['vehicle_component']))
                        ? VehicleComponent::firstWhere('name', $item['vehicle_component'])?->getKey()
                        : null,
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'amount' => $item['unit_amount'],
                    'vat' => $item['vat_charged'] ? $vatCalculator->getVatAmount($item['unit_amount']) : 0,
                ]);
            }

            return $estimate;
        });
    }
}
