<?php

namespace App\Actions;

use App\Models\Payout;
use App\Models\PayoutLineItem;
use App\Services\Accounting\AccountingService;
use App\Services\Accounting\BankTransactionData;
use App\Services\Accounting\BankTransferData;
use App\Services\Accounting\BankTransferLineItemData;
use App\Services\Accounting\ContactData;
use Spatie\QueueableAction\QueueableAction;

class CreateXeroBankTransferForPayout
{
    use QueueableAction;

    public function __construct(
        protected AccountingService $accountingService,
    ) {}

    public function execute(Payout $payout): BankTransferData
    {
        if ($payout->accounting_software_transfer_id) {
            throw new \Exception('Payout already has already been recorded as a Bank Transfer');
        }

        $this->createBankTransactionForProcessorFees($payout);

        return $this->createBankTransfer($payout);
    }

    private function createBankTransactionForProcessorFees(Payout $payout)
    {
        $lineItems = $payout->lineItems()
            ->with('payment')
            ->whereIn('type', ['gocardless_fee', 'surcharge_fee'])
            ->get()
            ->map(fn (PayoutLineItem $lineItem) => new BankTransferLineItemData(
                quantity: 1,
                unitAmount: 0 - $lineItem->amount - $lineItem->tax,
                taxAmount: $lineItem->tax,
                accountCode: config('services.xero.go_cardless_fee_account_code'),
                description: $lineItem->payment->processor_payment_id.' - '.str_replace('_', ' ', strtoupper($lineItem->type)),
                id: null,
            ));

        $bankTransactionData = $this->accountingService->createBankTransaction(new BankTransactionData(
            id: null,
            contact: new ContactData(
                id: config('services.xero.go_cardless_contact_id'),
            ),
            type: 'SPEND',
            status: 'AUTHORISED',
            date: $payout->arrival_date,
            reference: $payout->reference,
            url: $payout->getUrl(),
            bankAccountId: config('services.xero.go_cardless_bank_account_id'),
            lineItems: $lineItems,
            isReconciled: true,
        ));
    }

    private function createBankTransfer(Payout $payout): BankTransferData
    {
        $bankTransferData = $this->accountingService->createBankTransfer(new BankTransferData(
            id: null,
            date: $payout->arrival_date,
            amount: $payout->amount,
            fromAccountId: config('services.xero.go_cardless_bank_account_id'),
            toAccountId: config('services.xero.current_bank_account_id'),
            fromTransactionId: null,
            toTransactionId: null,
            reference: $payout->reference,
            fromIsReconciled: true,
            toIsReconciled: false,
        ));

        $payout->update([
            'accounting_software_transfer_id' => $bankTransferData->id,
            'accounting_software_from_transaction_id' => $bankTransferData->fromTransactionId,
            'accounting_software_to_transaction_id' => $bankTransferData->toTransactionId,
        ]);

        return $bankTransferData;
    }
}
