<?php

namespace App\Actions;

use App\Enums\PaymentMethod;
use App\Models\BillingRequest;
use App\Models\Concerns\BillableContract;
use App\Services\Payments\DirectDebit\DirectDebitPaymentProcessor;

readonly class SetupBillingRequest
{
    public function __construct(
        private DirectDebitPaymentProcessor $paymentProcessor,
    ) {}

    public function execute(BillableContract $billable, $sendNotification = false): void
    {
        if ($billable->getPaymentMethod() !== PaymentMethod::DIRECT_DEBIT || $billable->requiresPaymentSetup() === false) {
            return;
        }

        /** @var BillingRequest $billingRequest */
        $billingRequest = $billable->billingRequest()->first();

        if (! $billingRequest || $billingRequest->expiresSoon()) {
            $billingRequestData = $this->paymentProcessor->setupBillingRequestForCustomer($billable);

            $billable->billingRequest()->associate(
                BillingRequest::create([
                    'provider' => $this->paymentProcessor->getProcessorIdentifier(),
                    'provider_customer_id' => $billingRequestData->paymentProcessorCustomerId,
                    'provider_billing_request_id' => $billingRequestData->paymentProcessorBillingRequestId,
                    'mandate_url' => $billingRequestData->authorisationUrl,
                    'expires_at' => $billingRequestData->urlExpiresAt,
                    'status' => $billingRequestData->status,
                    'mandate_activated_at' => null,
                ])
            )->save();
        }

        if ($sendNotification) {
            $billable->sendPaymentSetupNotification();
        }
    }
}
