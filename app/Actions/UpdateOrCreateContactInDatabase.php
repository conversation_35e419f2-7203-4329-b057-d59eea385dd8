<?php

namespace App\Actions;

use App\Models\AccountingContact;
use App\Services\Accounting\ContactData;

class UpdateOrCreateContactInDatabase
{
    public function execute(ContactData $contactData): AccountingContact
    {
        return AccountingContact::query()->updateOrCreate(
            ['accounting_software_id' => $contactData->id],
            [
                'name' => $contactData->name,
                'email' => $contactData->email,
                'address_line_1' => $contactData->addressLine1,
                'address_line_2' => $contactData->addressLine2,
                'address_line_3' => $contactData->addressLine3,
                'address_line_4' => $contactData->addressLine4,
                'address_city' => $contactData->addressCity,
                'address_region' => $contactData->addressRegion,
                'address_postcode' => $contactData->addressPostcode,
            ]
        );
    }
}
