<?php

namespace App\Actions;

use App\Models\ClaimAuthorisation;
use App\Models\ClaimEstimateLineItem;
use App\Models\Invoice;
use App\Services\Tax\VatCalculator;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Spatie\QueueableAction\QueueableAction;

class GenerateWarrantyClaimInvoiceToDealershipAndGeneratePayment
{
    use QueueableAction;

    public function __construct(
        private readonly SyncInvoiceWithAccountingSoftware $syncInvoiceWithAccountsSoftware,
        private readonly VatCalculator $vatCalculator,
        private readonly GenerateInvoicePaymentForDealership $generateInvoicePaymentForDealership,
    ) {}

    public function execute(ClaimAuthorisation $claimAuthorisation): Invoice
    {
        $dealership = $claimAuthorisation->estimate->claim->warranty->sale->dealership;

        if (! $dealership->accountingContact) {
            throw new \RuntimeException('Dealership has not been linked to accounting software contact yet.');
        }

        if ($claimAuthorisation->invoice()->exists()) {
            throw new \RuntimeException('Invoice has already been created.');
        }

        $claimAuthorisation->load('estimate.lineItems.vehicleComponent');

        $invoice = DB::transaction(function () use ($claimAuthorisation, $dealership) {
            /** @var Invoice $invoice */
            $invoice = $claimAuthorisation->invoice()
                ->firstOr(function () use ($dealership, $claimAuthorisation) {
                    $invoice = $dealership->invoices()->create([
                        'account_id' => $dealership->account_id,
                        'date' => Carbon::today()->toDateString(),
                        'due_date' => Carbon::today()->addDay()->toDateString(),
                        'description' => "Warranty Claim Settlement for {$claimAuthorisation->estimate->claim->warranty->sale->vehicle->vrm}",
                        'reference' => $claimAuthorisation->estimate->claim->warranty->sale->vehicle->vrm,
                    ]);
                    $claimAuthorisation->invoice()->associate($invoice)->save();

                    return $invoice;
                });

            /** @var ClaimEstimateLineItem $estimateLineItem */
            foreach ($claimAuthorisation->estimate->lineItems as $estimateLineItem) {
                $invoice->lineItems()->create([
                    'sale_id' => $claimAuthorisation->estimate->claim->warranty->sale_id,
                    'type' => $estimateLineItem->type,
                    'account_code' => $this->getAccountCodeForWarrantyRepairReimbursement(),
                    'quantity' => $estimateLineItem->quantity,
                    'description' => implode(': ', array_filter([
                        $estimateLineItem->vehicleComponent?->name,
                        $estimateLineItem->description,
                    ])),
                    'unit_amount' => $estimateLineItem->amount,
                    'tax' => $estimateLineItem->vat,
                ]);
                if ($estimateLineItem->customer_contribution > 0) {
                    $invoice->lineItems()->create([
                        'sale_id' => $claimAuthorisation->estimate->claim->warranty->sale_id,
                        'type' => 'CUSTOMER_CONTRIBUTION',
                        'account_code' => $this->getAccountCodeForWarrantyRepairReimbursement(),
                        'quantity' => 1,
                        'description' => 'Customer Contribution',
                        'unit_amount' => 0 - $estimateLineItem->customer_contribution,
                        'tax' => 0,
                    ]);
                }
            }

            return $invoice;
        });

        $this->syncInvoiceWithAccountsSoftware->execute($invoice, 'AUTHORISED');

        $this->generateInvoicePaymentForDealership->execute($invoice, sendToPaymentProvider: true);

        return $invoice;
    }

    protected function getAccountCodeForWarrantyRepairReimbursement(): string
    {
        return config('accounting.nominal_codes.other.warranty_claim_repair_reimbursement.code');
    }
}
