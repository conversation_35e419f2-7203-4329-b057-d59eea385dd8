<?php

namespace App\Actions;

use App\Models\Invoice;
use App\Models\InvoiceLineItem;
use App\Services\Accounting\InvoiceData;
use App\Services\Accounting\InvoiceLineItemData;

class UpdateDatabaseInvoiceFromValueObject
{
    public function execute(Invoice $invoice, InvoiceData $invoiceData, bool $firstSync = false): Invoice
    {
        $paymentId = $invoiceData->payments->first()?->id;
        if (! $paymentId && $invoiceData->creditNotes->isNotEmpty()) {
            $paymentId = 'CREDIT_NOTE';
        }

        $invoice->update([
            'accounting_software_id' => $invoiceData->id,
            'accounting_software_payment_id' => $paymentId,
            'date' => $invoiceData->date,
            'due_date' => $invoiceData->dueDate,
            'invoice_number' => $invoiceData->invoiceNumber,
            'status' => $invoiceData->status,
            'reference' => $invoiceData->reference,
        ]);

        // Delete any line items that are no longer present in the invoice (except where it's the first sync)
        if (! $firstSync) {
            $invoice->lineItems()
                ->whereNotNull('accounting_software_id')
                ->whereNotIn('accounting_software_id', collect($invoiceData->lineItems)->pluck('id'))
                ->delete();
        }

        $invoice->lineItems()->each(function (InvoiceLineItem $lineItem, $index) use ($invoiceData, $firstSync) {
            if ($firstSync) {
                $lineItem->update([
                    'accounting_software_id' => $invoiceData->lineItems[$index]->id,
                ]);
            } elseif ($lineItem->accounting_software_id) {
                /** @var InvoiceLineItemData $itemData */
                $itemData = collect($invoiceData->lineItems)->firstWhere('id', $lineItem->accounting_software_id);
                if ($itemData) {
                    $lineItem->update([
                        'quantity' => $itemData->quantity,
                        'description' => $itemData->description,
                        'unit_amount' => $itemData->unitAmount,
                        'tax' => $itemData->taxAmount / $itemData->quantity,
                        'account_code' => $itemData->accountCode,
                    ]);
                }
            }
        });

        return $invoice;
    }
}
