<?php

namespace App\Actions;

use App\Models\AccountingContact;
use App\Services\Accounting\AccountingService;
use Spatie\QueueableAction\QueueableAction;

class PullContactFromAccountingSoftware
{
    use QueueableAction;

    public function __construct(protected AccountingService $accountingService) {}

    public function execute($resourceId): AccountingContact
    {
        try {
            $contact = $this->accountingService->getContact($resourceId);
        } catch (\Exception $e) {
            throw new \Exception("Failed to pull contact from accounting software: {$e->getMessage()}");
        }

        return AccountingContact::updateOrCreate(
            ['accounting_software_id' => $contact->id],
            [
                'name' => $contact->name,
                'email' => $contact->email,
                'address_line_1' => $contact->addressLine1,
                'address_line_2' => $contact->addressLine2,
                'address_line_3' => $contact->addressLine3,
                'address_line_4' => $contact->addressLine4,
                'address_city' => $contact->addressCity,
                'address_region' => $contact->addressRegion,
                'address_postcode' => $contact->addressPostcode,
            ]
        );
    }
}
