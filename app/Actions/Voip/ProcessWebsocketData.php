<?php

namespace App\Actions\Voip;

use App\Jobs\PullCallLogs;
use App\Services\Voip\Voip3cxProvider;

class ProcessWebsocketData
{
    const string CACHE_PREFIX = 'voip_call_cache:';

    const int TTL = 36000; // 10 hours

    public function __construct(private readonly Voip3cxProvider $voipProvider) {}

    public function execute(array $data): void
    {
        if (! preg_match('#^/callcontrol/(\d+)/participants/(\d+)$#', $data['event']['entity'], $matches)) {
            // This should not happen
            return;
        }

        $cacheKey = self::CACHE_PREFIX.$data['event']['entity'];

        [, $dn, $participantId] = $matches;

        if ($data['event']['event_type'] === 0) {
            if (cache()->has($cacheKey)) {
                // in case of race condition, only let the first one through
                return;
            }

            // Immediately put a placeholder (rather than waiting for API response) in the cache to prevent race conditions
            cache()->put($cacheKey, [], self::TTL);

            // The connection is opening so get the data from the API
            $response = $this->voipProvider->getCallControlParticipant($dn, $participantId);

            // Cache the API response so we can query it later
            cache()->put($cacheKey, $response, self::TTL);

        } else {
            // The connection has closed. We cannot query it using the API (it would return 403) so we will pull the cache.
            $response = cache()->pull($cacheKey, []);

            // Check if the call was a real call (not just the dialler setting up the caller's line)
            if (($response['party_dn_type'] ?? null) === 'None' && ($response['originated_by_type'] ?? null) === 'None') {
                // The response was cached, and we are certain it is the initial call setup where the dialler is setting up
                // the connection to the outbound caller. We are not interested in this, so can save an API call.
                return;
            }

            // A real call has just ended, so we're going to dispatch a job to pull the call logs
            PullCallLogs::dispatch(uniqueKey: $response['callid'] ?? null)->delay(1);
        }
    }
}
