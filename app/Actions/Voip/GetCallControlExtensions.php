<?php

namespace App\Actions\Voip;

use App\Services\Voip\Voip3cxProvider;

class GetCallControlExtensions
{
    public function __construct(private readonly Voip3cxProvider $voipProvider) {}

    public function execute()
    {
        try {
            return collect($this->voipProvider->getCallControlData())
                ->where('type', 'Wextension')
                ->mapWithKeys(fn ($item) => [
                    $item['dn'] => $item['dn'],
                ])
                ->all();
        } catch (\Throwable $exception) {
            return [];
        }
    }
}
