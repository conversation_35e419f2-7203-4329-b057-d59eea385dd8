<?php

namespace App\Actions;

use App\Models\Invoice;
use App\Services\Accounting\AccountingService;
use Spatie\QueueableAction\QueueableAction;

class DeleteXeroInvoicePayment
{
    use QueueableAction;

    public function __construct(
        protected AccountingService $accountingService,
        //        protected UpdateDatabaseInvoiceFromValueObject $updateDatabaseInvoiceFromValueObject,
    ) {}

    public function execute(Invoice $invoice)
    {
        if (! $invoice->accounting_software_payment_id) {
            throw new \RuntimeException('Payment does not exist.');
        }

        $invoiceData = $this->accountingService->deleteInvoicePayment($invoice->accounting_software_payment_id);

        $invoice->update([
            'accounting_software_payment_id' => null,
            'status' => $invoiceData->status,
        ]);
    }
}
