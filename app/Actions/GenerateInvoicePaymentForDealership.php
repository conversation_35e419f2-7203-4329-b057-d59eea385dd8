<?php

namespace App\Actions;

use App\Models\Dealership;
use App\Models\Invoice;
use App\Models\Payment;
use App\Services\Payments\DirectDebit\DirectDebitPaymentProcessor;
use Spatie\QueueableAction\QueueableAction;

class GenerateInvoicePaymentForDealership
{
    use QueueableAction;

    public function __construct(
        private readonly CreatePaymentInProcessor $createPaymentInProcessor,
        private readonly DirectDebitPaymentProcessor $paymentProcessor,
    ) {}

    public function execute(Invoice $invoice, $sendToPaymentProvider = false): Payment
    {
        if (! $invoice->invoiceable instanceof Dealership) {
            throw new \RuntimeException('Invoice is not for a dealership.');
        }
        if (! $invoice->invoiceable->billingRequest?->isActive()) {
            throw new \RuntimeException('Dealership does not have a live mandate.');
        }
        if ($invoice->payment()->exists()) {
            throw new \RuntimeException('Invoice already has a payment.');
        }

        $payment = $invoice->payment()->firstOrCreate([], [
            'provider' => $invoice->invoiceable->billingRequest->provider,
            'billing_request_id' => $invoice->invoiceable->billingRequest->getKey(),
            'amount' => $invoice->lineItems->sum('total'),
            'status' => Payment::STATUS_DRAFT,
        ]);

        if ($sendToPaymentProvider) {
            $this->createPaymentInProcessor->onQueue()->execute($payment);
        }

        return $payment;
    }
}
