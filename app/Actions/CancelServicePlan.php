<?php

namespace App\Actions;

use App\Models\ServicePlan;
use App\Notifications\CustomerServicePlanCancelled;
use App\Notifications\DealerServicePlanCancelled;

class CancelServicePlan
{
    public function execute(ServicePlan $servicePlan): void
    {
        $servicePlan->update(['cancelled_at' => now()]);

        $servicePlan->sale->customer->notify(new CustomerServicePlanCancelled($servicePlan));
        $servicePlan->sale->dealership->notify(new DealerServicePlanCancelled($servicePlan));
    }
}
