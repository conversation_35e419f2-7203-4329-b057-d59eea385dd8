<?php

namespace App\Actions;

use App\Models\Payout;
use App\Services\Payments\DirectDebit\GoCardlessDirectDebitPaymentProcessor;

/**
 * Process the GoCardless payload from the webhook
 * and update the payout record in the database.
 */
class ProcessGoCardlessPayoutFromWebhook
{
    public function __construct(
        private readonly GoCardlessDirectDebitPaymentProcessor $goCardless,
        private readonly ProcessGoCardlessPaymentFromWebhook $processGoCardlessPayment,
        private readonly CreateXeroBankTransferForPayout $createXeroBankTransferForPayout,
    ) {}

    public function execute(string $payoutId)
    {
        $gcPayout = $this->goCardless->getPayout($payoutId);
        $payout = Payout::updateOrCreate([
            'processor_payout_id' => $gcPayout->id,
        ], [
            'reference' => $gcPayout->reference,
            'status' => $gcPayout->status,
            'arrival_date' => $gcPayout->arrival_date,
            'amount' => $gcPayout->amount / 100,
            'deducted_fees' => $gcPayout->deducted_fees / 100,
        ]);

        $gcPayoutItems = $this->goCardless->getPayoutItems($payoutId);
        foreach ($gcPayoutItems->records as $gcPayoutItem) {
            $payment = $this->processGoCardlessPayment->execute($gcPayoutItem->links->payment, $payout);
            $payout->lineItems()->updateOrCreate([
                'type' => $gcPayoutItem->type,
                'processor_payment_id' => $gcPayoutItem->links->payment,
            ], [
                'payment_id' => $payment->id,
                'amount' => $gcPayoutItem->amount / 100,
                'tax' => collect($gcPayoutItem->taxes)->sum('amount') / 100,
            ]);
        }

        $this->createXeroBankTransferForPayout->onQueue()->execute($payout);
    }
}
