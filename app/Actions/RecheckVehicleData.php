<?php

namespace App\Actions;

use App\Models\Vehicle;
use App\Services\VehicleLookupService\VehicleLookupService;
use App\Services\VehicleLookupService\VehicleVinMismatch;

readonly class RecheckVehicleData
{
    public function __construct(private VehicleLookupService $vehicleLookupService) {}

    public function execute(Vehicle $vehicle): void
    {
        $vehicle->update([
            'vehicle_lookup_last_checked_at' => now(),
        ]);

        try {
            $vehicleDTO = $this->vehicleLookupService->lookup($vehicle->private_plate ?: $vehicle->vrm, usingCache: app()->isLocal());
        } catch (\Exception $e) {
            report($e);

            return;
        }

        if (strcasecmp($vehicleDTO->vin, $vehicle->vin) !== 0) {
            VehicleVinMismatch::throw();
        }

        $vehicle->update([
            'body_type' => $vehicleDTO->type,
        ]);

        $vehicle->ownerChanges()->updateOrCreate([
            'start_of_ownership' => $vehicleDTO->ownerStartDate,
            'previous_owners' => $vehicleDTO->previousOwners,
        ]);
    }
}
