<?php

namespace App\Actions;

use App\Models\BillingRequest;
use App\Models\Payment;
use App\Services\Payments\DirectDebit\DirectDebitPaymentProcessor;
use App\Services\WorkingDays\WorkingDaysService;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\Log;
use Spatie\QueueableAction\QueueableAction;

class CancelPayment
{
    use QueueableAction;

    private BillingRequest $billingRequest;

    private Carbon|CarbonImmutable $startDate;

    private bool $isFirstPayment;

    private bool $isDraft;

    public function __construct(
        private readonly DirectDebitPaymentProcessor $paymentProcessor,
        private readonly WorkingDaysService $workingDaysService,
    ) {}

    public function execute(Payment $payment): void
    {
        if ($payment->status !== Payment::STATUS_PENDING) {
            Log::warning('Payment is not pending', ['payment' => $payment->toArray()]);

            return;
        }

        $this->paymentProcessor->cancelPayment($payment);

        $payment->update([
            'status' => Payment::STATUS_CANCELLED,
            'processor_payment_id' => null,
        ]);
    }
}
