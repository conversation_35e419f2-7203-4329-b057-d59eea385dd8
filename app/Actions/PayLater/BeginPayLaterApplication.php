<?php

namespace App\Actions\PayLater;

use App\Enums\PayLaterAgreementStatus;
use App\Models\PayLaterAgreement;
use App\Services\Payments\PayLater\PayLaterPaymentProcessor;
use Spatie\QueueableAction\QueueableAction;

class BeginPayLaterApplication
{
    use QueueableAction;

    public function __construct(
        private readonly PayLaterPaymentProcessor $payLaterPaymentProcessor,
    ) {}

    public function execute(PayLaterAgreement $payLaterAgreement, $sendNotification = false): void
    {
        if (! $payLaterAgreement->is_approved) {
            abort(400, 'Sale has not been pre-approved for Pay Later.');
        }

        $applicationRedirectData = $this->payLaterPaymentProcessor
            ->forAccount($payLaterAgreement->payable->account)
            ->startApplication(
                payLaterAgreement: $payLaterAgreement,
                reference: $payLaterAgreement->getKey(),
            );

        $payLaterAgreement->update([
            'token' => $applicationRedirectData->token,
            'url' => $applicationRedirectData->url,
            'status' => PayLaterAgreementStatus::PENDING,
        ]);

        if ($sendNotification) {
            $payLaterAgreement->sendPaymentSetupNotification();
        }
    }
}
