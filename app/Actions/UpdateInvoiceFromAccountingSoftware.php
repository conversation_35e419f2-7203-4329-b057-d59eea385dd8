<?php

namespace App\Actions;

use App\Models\Invoice;
use App\Services\Accounting\AccountingService;
use App\Services\Accounting\InvoiceData;
use Spatie\QueueableAction\QueueableAction;

class UpdateInvoiceFromAccountingSoftware
{
    use QueueableAction;

    public function __construct(
        protected AccountingService $accountingService,
        protected UpdateDatabaseInvoiceFromValueObject $updateDatabaseInvoiceFromValueObject,
    ) {}

    public function execute(Invoice $invoice): InvoiceData
    {
        $invoiceData = $this->accountingService->getInvoice($invoice->accounting_software_id);

        $this->updateDatabaseInvoiceFromValueObject->execute($invoice, $invoiceData);

        return $invoiceData;
    }
}
