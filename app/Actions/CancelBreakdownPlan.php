<?php

namespace App\Actions;

use App\Models\BreakdownPlan;
use App\Notifications\CustomerBreakdownPlanCancelled;
use App\Notifications\DealerBreakdownPlanCancelled;

class CancelBreakdownPlan
{
    public function execute(BreakdownPlan $breakdownPlan): void
    {
        $breakdownPlan->update(['cancelled_at' => now()]);

        $breakdownPlan->sale->customer->notify(new CustomerBreakdownPlanCancelled($breakdownPlan));
        $breakdownPlan->sale->dealership->notify(new DealerBreakdownPlanCancelled($breakdownPlan));
    }
}
