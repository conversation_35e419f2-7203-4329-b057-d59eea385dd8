<?php

namespace App\Actions;

use App\DataTransferObjects\ClaimValidationResult;
use App\Models\Claim;
use App\Services\VehicleLookupService\VehicleVinMismatch;

class ValidateClaim
{
    public function __construct(
        private readonly FetchMotHistory $fetchMotHistory,
        private readonly RecheckVehicleData $recheckVehicleData,
    ) {}

    public function execute(Claim $claim): ClaimValidationResult
    {
        $sale = $claim->warranty->sale;
        $vehicle = $sale->vehicle;

        // Fetch latest data
        $this->fetchMotHistory->execute($vehicle);

        if (! $vehicle->vehicle_lookup_last_checked_at || $vehicle->vehicle_lookup_last_checked_at->isBefore($claim->created_at)) {
            try {
                $this->recheckVehicleData->execute($vehicle);
                $keeperChangeChecked = true;
            } catch (VehicleVinMismatch $e) {
                // Usually means the vehicle lookup failed or the VIN does not match
                // due to private plate changes.
                $keeperChangeChecked = false;
            }
        } else {
            $keeperChangeChecked = true;
        }

        $lastOwnerChange = $vehicle->ownerChanges()
            ->select('start_of_ownership')
            ->latest('start_of_ownership')
            ->first()?->start_of_ownership;

        $latestMot = $vehicle->motTests()
            ->valid()
            ->where('completed_at', '<', $claim->failure_date)
            ->latest('expiry_date')
            ->first();

        return new ClaimValidationResult(
            validMileage: $claim->current_mileage < $claim->warranty->mileage_cutoff,
            validMot: (bool) $latestMot,
            keeperChangeChecked: $keeperChangeChecked,
            validOwnership: (bool) $lastOwnerChange?->lte($sale->start_date),
            validVinMatch: $sale->vin === $claim->warranty->sale->vin,
            needsServiceProof: $this->checkNeedsServiceProof($sale, $claim),
            customerAcceptedTerms: (bool) $claim->terms_accepted_at,
            lastOwnerChangeDate: $lastOwnerChange?->toDateString(),
            motExpiryDate: $latestMot?->expiry_date?->toDateString(),
        );
    }

    private function checkNeedsServiceProof($sale, $claim): bool
    {
        if (! $sale->last_service_date || $sale->last_service_mileage === null) {
            return true;
        }

        if ($sale->last_service_date->addMonths(12)->isBefore($claim->failure_date)) {
            return true;
        }

        if (! $sale->last_service_mileage + 12000 < $claim->failure_mileage) {
            return true;
        }

        return false;
    }
}
