<?php

namespace App\Actions;

use App\Models\CoverLevel;
use App\Models\Sale;
use App\Services\Tax\VatCalculator;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\View;
use Wnx\SidecarBrowsershot\BrowsershotLambda;

class GenerateSaleDocument
{
    public function __construct(private readonly GenerateQrCode $qrCodeGenerator) {}

    public function generate(Sale $sale, bool $preview = false): string
    {
        if (! $sale->confirmed_at) {
            abort(403);
        }

        $coverLevels = collect();
        $documents = collect();
        if ($sale->warranty) {
            $coverLevels = CoverLevel::query()
                ->where('vehicle_type', $sale->warranty->product->coverLevel->vehicle_type)
                ->where('id', '>=', $sale?->warranty->product->coverLevel->id)
                ->when(! $sale->warranty->isRecurring(), fn ($q) => $q->limit(1))
                ->get();

            $documents = $coverLevels
                ->map(function (CoverLevel $coverLevel) use ($sale) {
                    if ($coverLevel->document) {
                        return $coverLevel->document->getContent($sale->confirmed_at);
                    }
                    throw new \UnexpectedValueException("Document not found for Cover Level: {$coverLevel->id}");
                });
        }

        if ($sale->isRecurring() && $sale->billingRequest?->mandate_url) {
            $qrCode = $this->qrCodeGenerator->execute($sale->billingRequest->mandate_url);
        }

        $view = View::first(
            ["pdf.{$sale->account_id}.main", 'pdf.default.main'],
            [
                'isPreview' => $preview,
                'sale' => $sale,
                'qrCode' => $qrCode ?? null,
                'coverLevels' => $coverLevels,
                'documents' => $documents,
                'vatCalculator' => app(VatCalculator::class),
            ]
        );

        if ($preview) {
            return $view->render();
        }

        $output = BrowsershotLambda::html($view->render())
            ->format('A4')
            ->waitUntilNetworkIdle()
            ->scale(0.75)
            ->pdf();

        return response($output, Response::HTTP_OK, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => "inline; filename=\"warranty_{$sale->id}.pdf\"",
        ]);
    }
}
