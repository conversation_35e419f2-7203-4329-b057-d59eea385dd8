<?php

namespace App\Actions;

use App\Models\Dealership;
use Carbon\Carbon;

class InvoiceDealershipAndCreateGoCardlessPaymentForMonth
{
    public function __construct(
        private readonly GenerateMonthlyInvoiceToDealership $generateMonthlyInvoiceToDealership,
        private readonly SyncInvoiceWithAccountingSoftware $syncInvoiceWithAccountsSoftware,
        private readonly GenerateInvoicePaymentForDealership $generateInvoicePaymentForDealership,
    ) {}

    public function execute(Dealership $dealership, Carbon $start): void
    {
        $invoice = $this->generateMonthlyInvoiceToDealership->execute($dealership, $start);

        if (! $invoice) {
            // No invoice generated, so no payment needed
            return;
        }

        $this->syncInvoiceWithAccountsSoftware->onQueue()->execute($invoice, 'AUTHORISED');
        $this->generateInvoicePaymentForDealership->execute($invoice, sendToPaymentProvider: true);
    }
}
