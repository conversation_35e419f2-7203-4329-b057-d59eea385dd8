<?php

namespace App\Actions;

use App\Enums\PaymentProvider;
use App\Filament\Resources\PaymentResource;
use App\Models\Payment;
use App\Models\PaymentLineItem;
use App\Services\Accounting\AccountingService;
use App\Services\Accounting\BankTransactionData;
use App\Services\Accounting\BankTransferLineItemData;
use App\Services\Accounting\ContactData;
use Spatie\QueueableAction\QueueableAction;

class CreateXeroBankTransactionForPayment
{
    use QueueableAction;

    public function __construct(
        protected AccountingService $accountingService,
    ) {}

    public function execute(Payment $payment): BankTransactionData
    {
        if ($payment->accounting_software_bank_transfer_id) {
            throw new \RuntimeException("Payment {$payment->id} already has an accounting software bank transfer ID.");
        }

        if (! in_array($payment->status, [Payment::STATUS_PAID_OUT, Payment::STATUS_PAID])) {
            throw new \RuntimeException("Payment {$payment->id} is not paid.");
        }

        $paymentData = $this->accountingService->createBankTransaction(new BankTransactionData(
            id: null,
            contact: new ContactData(
                id: config('services.xero.subscription_contact_id')
            ),
            type: 'RECEIVE',
            status: 'AUTHORISED',
            date: $payment->payout->arrival_date ?? $payment->charge_date,
            reference: sprintf('Payment #%s', $payment->getKey()),
            url: PaymentResource::getUrl('view', ['record' => $payment]),
            bankAccountId: match ($payment->provider) {
                PaymentProvider::GO_CARDLESS => config('services.xero.go_cardless_bank_account_id'),
                PaymentProvider::ACCESS_PAYSUITE => config('services.xero.access_paysuite_bank_account_id'),
            },
            lineItems: $payment->lineItems->map(fn (PaymentLineItem $lineItem) => new BankTransferLineItemData(
                quantity: 1,
                unitAmount: $lineItem->unit_amount,
                taxAmount: $lineItem->tax,
                accountCode: $lineItem->account_code,
                description: $lineItem->description,
            ))
        ));

        $payment->update([
            'accounting_software_bank_transfer_id' => $paymentData->id,
        ]);

        return $paymentData;
    }
}
