<?php

namespace App\Actions;

use App\Models\Warranty;
use App\Notifications\CustomerWarrantyCancelled;
use App\Notifications\DealerWarrantyCancelled;

class CancelWarranty
{
    public function execute(Warranty $warranty): void
    {
        $warranty->update(['cancelled_at' => now()]);

        $warranty->sale->customer->notify(new CustomerWarrantyCancelled($warranty));
        $warranty->sale->dealership->notify(new DealerWarrantyCancelled($warranty));
    }
}
