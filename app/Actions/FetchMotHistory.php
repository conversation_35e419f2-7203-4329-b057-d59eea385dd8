<?php

namespace App\Actions;

use App\Models\Vehicle;
use App\Services\MotHistoryService\MotHistoryService;
use Carbon\Carbon;
use Spatie\QueueableAction\QueueableAction;

class FetchMotHistory
{
    use QueueableAction;

    public function __construct(private readonly MotHistoryService $motHistoryService) {}

    public function execute(Vehicle $vehicle): void
    {
        if ($vehicle->mot_last_checked_at && $vehicle->mot_last_checked_at->diffInDays() < 1) {
            return;
        }

        try {
            if ($vehicle->vin) {
                $motHistory = $this->motHistoryService->lookupVin($vehicle->vin);
                if (strcasecmp($motHistory['registration'], $vehicle->vrm) !== 0
                    && strcasecmp($motHistory['registration'], $vehicle->private_plate) !== 0) {
                    // VRM does not match vrm or private plate, so update the vehicle
                    if ($vehicle->private_plate) {
                        // Private plate is currently set so we expect the VRM to be an original plate
                        $vehicle->update([
                            'vrm' => $motHistory['registration'],
                            'private_plate' => null,
                        ]);
                    } else {
                        // This is most likely a cherished transfer, so keep the original VRM and
                        // set the private plate to the new VRM
                        $vehicle->update([
                            'private_plate' => $motHistory['registration'],
                        ]);
                    }
                }
            } else {
                $motHistory = $this->motHistoryService->lookupVrm($vehicle->private_plate ?: $vehicle->vrm);
            }

            foreach ($motHistory['motTests'] ?? [] as $item) {
                $motTest = $vehicle->motTests()->firstOrCreate([
                    'test_number' => $item['motTestNumber'],
                ], [
                    'completed_at' => Carbon::parse($item['completedDate']),
                    'expiry_date' => $item['expiryDate'],
                    'odometer_reading' => $item['odometerValue'],
                    'odometer_unit' => $item['odometerUnit'],
                    'result' => $item['testResult'],
                    'data_source' => $item['dataSource'],
                ]);

                if ($motTest->wasRecentlyCreated) {
                    foreach ($item['defects'] as $defect) {
                        $motTest->defects()->create([
                            'type' => $defect['type'],
                            'description' => $defect['text'],
                            'dangerous' => $defect['dangerous'],
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            if (app()->isLocal()) {
                throw $e;
            }
            report($e);
        }

        $vehicle->update([
            'mot_last_checked_at' => Carbon::now(),
        ]);
    }
}
