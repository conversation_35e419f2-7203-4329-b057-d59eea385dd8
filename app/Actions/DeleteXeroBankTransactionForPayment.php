<?php

namespace App\Actions;

use App\Models\Payment;
use App\Services\Accounting\AccountingService;
use Spatie\QueueableAction\QueueableAction;

class DeleteXeroBankTransactionForPayment
{
    use QueueableAction;

    public function __construct(
        protected AccountingService $accountingService,
    ) {}

    public function execute(Payment $payment): void
    {
        if (! $payment->accounting_software_bank_transfer_id) {
            throw new \RuntimeException('Payment does not have a Xero Bank Transaction.');
        }

        $this->accountingService->deleteBankTransaction($payment->accounting_software_bank_transfer_id);

        $payment->update([
            'accounting_software_bank_transfer_id' => null,
        ]);
    }
}
