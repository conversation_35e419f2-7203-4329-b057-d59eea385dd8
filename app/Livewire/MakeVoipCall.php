<?php

namespace App\Livewire;

use App\Actions\MakeVoipCall as MakeVoipCallAction;
use App\Models\Concerns\VoipCallable;
use Livewire\Attributes\Computed;
use Livewire\Component;

class MakeVoipCall extends Component
{
    public VoipCallable $voipCallable;

    #[Computed]
    public function canMakeCall(): bool
    {
        return auth()->user()->voipUser()->exists();
    }

    public function makeCall(MakeVoipCallAction $makeVoipCallAction)
    {
        if (! $this->canMakeCall) {
            return;
        }

        $makeVoipCallAction->execute(auth()->user(), $this->voipCallable);
    }

    public function render()
    {
        return view('livewire.make-voip-call');
    }
}
