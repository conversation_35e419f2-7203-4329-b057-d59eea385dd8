<?php

namespace App\Livewire\CustomerPortal;

use Flux\Flux;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class UpdateName extends Component
{
    public bool $isUpdating = false;

    public string $first_name = '';

    public string $last_name = '';

    public function mount()
    {
        $this->first_name = Auth::user()->first_name;
        $this->last_name = Auth::user()->last_name;
    }

    public function save()
    {
        $validData = $this->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
        ]);

        Auth::user()->update($validData);

        Flux::toast('Thank you for updating your name.', variant: 'success');

        $this->isUpdating = false;
    }

    public function render()
    {
        return view('livewire.customer-portal.update-name');
    }
}
