<?php

namespace App\Livewire\CustomerPortal;

use App\Livewire\Forms\AddressForm;
use Flux\Flux;
use Livewire\Component;

class UpdateAddress extends Component
{
    public AddressForm $form;

    public bool $isUpdating = false;

    public function mount()
    {
        $this->form->setCustomer(auth()->user());
    }

    public function save()
    {
        $this->form->update();

        Flux::toast('Thank you for updating your address.', variant: 'success');

        $this->isUpdating = false;
    }

    public function render()
    {
        return view('livewire.customer-portal.update-address');
    }
}
