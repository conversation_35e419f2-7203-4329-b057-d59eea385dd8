<?php

namespace App\Livewire\CustomerPortal;

use App\Models\Account;
use App\Models\FaultType;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

class CreateClaim extends Component
{
    public array $checklist = [];

    public ?string $sale_id = null;

    public string $failure_date = '';

    public string $current_mileage = '';

    public string $fault_type_id = '';

    public string $fault_description = '';

    public string $vehicle_location = '';

    public function mount()
    {
        if ($this->validSales->count() === 1) {
            $this->sale_id = $this->validSales->first()->id;
        }
    }

    #[Computed]
    public function faultTypes()
    {
        return FaultType::pluck('name', 'id');
    }

    #[Computed]
    public function checklistItems()
    {
        $account = Account::find(1);

        return [
            'vehicle_servicing_requirements' => Lang::get('claims.checklist.vehicle_servicing_requirements'),
            'max_hourly_labour_rate' => Lang::get('claims.checklist.max_hourly_labour_rate', ['max_hourly_labour_rate' => $account->max_hourly_labour_rate]),
            'diagnostic_charge' => Lang::get('claims.checklist.diagnostic_charge'),
        ];
    }

    #[Computed]
    public function validSales(): Collection
    {
        return Auth::user()->sales()
            ->whereHas('warranty', fn ($q) => $q->live())
            ->get();
    }

    public function save()
    {
        $validData = $this->validate([
            'failure_date' => ['required', 'date', 'before:tomorrow', 'after:two_weeks_ago'],
            'current_mileage' => ['required', 'numeric', 'min:1'], // TODO
            'fault_type_id' => ['required', 'exists:fault_types,id'],
            'fault_description' => ['required', 'string', 'max:500'],
            'vehicle_location' => ['required', 'string', 'max:500'],
        ]);

        $customer = Auth::user();

        $sale = $customer->sales()->find($this->sale_id);

        $claim = $sale->warranty->claims()->create([
            ...$validData,
            'terms_accepted_at' => now(),
        ]);

        $this->redirectRoute('customer-portal.claims.show', $claim, navigate: true);
    }

    #[Title('Make a claim')]
    public function render()
    {
        return view('livewire.customer-portal.create-claim');
    }
}
