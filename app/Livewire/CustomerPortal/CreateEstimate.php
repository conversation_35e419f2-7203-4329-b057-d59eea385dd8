<?php

namespace App\Livewire\CustomerPortal;

use App\Models\Claim;
use App\Services\Tax\VatCalculator;
use Livewire\Component;

class CreateEstimate extends Component
{
    public array $checklist = [];

    public ?string $workshop_name = null;

    public ?string $workshop_contact = null;

    public ?string $workshop_phone = null;

    public ?string $workshop_email = null;

    public ?string $workshop_address = null;

    public ?string $work_required = null;

    public ?string $total_parts = null;

    public ?string $total_labour = null;

    public ?string $vat = null;

    public ?string $total = null;

    public Claim $claim;

    public function updated($propertyName, VatCalculator $vatCalculator)
    {
        if (in_array($propertyName, ['total_parts', 'total_labour'])) {
            $this->total_parts = number_format((float) $this->total_parts, 2, thousands_separator: '');
            $this->total_labour = number_format((float) $this->total_labour, 2, thousands_separator: '');
            $this->vat = number_format($vatCalculator->getVatAmount($this->total_parts + $this->total_labour), 2, thousands_separator: '');
        }
        if ($propertyName == 'vat') {
            $this->vat = number_format((float) $this->vat, 2, thousands_separator: '');
        }

        $this->total = number_format((float) $this->total_parts + $this->total_labour + $this->vat, 2, thousands_separator: '');
    }

    public function save()
    {
        $validData = $this->validate([
            'workshop_name' => ['required', 'string', 'max:255'],
            'workshop_contact' => ['required', 'string', 'max:255'],
            'workshop_phone' => ['required', 'string', 'max:255'],
            'workshop_email' => ['required', 'email', 'max:255'],
            'workshop_address' => ['required', 'string', 'max:1000'],
            'work_required' => ['required', 'string', 'max:1000'],
            'total_parts' => ['required', 'numeric', 'max:99999'],
            'total_labour' => ['required', 'numeric', 'max:99999'],
            'vat' => ['required', 'numeric', 'max:99999'],
        ]);

        $this->claim->estimates()->create($validData);

        $this->redirectRoute('claims.show', $this->claim, navigate: true);
    }

    public function render()
    {
        return view('livewire.customer-portal.create-estimate');
    }
}
