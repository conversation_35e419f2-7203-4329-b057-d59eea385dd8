<?php

namespace App\Livewire\CustomerPortal;

use App\Models\Claim;
use Flux\Flux;
use Livewire\Component;

class ViewClaim extends Component
{
    public Claim $claim;

    public function acceptTerms()
    {
        $this->claim->update([
            'terms_accepted_at' => now(),
        ]);

        Flux::toast('Thanks for accepting the terms, we will get back to you soon regarding the progress of your claim.', variant: 'success');
    }

    public function render()
    {
        return view('livewire.customer-portal.view-claim');
    }
}
