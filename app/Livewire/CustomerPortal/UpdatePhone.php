<?php

namespace App\Livewire\CustomerPortal;

use Flux\Flux;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class UpdatePhone extends Component
{
    public bool $isUpdating = false;

    public string $phone = '';

    public function mount()
    {
        $this->phone = Auth::user()->phone;
    }

    public function save()
    {
        $this->validate([
            'phone' => 'required|digits:11',
        ]);

        Auth::user()->update([
            'phone' => $this->phone,
        ]);

        Flux::toast('Thank you for updating your phone number.', variant: 'success');

        $this->isUpdating = false;
    }

    public function render()
    {
        return view('livewire.customer-portal.update-phone');
    }
}
