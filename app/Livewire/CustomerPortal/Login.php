<?php

namespace App\Livewire\CustomerPortal;

use App\Models\Customer;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\RequiredIf;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use RateLimiter;

class Login extends Component
{
    #[Validate('required|string')]
    public string $username = '';

    #[Validate('required|string')]
    public string $password = '';

    #[Validate('required|string|size:6')]
    public string $otp = '';

    #[Validate('boolean')]
    public bool $remember = false;

    public bool $isEmail = false;

    public bool $isSms = false;

    public function resetForm(): void
    {
        $this->username = '';
        $this->password = '';
        $this->otp = '';
        $this->remember = false;
        $this->isEmail = false;
        $this->isSms = false;
    }

    /**
     * Check form username for email or phone
     */
    public function check(): void
    {
        // Temporarily disallow login until we launch
        // the customer portal to the public

        $this->addError('username', 'Login is currently disabled');

        return;

        if ($this->isEmail || $this->isSms) {
            $this->attemptLogin();

            return;
        }

        $this->username = str_replace(' ', '', $this->username);

        $this->validate([
            'username' => ['required', 'string'],
        ]);

        $this->isEmail = $this->isSms = false;

        if (filter_var($this->username, FILTER_VALIDATE_EMAIL)) {
            $this->isEmail = true;
        } elseif ($this->isUkMobileNumber()) {
            $this->isSms = true;
        } else {
            $this->addError('username', 'Invalid email or phone number');

            return;
        }

        $this->sendOTPMessage();
    }

    protected function isUkMobileNumber(): bool
    {
        return (bool) preg_match('/^447\d{9}$/', $this->normalisedSmsNumber());
    }

    /**
     * Handle an incoming authentication request.
     */
    public function attemptLogin(): void
    {
        $this->authenticate();

        Session::regenerate();

        $this->redirectIntended(default: route('home', absolute: false), navigate: true);
    }

    /**
     * Attempt to authenticate the request's credentials.
     *
     * @throws ValidationException
     */
    public function authenticate(): void
    {
        $this->validate([
            'username' => ['required', 'string'],
            'otp' => [new RequiredIf($this->isSms), 'string', 'size:6'],
        ]);

        $this->ensureIsNotRateLimited();

        $this->authenticateOtp();

        RateLimiter::clear($this->throttleKey());
    }

    /**
     * Attempt to authenticate the request's credentials.
     *
     * @throws ValidationException
     */
    protected function authenticateEmail(): void
    {
        if (
            ! Auth::guard('customer')->attempt([
                'email' => $this->username,
                'password' => $this->password,
            ], $this->remember)
        ) {
            RateLimiter::hit($this->throttleKey());

            throw ValidationException::withMessages([
                'username' => trans('auth.failed'),
            ]);
        }
    }

    protected function sendOTPMessage()
    {
        if (! $this->isSms && ! $this->isEmail) {
            throw new \RuntimeException('Missing email or phone number');
        }

        RateLimiter::hit($this->throttleKey());

        $user = Customer::query()
            ->when($this->isEmail, fn ($query) => $query->where('email', $this->username))
            ->when($this->isSms, fn ($query) => $query->where('phone', $this->normalisedSmsNumber()))
            ->first();

        if ($user) {
            try {
                app(\App\Actions\SendOTPAuthenticationCode::class)->execute($user);
            } catch (\RuntimeException $e) {
                //
            }
        }
    }

    protected function authenticateOtp()
    {
        $user = Customer::query()
            ->when($this->isEmail, fn ($query) => $query->where('email', $this->username))
            ->when($this->isSms, fn ($query) => $query->where('phone', $this->normalisedSmsNumber()))
            ->first();

        $passcodes = $user?->oneTimePasscodes()->valid()->get() ?: [];

        foreach ($passcodes as $passcode) {
            if (Hash::check($this->otp, $passcode->passcode)) {
                $passcode->delete();
                Auth::guard('customer')->login($user, $this->remember);

                return;
            }
        }

        throw ValidationException::withMessages([
            'username' => trans('auth.failed'),
        ]);
    }

    /**
     * Ensure the authentication request is not rate limited.
     */
    protected function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout(request()));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'username' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the authentication rate limiting throttle key.
     */
    protected function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->username).'|'.request()->ip());
    }

    protected function normalisedSmsNumber(): string
    {
        return preg_replace('/^0/', '44', $this->username);
    }

    #[Title('Customer Portal Login')]
    public function render()
    {
        return view('livewire.customer-portal.login')->layout('components.layouts.auth');
    }
}
