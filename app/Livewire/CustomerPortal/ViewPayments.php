<?php

namespace App\Livewire\CustomerPortal;

use App\Actions\SetupBillingRequest;
use App\Models\Sale;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

class ViewPayments extends Component
{
    #[Computed]
    public function sales()
    {
        return auth()->user()
            ->sales()
            ->with([
                'dealership',
                'vehicle',
                'payments',
                'payLaterAgreement',
                'warranty',
                'breakdownPlan',
                'servicePlan',
            ])
            ->latest()
            ->get();
    }

    public function setupDirectDebit(Sale $sale, SetupBillingRequest $setupBillingRequest)
    {
        $setupBillingRequest->execute($sale, sendNotification: false);

        $this->redirect($sale->getCompletePaymentUrl());
    }

    #[Title('View Payments')]
    public function render()
    {
        return view('livewire.customer-portal.view-payments');
    }
}
