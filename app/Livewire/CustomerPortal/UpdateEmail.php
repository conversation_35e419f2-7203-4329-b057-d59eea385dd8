<?php

namespace App\Livewire\CustomerPortal;

use Flux\Flux;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Livewire\Component;

class UpdateEmail extends Component
{
    public bool $isUpdating = false;

    public string $email = '';

    public function mount()
    {
        $this->email = Auth::user()->email;
    }

    public function save()
    {
        $this->validate([
            'email' => [
                'required',
                'string',
                'email',
                'max:100',
                Rule::unique('customers')
                    ->where('account_id', Auth::user()->account_id)
                    ->ignore(Auth::user()),
            ],
        ]);

        Auth::user()->update([
            'email' => $this->email,
        ]);

        Flux::toast('Thank you for updating your email address.', variant: 'success');

        $this->isUpdating = false;
    }

    public function render()
    {
        return view('livewire.customer-portal.update-email');
    }
}
