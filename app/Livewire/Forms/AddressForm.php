<?php

namespace App\Livewire\Forms;

use App\Models\Customer;
use Livewire\Form;

class AddressForm extends Form
{
    public ?Customer $customer;

    public $address_1 = '';

    public $address_2 = '';

    public $city = '';

    public $county = '';

    public $country = '';

    public $postcode = '';

    public function setCustomer(Customer $customer)
    {
        $this->customer = $customer;

        $this->address_1 = $customer->address_1;
        $this->address_2 = $customer->address_2;
        $this->city = $customer->city;
        $this->county = $customer->county;
        $this->country = $customer->country;
        $this->postcode = $customer->postcode;
    }

    public function rules(): array
    {
        return [
            //            'first_name' => ['required', 'string', 'max:100'],
            //            'last_name' => ['required', 'string', 'max:100'],
            'address_1' => ['required', 'string', 'max:150'],
            'address_2' => ['nullable', 'string', 'max:150'],
            'city' => ['required', 'string', 'max:50'],
            'county' => ['required', 'string', 'max:50'],
            'country' => ['required', 'string', 'max:100'],
            'postcode' => ['required', 'string', 'max:25'],
        ];
    }

    public function update()
    {
        $this->customer->update($this->validate());
    }
}
