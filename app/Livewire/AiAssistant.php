<?php

namespace App\Livewire;

use App\Models\AiRequest;
use App\Models\AiTask;
use App\Services\AI\AiTaskProcessor;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Components\Livewire;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Computed;
use Livewire\Component;
use OpenAI\Exceptions\ErrorException;

class AiAssistant extends Component implements HasForms, HasInfolists
{
    use InteractsWithForms;
    use InteractsWithInfolists;

    public Model $record;

    public AiTask $taskModel;

    public AiRequest $AiRequest;

    public string $title;

    public static function infolistSection(string $title, string $task)
    {
        return Livewire::make(static::class, [
            'title' => $title,
            'task' => $task,
        ])->key($task)->lazy();
    }

    public function mount(string $title, string $task)
    {
        $this->title = $title;
        $this->taskModel = AiTask::where('processor_class', $task)->first();
    }

    public function processorClass(): AiTaskProcessor
    {
        return $this->taskModel->getProcessor($this->record);
    }

    #[Computed]
    public function latestRequest(): ?AiRequest
    {
        try {
            return $this->taskModel->fetchOrProcessRequest($this->record)->load('vote');
        } catch (ErrorException $e) {
            if (app()->environment('local') || app()->runningInConsole()) {
                throw $e;
            }
            report($e);

            return null;
        }
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->state($this->latestRequest?->response)
            ->schema($this->processorClass()->getInfolistSchema());
    }

    public function retry()
    {
        $this->taskModel->reprocessRequest($this->record);
    }

    public function voteUp()
    {
        $this->latestRequest->voteUp(Auth::user());
        $this->latestRequest->load('vote');
    }

    public function voteDown()
    {
        $this->latestRequest->voteDown(Auth::user());
        $this->latestRequest->load('vote');
    }

    public function placeholder(array $params = [])
    {
        return view('livewire.ai-assistant.placeholder', $params);
    }

    public function render()
    {
        return view('livewire.ai-assistant.index');
    }
}
