<?php

namespace App\Livewire\RepairerPortal;

use App\Models\ClaimEstimate;
use App\Models\ClaimEstimateLineItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Livewire\Attributes\Layout;
use Livewire\Component;

class CreateEstimate extends Component
{
    public $vrm = '';

    public $vehicleDetails = [
        'make' => '',
        'model' => '',
        'year' => '',
        'engineSize' => '',
    ];

    public $date;

    public $description = '';

    public $lineItems = [];

    public $hasGarageProfile = true;

    public function mount()
    {
        // Check if garage profile exists
        //        $profile = session('garageProfile');
        //        if ($profile) {
        //            $this->hasGarageProfile = true;
        //        } else {
        //            session()->flash('warning', 'Please set up your garage profile first');
        //            return redirect()->route('garage-profile');
        //        }

        // Initialize with today's date
        $this->date = Carbon::now()->format('Y-m-d');

        // Add empty line item to array
        $this->addLineItem();
    }

    public function handleVrmLookup()
    {
        if (! $this->vrm) {
            session()->flash('error', 'Please enter a vehicle registration');

            return;
        }

        // Validate VRM format (basic UK format validation)
        if (! preg_match('/^[A-Z0-9]{2,7}$/i', $this->vrm)) {
            session()->flash('error', 'Please enter a valid UK registration');

            return;
        }

        session()->flash('info', 'Looking up vehicle details...');

        // In a real application, this would call a VRM lookup API
        // For demo purposes, we'll mock the response
        // You could use: $response = Http::get('https://vrm-api.example.com?vrm=' . $this->vrm);

        // Mock response for demo
        $this->vehicleDetails = [
            'make' => 'Ford',
            'model' => 'Focus',
            'year' => '2020',
            'engineSize' => '1.5L',
        ];

        session()->flash('success', 'Vehicle details found!');
    }

    public function addLineItem()
    {
        $this->lineItems[] = [
            'id' => uniqid(),
            'quantity' => 1,
            'type' => 'parts',
            'description' => '',
            'netCost' => 0,
            'vatRate' => 20,
            'vatAmount' => 0,
            'totalAmount' => 0,
        ];
    }

    public function updateLineItem($id, $field, $value)
    {
        foreach ($this->lineItems as $index => $item) {
            if ($item['id'] === $id) {
                $this->lineItems[$index][$field] = $value;

                // Recalculate amounts if necessary
                if (in_array($field, ['netCost', 'quantity', 'vatRate'])) {
                    $netCost = (float) $this->lineItems[$index]['netCost'];
                    $quantity = (int) $this->lineItems[$index]['quantity'];
                    $vatRate = (float) $this->lineItems[$index]['vatRate'];

                    $totalNet = $netCost * $quantity;
                    $vatAmount = $totalNet * ($vatRate / 100);
                    $totalAmount = $totalNet + $vatAmount;

                    $this->lineItems[$index]['vatAmount'] = round($vatAmount, 2);
                    $this->lineItems[$index]['totalAmount'] = round($totalAmount, 2);
                }

                break;
            }
        }
    }

    public function removeLineItem($id)
    {
        $this->lineItems = array_filter($this->lineItems, function ($item) use ($id) {
            return $item['id'] !== $id;
        });
    }

    public function calculateTotal()
    {
        return array_reduce($this->lineItems, function ($total, $item) {
            return $total + $item['totalAmount'];
        }, 0);
    }

    public function submit(): void
    {
        // Validate form
        $this->validate([
            'vrm' => 'required|regex:/^[A-Z0-9]{2,7}$/i',
            'date' => 'required|date',
            'description' => 'required|string',
            'lineItems' => 'required|array|min:1',
        ], [
            'vrm.required' => 'Please enter a vehicle registration',
            'vrm.regex' => 'Please enter a valid UK registration',
            'lineItems.min' => 'Please add at least one line item',
        ]);

        if (empty($this->vehicleDetails['make'])) {
            session()->flash('error', 'Please lookup the vehicle registration to continue.');

            return;
        }

        // Validate line items
        $invalidLineItems = array_filter($this->lineItems, function ($item) {
            return empty($item['description']) || $item['netCost'] <= 0;
        });

        if (count($invalidLineItems) > 0) {
            session()->flash('error', 'Please complete all line items with descriptions and costs.');

            return;
        }

        // Create the claim estimate
        $claimEstimate = ClaimEstimate::create([
            'vrm' => $this->vrm,
            'vehicle_make' => $this->vehicleDetails['make'],
            'vehicle_model' => $this->vehicleDetails['model'],
            'vehicle_year' => $this->vehicleDetails['year'],
            'vehicle_engine_size' => $this->vehicleDetails['engineSize'],
            'date' => $this->date,
            'description' => $this->description,
            'total_amount' => $this->calculateTotal(),
        ]);

        // Create the line items
        foreach ($this->lineItems as $item) {
            ClaimEstimateLineItem::create([
                'claim_estimate_id' => $claimEstimate->id,
                'quantity' => $item['quantity'],
                'type' => $item['type'],
                'description' => $item['description'],
                'net_cost' => $item['netCost'],
                'vat_rate' => $item['vatRate'],
                'vat_amount' => $item['vatAmount'],
                'total_amount' => $item['totalAmount'],
            ]);
        }

        session()->flash('success', 'Repair job saved successfully!');

        // Redirect to summary page
        $this->redirectRoute('summary', ['id' => $claimEstimate->id]);
    }

    #[Layout('components.layouts.repairer-portal')]
    public function render()
    {
        return view('livewire.repairer-portal.create-estimate', [
            'total' => $this->calculateTotal(),
        ]);
    }
}
