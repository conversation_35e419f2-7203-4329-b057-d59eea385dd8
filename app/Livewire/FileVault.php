<?php

namespace App\Livewire;

use App\Models\File;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Gate;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class FileVault extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public ?array $data = [];

    public Model $record;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->record->files()->getQuery())
            ->queryStringIdentifier('file-vault')
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('size')
                    ->formatStateUsing(fn (File $file) => $file->getSizeForHumans())
                    ->alignRight(),
                IconColumn::make('download')
                    ->default(fn (File $file) => ! $file->trashed())
                    ->icon(fn (File $file) => $file->trashed() ? null : 'heroicon-o-arrow-down-tray')
                    ->alignRight()
                    ->url(fn (File $file) => $file->trashed() ? null : $file->getTemporaryDownloadUrl())
                    ->openUrlInNewTab(),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->actions([
                ActionGroup::make([
                    Action::make('rename_file')
                        ->label('Rename')
                        ->icon('heroicon-o-pencil')
                        ->modalWidth('md')
                        ->form([
                            TextInput::make('name')
                                ->placeholder('Enter the new name/')
                                ->default(fn (File $file) => $file->name)
                                ->required()
                                ->maxLength(255),
                        ])
                        ->action(function (File $file, array $data, Action $action) {
                            $file->update($data);
                            $action->success();
                        })
                        ->successNotificationTitle('File renamed successfully'),
                    RestoreAction::make(),
                    DeleteAction::make(),
                ]),
            ])
            ->bulkActions(
                BulkActionGroup::make([
                    RestoreBulkAction::make(),
                    DeleteBulkAction::make(),
                ]),
            );
    }

    public function form(Form $form): Form
    {
        if (! auth()->user()->can('create', config('filament-comments.comment_model'))) {
            return $form;
        }

        return $form
            ->schema([
                FileUpload::make('files')
                    ->hiddenLabel()
                    ->multiple()
                    ->afterStateUpdated(function (array $state) {
                        /** @var TemporaryUploadedFile $file */
                        foreach ($state as $file) {
                            $this->record->files()->create([
                                'account_id' => $this->record->getAccountId(),
                                'name' => $file->getClientOriginalName(),
                                'path' => $file->store('vault'),
                                'size' => $file->getSize(),
                                'mime_type' => $file->getMimeType(),
                            ]);
                        }
                        $this->form->fill();
                    })
                    ->deletable(false),
            ])
            ->statePath('data');
    }

    public function canUploadFiles(): bool
    {
        return Gate::allows('fileUpload', $this->record);
    }

    public function render()
    {
        return view('packages.file_vault.livewire.file-vault');
    }
}
