<?php

namespace App\Livewire\Tables;

use App\Enums\ClaimEstimateLineItemType;
use App\Models\ClaimAuthorisation;
use App\Models\ClaimEstimate;
use App\Models\ClaimEstimateLineItem;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Livewire\Attributes\Computed;
use Livewire\Component;

class ClaimEstimateLineItemsTable extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public ClaimEstimate $claimEstimate;

    public bool $readonly = false;

    public bool $showTotals = true;

    #[Computed(persist: true)]
    public function canAuthorise(): bool
    {
        return Gate::allows('create', [ClaimAuthorisation::class, $this->claimEstimate])
            && ! $this->claimEstimate->claim->rejection()->exists()
            && ! $this->claimEstimate->authorisation()->exists();
    }

    public function table(Table $table): Table
    {
        return $table
            ->paginated(false)
            ->relationship(fn () => $this->claimEstimate->lineItems())
            ->columns([
                IconColumn::make('type')
                    ->icon(fn (ClaimEstimateLineItemType $state) => match ($state) {
                        ClaimEstimateLineItemType::PART => 'heroicon-s-cube',
                        ClaimEstimateLineItemType::LABOUR => 'heroicon-s-wrench-screwdriver',
                        ClaimEstimateLineItemType::CONSUMABLE => 'heroicon-s-bolt',
                    })
                    ->size('sm')
                    ->color(fn (ClaimEstimateLineItemType $state) => match ($state) {
                        ClaimEstimateLineItemType::PART => 'success',
                        ClaimEstimateLineItemType::LABOUR => 'warning',
                        ClaimEstimateLineItemType::CONSUMABLE => 'primary',
                    }),
                TextColumn::make('vehicleComponent.name')
                    ->label('Component')
                    ->placeholder('N/A'),
                TextColumn::make('description')->wrap(),
                TextColumn::make('quantity')
                    ->numeric()
                    ->alignRight(),
                TextColumn::make('amount')
                    ->money()
                    ->alignRight(),
                TextColumn::make('vat')
                    ->label('VAT')
                    ->money()
                    ->alignRight(),
                $this->customerContributionColumn(),
                TextColumn::make('total')
                    ->money()
                    ->alignRight(),
            ]);
    }

    private function customerContributionColumn()
    {
        if ($this->canAuthorise && ! $this->readonly) {
            return TextInputColumn::make('customer_contribution')
                ->label('Customer Contribution (Gross)')
                ->wrapHeader()
                ->tooltip('The gross total that the customer will be contributing to this line item.')
                ->visible($this->canAuthorise)
                ->type('number')
                ->rules(fn (ClaimEstimateLineItem $record) => [
                    'numeric',
                    'min:0',
                    'max:'.(($record->amount + $record->vat) * $record->quantity),
                ])
                ->extraInputAttributes(fn (ClaimEstimateLineItem $record) => [
                    'step' => '0.01',
                    'min' => 0,
                    'max' => (($record->amount + $record->vat) * $record->quantity),

                ])
                ->width(40)
                ->alignRight();
        }

        return TextColumn::make('customer_contribution')
            ->visible(! $this->canAuthorise)
            ->money()
            ->alignRight();
    }

    public function render()
    {
        return view('livewire.tables.claim-estimate-line-items-table');
    }
}
