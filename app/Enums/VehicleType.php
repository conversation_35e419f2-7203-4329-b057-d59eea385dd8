<?php

namespace App\Enums;

enum VehicleType: string
{
    case CAR = 'CAR';
    case VAN = 'VAN';
    case MOTORCYCLE = 'MOTORCYCLE';
    case MOTORHOME = 'MOTORHOME';

    public static function toSelectArray(): array
    {
        return [
            self::CAR->value => 'Car',
            self::VAN->value => 'Van',
            self::MOTORCYCLE->value => 'Motorcycle',
            self::MOTORHOME->value => 'Motorhome / Camper van',
        ];
    }
}
