<?php

namespace App\Enums;

enum FundType: int
{
    case MANAGED = 1;
    case DEALER = 2;
    case DEALER_RETAINED = 3;

    public static function dealerFundedValues()
    {
        return [self::DEALER, self::DEALER_RETAINED];
    }

    public function color()
    {
        return match ($this) {
            self::MANAGED => 'warning',
            self::DEALER, self::DEALER_RETAINED => 'gray',
        };
    }

    public function isDealerFunded(): bool
    {
        return in_array($this, [self::DEALER, self::DEALER_RETAINED]);
    }

    public function label()
    {
        return match ($this) {
            self::MANAGED => 'Managed Fund',
            self::DEALER => 'Dealer Fund',
            self::DEALER_RETAINED => 'Dealer Fund (Retained)',
        };
    }

    public static function toSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn (self $case) => [$case->value => $case->label()])->all();
    }
}
