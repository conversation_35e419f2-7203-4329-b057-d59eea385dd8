<?php

namespace App\Enums;

enum ClaimStatus: string
{
    case AWAITING_ESTIMATE = 'awaiting_estimate';
    case ESTIMATE_RECEIVED = 'estimate_received';
    case REJECTED = 'rejected';
    case AUTHORISED = 'authorised';
    case SETTLED = 'settled';

    public function color()
    {
        return match ($this) {
            self::AWAITING_ESTIMATE => 'warning',
            self::AUTHORISED => 'success',
            self::REJECTED, ClaimStatus::ESTIMATE_RECEIVED => 'danger',
            default => 'gray',
        };
    }

    public function colorCustomerPortal()
    {
        return match ($this) {
            self::AWAITING_ESTIMATE => 'yellow',
            self::AUTHORISED => 'green',
            self::REJECTED, self::ESTIMATE_RECEIVED => 'red',
            default => 'gray',
        };
    }
}
