<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum CallOutcome: string implements HasLabel
{
    case INTERESTED = 'Interested';
    case NOT_INTERESTED = 'Not interested';
    case CALLBACK = 'Callback';
    case NO_ANSWER = 'No answer';
    case ENGAGED = 'Engaged';
    case AUTO_ASSIGNED = 'Auto assigned';

    public function getLabel(): ?string
    {
        return $this->value;
    }

    public function color(): string
    {
        return match ($this) {
            self::INTERESTED => 'success',
            self::NOT_INTERESTED, self::NO_ANSWER => 'danger',
            default => 'warning',
        };
    }

    public function calendarColor(): string
    {
        return match ($this->color()) {
            'success' => '#27b211',
            'danger' => '#ff5733',
            'warning' => '#ffbd33',
        };
    }
}
