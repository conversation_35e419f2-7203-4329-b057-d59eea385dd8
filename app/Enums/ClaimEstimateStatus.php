<?php

namespace App\Enums;

enum ClaimEstimateStatus: string
{
    case PENDING = 'pending';
    case BOOKED = 'booked';
    case OVERDUE = 'overdue';
    case AWAITING_DECISION = 'awaiting_decision';
    case REJECTED = 'rejected';
    case AUTHORISED = 'authorised';
    case SETTLED = 'settled';

    public function color(): string
    {
        return match ($this) {
            self::REJECTED, self::AWAITING_DECISION, self::OVERDUE => 'danger',
            self::PENDING, ClaimEstimateStatus::AUTHORISED, self::SETTLED, ClaimEstimateStatus::BOOKED => 'success',
        };
    }
}
