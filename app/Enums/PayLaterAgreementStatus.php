<?php

namespace App\Enums;

enum PayLaterAgreementStatus: string
{
    case PENDING = 'pending';
    case PENDING_CAPTURE = 'pending_capture';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case EXPIRED = 'expired';

    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::PENDING_CAPTURE => 'Pending Capture',
            self::COMPLETED => 'Completed',
            self::FAILED => 'Failed',
            self::EXPIRED => 'Expired',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::PENDING, self::PENDING_CAPTURE => 'warning',
            self::COMPLETED => 'success',
            self::FAILED, self::EXPIRED => 'danger',
        };
    }

    public static function toSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn (self $case) => [$case->value => $case->label()])->all();
    }
}
