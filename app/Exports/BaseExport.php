<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

abstract class BaseExport implements FromQuery, WithHeadings, WithMapping
{
    use Exportable;

    protected array $filters = [];

    protected bool $showAccount = false;

    public function filters(array $filters)
    {
        $this->filters = $filters;

        return $this;
    }

    public function showAccount(bool $showAccount)
    {
        $this->showAccount = $showAccount;

        return $this;
    }
}
