<?php

namespace App\Exports;

use App\Enums\ClaimEstimateLineItemType;
use App\Models\Claim;
use App\Models\ClaimEstimate;
use App\Models\ClaimEstimateLineItem;
use Carbon\Carbon;

class ClaimsExport extends BaseExport
{
    protected array $filters;

    protected bool $showAccount = false;

    public function query()
    {
        return Claim::query()
            ->has('warranty')
            ->with([
                'faultType',
                'warranty.account',
                'warranty.sale.dealership',
                'warranty.sale.customer',
                'warranty.sale.vehicle',
                'estimates' => fn ($q) => $q->has('authorisation'),
                'estimates.authorisation.invoice',
                'estimates.lineItems',
            ])
            ->select('claims.*')
            ->latest('claims.id')
            ->groupBy('claims.id')
            ->filter($this->filters);
    }

    public function headings(): array
    {
        return collect([
            $this->showAccount ? ['Account'] : [],
            [
                '#',
                'Funding Type',
                'Claim Date',

                'VRM',
                'Private Plate',
                'VIN',
                'Make',
                'Model',
                'Derivative',
                'Engine Capacity',
                'Colour',
                'Fuel Type',
                'Transmission Type',
                'Registration Date',
                'Age at Delivery Date (years)',
                'Delivery Mileage',
                'Last Service Mileage',
                'Last Service Date',

                'Dealer',

                'Last Name',
                'First Name',
                'Customer Email',
                'Customer Phone',
                'Address 1',
                'Address 2',
                'City',
                'County',
                'Country',
                'Postcode',

                'Sale Date',
                'Failure Date',
                'Days to Failure',
                'Mileage at Failure',
                'Reference',
                'Fault',

                'Authorised estimates',

                'Total Parts NET',
                'Total Labour NET',
                'VAT',
                'Customer Contribution',
                'Total Authorised NET',
                'Total Authorised GROSS',

                'Invoice numbers',
            ],
        ])->filter()->flatten()->toArray();
    }

    public function map($row): array
    {
        $totalParts = $row->estimates->sum(fn (ClaimEstimate $estimate) => $estimate->lineItems->sum(fn (ClaimEstimateLineItem $lineItem) => $lineItem->type !== ClaimEstimateLineItemType::LABOUR ? $lineItem->quantity * $lineItem->amount : 0));
        $totalLabour = $row->estimates->sum(fn (ClaimEstimate $estimate) => $estimate->lineItems->sum(fn (ClaimEstimateLineItem $lineItem) => $lineItem->type === ClaimEstimateLineItemType::LABOUR ? $lineItem->quantity * $lineItem->amount : 0));
        $totalVat = $row->estimates->sum(fn (ClaimEstimate $estimate) => $estimate->lineItems->sum(fn (ClaimEstimateLineItem $lineItem) => $lineItem->quantity * $lineItem->vat));
        $totalCustomerContribution = $row->estimates->sum(fn (ClaimEstimate $estimate) => $estimate->lineItems->sum('customer_contribution'));
        $totalAuthorisedNet = $totalParts + $totalLabour - $totalCustomerContribution;
        $totalAuthorisedGross = $totalAuthorisedNet + $totalVat;

        return collect([
            $this->showAccount ? $row->warranty->account?->name : [],
            [
                $row->reference,
                $row->warranty->fund_type->label(),
                $row->created_at->format('d/m/Y'),

                $row->warranty->sale->vehicle->vrm,
                $row->warranty->sale->vehicle->private_plate,
                $row->warranty->sale->vehicle->vin,
                $row->warranty->sale->vehicle->make,
                $row->warranty->sale->vehicle->model,
                $row->warranty->sale->vehicle->derivative,
                $row->warranty->sale->vehicle->engine_capacity,
                $row->warranty->sale->vehicle->colour,
                $row->warranty->sale->vehicle->fuel_type,
                $row->warranty->sale->vehicle->transmission_type,
                Carbon::parse($row->warranty->sale->vehicle->registration_date)->format('d/m/Y'),
                round(Carbon::parse($row->warranty->sale->vehicle->registration_date)->diffInDays($row->warranty->sale->start_date) / 365, 1),
                $row->warranty->sale->delivery_mileage,
                $row->warranty->sale->last_service_mileage,
                $row->warranty->sale->last_service_date ? Carbon::parse($row->warranty->sale->last_service_date)->format('d/m/Y') : null,

                $row->warranty->sale->dealership->name,

                $row->warranty->sale->customer->last_name,
                $row->warranty->sale->customer->first_name,
                $row->warranty->sale->customer->email,
                $row->warranty->sale->customer->phone,

                $row->warranty->sale->customer->address_1,
                $row->warranty->sale->customer->address_2,
                $row->warranty->sale->customer->city,
                $row->warranty->sale->customer->county,
                $row->warranty->sale->customer->country,
                $row->warranty->sale->customer->postcode,

                Carbon::parse($row->warranty->sale->start_date)->format('d/m/Y'),
                Carbon::parse($row->failure_date)->format('d/m/Y'),
                Carbon::parse($row->warranty->sale->start_date)->diffInDays($row->failure_date),
                $row->current_mileage,
                $row->reference,
                $row->faultType->name,

                $row->estimates->count(),

                number_format($totalParts, 2, null, ''),
                number_format($totalLabour, 2, null, ''),
                number_format($totalVat, 2, null, ''),
                number_format($totalCustomerContribution, 2, null, ''),
                number_format($totalAuthorisedNet, 2, null, ''),
                number_format($totalAuthorisedGross, 2, null, ''),

                $row->estimates->pluck('authorisation.invoice.invoice_number')->join(', '),
            ],
        ])->filter()->flatten()->toArray();
    }
}
