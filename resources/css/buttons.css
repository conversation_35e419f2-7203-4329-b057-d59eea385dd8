.btn-indigo {
    @apply px-6 py-3 rounded bg-indigo-600 text-white text-sm leading-4 font-bold whitespace-nowrap hover:bg-orange-400 focus:bg-orange-400 transition-colors duration-300 disabled:opacity-60;
}

.btn-outline {
    @apply px-6 py-3 rounded border border-indigo-600 text-indigo-600 text-sm leading-4 font-bold whitespace-nowrap hover:border-orange-400 hover:text-orange-400 focus:border-orange-400 transition-colors duration-300 disabled:opacity-60;
}

.btn-danger {
    @apply px-6 py-3 rounded bg-red-500 text-white text-sm leading-4 font-bold whitespace-nowrap hover:bg-red-800 focus:bg-red-800 transition-colors duration-300 disabled:opacity-60;
}

.btn-sm {
    @apply px-4 py-1 text-xs;
}

.btn-spinner,
.btn-spinner:after {
    border-radius: 50%;
    width: 1.5em;
    height: 1.5em;
}

.btn-spinner {
    font-size: 10px;
    position: relative;
    text-indent: -9999em;
    border-top: 0.2em solid white;
    border-right: 0.2em solid white;
    border-bottom: 0.2em solid white;
    border-left: 0.2em solid transparent;
    transform: translateZ(0);
    animation: spinning 1s infinite linear;
}

.btn-outline .btn-spinner {
    @apply border-t-orange-400 border-r-orange-400 border-b-orange-400;
}

@keyframes spinning {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
