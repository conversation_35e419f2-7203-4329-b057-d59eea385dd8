@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --color-accent: var(--color-red-500);
        --color-accent-content: var(--color-red-600);
        --color-accent-foreground: var(--color-white);
    }

    .dark {
        --color-accent: var(--color-red-500);
        --color-accent-content: var(--color-red-400);
        --color-accent-foreground: var(--color-white);
    }

    @page {
        size: A4;
        margin: 15mm 10mm;
    }

    @media screen {
        .print-page {
            margin: 15mm 10mm;
            padding-bottom: 10mm;
            border-bottom: 1px solid #ccc;
        }
    }

    @media print {
        .print-page {
            margin: 0;
            border: initial;
            border-radius: initial;
            width: initial;
            min-height: initial;
            box-shadow: initial;
            background: initial;
            page-break-after: always;
            h1, h2, h3, h4, h5, h6 {
                page-break-after: avoid;
            }
            p, blockquote {
                page-break-inside: avoid;
            }
        }
    }
}

