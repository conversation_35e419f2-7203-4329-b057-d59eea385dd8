<div class="max-w-3xl mx-auto">
  <flux:heading size="lg" level="1" class="mb-6 mx-6">Select product to view payments</flux:heading>

  <div class="grid grid-cols-1 gap-4" x-data="{ visibleKey: '0' }">
    @foreach($this->sales as $sale)
      <x-product-card :heading="'Reference: ' . $sale->id"
                      :key="$loop->index"
                      :sale="$sale"
      >
        @if($sale->requiresPaymentSetup())
          <flux:card class="mb-6 bg-red-100 border-1 !border-red-500 flex items-start justify-between gap-4">
            <div class=" flex items-center gap-2.5">
              <flux:icon name="exclamation-circle" class="text-red-500"/>
              <flux:heading size="lg" class="!text-red-500">We do not have a Direct Debit on file for
                this.
              </flux:heading>
            </div>
            <div>
              <flux:button size="sm"
                           wire:click="setupDirectDebit({{ $sale->getKey() }})"
                           variant="primary"
              >Setup Direct Debit
              </flux:button>
            </div>
          </flux:card>
        @endif

        <div class="mx-auto bg-white p-4 rounded-xl shadow">
          @if($sale->isRecurring())
            <flux:table>
              <flux:columns>
                <flux:column>Date</flux:column>
                <flux:column>Status</flux:column>
                <flux:column>Amount</flux:column>
              </flux:columns>

              <flux:rows>
                @foreach($sale->payments as $payment)
                  <flux:row>
                    <flux:cell>{{ $payment->charge_date->format('d F Y') }}</flux:cell>
                    <flux:cell>
                      <flux:badge
                              :color="$payment->statusColor()"
                              size="sm" inset="top bottom"
                      >{{ $payment->statusForCustomer() }}</flux:badge>
                    </flux:cell>
                    <flux:cell variant="strong">
                      £{{ number_format($payment->amount, 2) }}</flux:cell>
                  </flux:row>
                @endforeach
              </flux:rows>
            </flux:table>
          @else
            <div class="px-12 py-6">
              <flux:subheading class="text-center">This was paid for
                via {{ $sale->dealership?->name ?: 'your dealer' }}. Only subscription based
                products will show payments here.
              </flux:subheading>
            </div>
          @endif
        </div>
      </x-product-card>
    @endforeach
  </div>
</div>
