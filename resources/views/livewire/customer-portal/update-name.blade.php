<flux:card>
    <div class="flex gap-4 items-start justify-between">
        <div class="flex items-start gap-2.5 mb-2">
            <flux:icon name="user" class="text-accent mb-2"/>
            <flux:heading size="lg">Name</flux:heading>
        </div>
        @if(!$isUpdating)
            <button wire:click="set('isUpdating', true)">
                <flux:icon name="pencil" class="size-5 text-accent"/>
            </button>
        @endif
    </div>
    <flux:subheading>Update the name that we have on record:</flux:subheading>
    <form class="mt-8" wire:submit.prevent="save">
        <div class="flex items-end justify-between gap-2">
            <div class="w-full max-w-sm grid gap-2">
                <flux:input name="first_name" wire:model="first_name"
                            placeholder="First Name"
                            :readonly="!$isUpdating"
                            :variant="$isUpdating ? 'outline' : 'filled'"
                />
                <flux:input name="last_name" wire:model="last_name"
                            placeholder="Last Name"
                            :readonly="!$isUpdating"
                            :variant="$isUpdating ? 'outline' : 'filled'"
                />

            </div>
            @if($isUpdating)
                <flux:button
                    variant="primary"
                    type="submit"
                >Save
                </flux:button>
            @endif
        </div>
    </form>
</flux:card>
