<flux:card>
    <div class="flex gap-4 items-start justify-between">
        <div class="flex items-start gap-2.5 mb-2">
            <flux:icon name="home-modern" class="text-accent mb-2"/>
            <flux:heading size="lg">Address</flux:heading>
        </div>
        <button>
            @if(!$isUpdating)
                <button wire:click="set('isUpdating', true)">
                    <flux:icon name="pencil" class="size-5 text-accent"/>
                </button>
            @endif
        </button>
    </div>
    <flux:subheading>Make sure we have your correct address:</flux:subheading>
    @if($isUpdating)
        <form wire:submit.prevent="save" class="mt-8 max-w-sm space-y-4">
            <flux:input label="Line 1" wire:model="form.address_1"/>
            <flux:input label="Line 2" wire:model="form.address_2"/>
            <flux:input label="County" wire:model="form.county"/>
            <flux:input label="Country" wire:model="form.country"/>
            <flux:input label="City" wire:model="form.city"/>
            <flux:input label="Postcode" wire:model="form.postcode"/>
            <div class="flex justify-end">
                <flux:button variant="primary" type="submit">Save</flux:button>
            </div>
        </form>
    @else
        <address class="mt-8 max-w-sm not-italic bg-gray-100 rounded-lg p-3 text-gray-600 text-sm">
            {{ $this->form->customer->fullAddress() }}
        </address>
    @endif
</flux:card>
