<form wire:submit.prevent="save">
    <flux:card class="max-w-4xl mx-auto space-y-6">
        <flux:heading size="lg" level="2">New Estimate</flux:heading>
        <div class="grid md:grid-cols-2 gap-6">
            <flux:input wire:model="workshop_name" label="Name of the workshop" badge="required"/>
            <flux:input wire:model="workshop_contact" label="Contact person at the workshop"
                        badge="required"
            />
            <flux:input wire:model="workshop_phone" label="Workshop phone number" badge="required"/>
            <flux:input wire:model="workshop_email" label="Workshop email" badge="required"/>
            <flux:textarea wire:model="workshop_address" label="Workshop address" resize="none"
                           badge="required"
            />
            <flux:textarea wire:model="work_required" label="Details of parts and work required"
                           badge="required"
                           resize="none"
            />
            <div class="col-start-2 grid grid-cols-1 gap-4">
                <x-input.money-inline type="number" step="0.01" label="Total Parts"
                                      wire:model.number.blur="total_parts"
                />
                <x-input.money-inline type="number" step="0.01" label="Total Labour"
                                      wire:model.number.blur="total_labour"
                />
                <x-input.money-inline type="number" step="0.01" label="VAT" wire:model.number.blur="vat"/>
                <x-input.money-inline type="number" step="0.01" label="Total" :value="$this->total" readonly
                                      variant="filled"
                />
            </div>
        </div>
        <div class="flex justify-end">
            <flux:button variant="primary" type="submit">Submit Estimate</flux:button>
        </div>
    </flux:card>
</form>
