<div>
    <div class="flex gap-4 items-start justify-between">
        <div class="flex items-start gap-2.5 mb-2">
            <flux:icon name="hand-helping" class="text-accent mb-2"/>
            <flux:heading size="lg">My Offers</flux:heading>
        </div>
    </div>
    <div class="flex flex-col gap-4">
        @foreach($this->salesOffers as $salesOffer)
            <flux:card class="bg-gray-50 overflow-hidden">
                <div class="flex items-end justify-between">
                    <div>
                        <flux:heading accent size="xl" level="2" class="mb-2"
                        >{{ $salesOffer->salesLead->sale->vehicle->formattedVrm() }}</flux:heading>
                        <flux:subheading>{{ $salesOffer->salesLead->sale->vehicle->details }} {{ $salesOffer->salesLead->sale->vehicle->derivative }}</flux:subheading>
                        <div>
                            {!! $salesOffer->offer->body !!}
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <flux:button variant="primary"
                                     :href="route('customer-portal.offers.view', $salesOffer)"
                                     size="sm"
                        >View offer
                            <flux:icon name="chevron-right" variant="micro"/>
                        </flux:button>
                    </div>
                </div>

            </flux:card>
        @endforeach
    </div>
</div>
