<div class="flex min-h-full items-center justify-center">
    <flux:card class="w-full max-w-2xl flex flex-col items-center md:py-20 md:px-32 gap-8">
        <x-brand/>

        <form wire:submit.prevent="check" class="w-full max-w-sm space-y-6"
              x-data="{
            otp: '',
            otpInput() {
                this.otp = this.otp.replace(/[^0-9]/g, '');
                if (this.otp.length === 6) {
                    $wire.check();
                }
            }
        }"
        >
            <flux:input label="Email" name="username" wire:model="username" placeholder="Email" :disabled="$this->isEmail || $this->isSms"/>
            @if($this->isEmail || $this->isSms)
                <flux:input label="One-Time-Passcode"
                            name="otp"
                            maxlength="6"
                            wire:model="otp"
                            x-model="otp"
                            placeholder="******"
                            @input="otpInput"
                            class:input="tracking-[2.1em] text-center !px-20"
                />
            @endif
            <div>
                <flux:button variant="primary" type="submit" class="mt-4 w-full">Login</flux:button>
            </div>
        </form>

    </flux:card>
</div>
