<div x-data="{ acceptTerms: false }" class="max-w-4xl mx-auto space-y-6">
    @unless($claim->terms_accepted_at)
        <flux:card class="space-y-6 overflow-hidden bg-red-100 !border-red-500 !text-red-700">
            <div class="flex items-center gap-2">
                <flux:icon name="exclamation-circle" class="text-red-700"/>
                <flux:heading size="lg" level="2" class="!text-red-700">Action Required</flux:heading>
            </div>
            <p>
                In order to progress this claim, please confirm that you understand the following:
            </p>
            <ul class="list-inside list-disc">
                <li>Your vehicle has been serviced in accordance with manufacturer guidelines.</li>
                <li>You understand that this claim is subject to a maximum labour rate of
                    <strong class="font-semibold">£{{ $claim->warranty->account->max_hourly_labour_rate }}</strong> per
                    hour.
                </li>
                <li>You understand that you will be responsible for paying the diagnostics charge.</li>
            </ul>
            <flux:checkbox x-model="acceptTerms" label="I understand the above conditions"/>
            <flux:button variant="primary" x-bind:disabled="!acceptTerms" wire:click="acceptTerms">Confirm</flux:button>
        </flux:card>
    @endunless

    <flux:card class="space-y-6 overflow-hidden">
        <x-definition-list class="bg-white -mx-6 -mb-6 pb-6 px-6">
            <x-definition-list.group heading="Claim Overview">
                <x-definition-list.item label="Reference" :value="$claim->reference"/>
                <x-definition-list.item label="Status">
                    <flux:badge :color="$claim->status->colorCustomerPortal()" size="sm" inset="top bottom">
                        {{ ucwords($claim->status->value) }}
                    </flux:badge>
                </x-definition-list.item>
            </x-definition-list.group>
        </x-definition-list>
        @if($claim->status === \App\Enums\ClaimStatus::REJECTED)
            <flux:subheading>
                Your claim has not been authorised on this occasion. Please contact us for more information.
            </flux:subheading>
        @elseif($claim->status === \App\Enums\ClaimStatus::AWAITING_ESTIMATE)
            <flux:subheading>
                Your claim is currently being reviewed. We will contact you with an update as soon as possible.
            </flux:subheading>
        @endif
    </flux:card>

    <div x-data="{ visibleKey: null }" class="grid gap-6">
        @foreach($claim->estimates as $estimate)
            <flux:card class="space-y-6 bg-gray-50 pb-20 overflow-hidden">
                <div class="flex items-start justify-between">
                    <div>
                        <flux:heading size="lg" level="2" accent>
                            Estimate #{{ $loop->iteration }}</flux:heading>
                        {{--                        <flux:heading>Total £{{ number_format($estimate->totalAmount(), 2) }}</flux:subheading>--}}
                    </div>
                    <div>
                        <flux:button variant="primary"
                                     @click="visibleKey = (visibleKey === '{{ $loop->iteration }}' ? null : '{{ $loop->iteration }}')"
                        >View
                        </flux:button>
                    </div>
                </div>
                <div x-cloak x-show="visibleKey === '{{ $loop->iteration }}'">
                    <x-definition-list class="bg-white -mx-6 -mb-6 pb-6 px-6">
                        <x-definition-list.group heading="Estimate">
                            <x-definition-list.item label="Date"
                                                    :value="$estimate->created_at->format('d/m/Y H:i')"
                            />
                            <x-definition-list.item label="Workshop" :value="$estimate->workshop_name"/>
                            <x-definition-list.item label="Workshop contact" :value="$estimate->workshop_contact"/>
                            <x-definition-list.item label="Workshop email" :value="$estimate->workshop_email"/>
                            <x-definition-list.item label="Workshop phone" :value="$estimate->workshop_phone"/>
                            <x-definition-list.item label="Workshop address" :value="$estimate->workshop_address"/>
                            <x-definition-list.item label="Work required" :value="$estimate->work_required"/>
                            {{--                            <x-definition-list.item label="TOTAL"--}}
                            {{--                                                    :value="'£' . number_format($estimate->totalAmount(), 2)"--}}
                            {{--                            />--}}
                            {{--                            <x-definition-list.item label="Your Contribution"--}}
                            {{--                                                    :value="'£' . number_format($estimate->customer_contribution, 2)"--}}
                            {{--                            />--}}
                        </x-definition-list.group>
                    </x-definition-list>
                </div>
            </flux:card>
        @endforeach
    </div>

    {{--    <flux:card class="space-y-6 pb-20 overflow-hidden">--}}
    {{--        <div class="flex items-start justify-between">--}}
    {{--            <div>--}}
    {{--                <flux:heading size="lg" level="2" accent>Add a new estimate</flux:heading>--}}
    {{--            </div>--}}
    {{--            <div>--}}
    {{--                <flux:button variant="primary"--}}
    {{--                             :href="route('customer-portal.claims.new-estimate', $claim)"--}}
    {{--                >Add estimate--}}
    {{--                </flux:button>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--    </flux:card>--}}
</div>
