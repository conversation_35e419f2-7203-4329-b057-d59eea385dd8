<flux:card>
    <div class="flex gap-4 items-start justify-between">
        <div class="flex items-start gap-2.5 mb-2">
            <flux:icon name="phone" class="text-accent mb-2"/>
            <flux:heading size="lg">Phone</flux:heading>
        </div>
        @if(!$isUpdating)
            <button wire:click="set('isUpdating', true)">
                <flux:icon name="pencil" class="size-5 text-accent"/>
            </button>
        @endif
    </div>
    <flux:subheading>Update the phone numbers we have on record:</flux:subheading>
    <form class="mt-8 max-w-sm" wire:submit.prevent="save">
        <flux:field>
            <flux:input.group>
                <flux:input name="phone" wire:model="phone"
                            :readonly="!$isUpdating"
                            :variant="$isUpdating ? 'outline' : 'filled'"
                            mask="07999999999"
                />
                @if($isUpdating)
                    <flux:button
                        variant="primary"
                        type="submit"
                    >Save
                    </flux:button>
                @endif
            </flux:input.group>
            <flux:error name="phone"/>
        </flux:field>
    </form>
</flux:card>
