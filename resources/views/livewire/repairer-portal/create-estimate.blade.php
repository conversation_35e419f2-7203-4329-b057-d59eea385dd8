<div>
    <div class="mx-auto max-w-7xl space-y-6">

        <form wire:submit.prevent="save">
            <flux:card class="space-y-6">
                <flux:heading size="xl">New Estimate</flux:heading>
                <div class="grid md:grid-cols-2 gap-6">
                    <flux:input wire:model="workshop_name" label="Name of the workshop" badge="required"/>
                    <flux:input wire:model="workshop_contact" label="Contact person at the workshop"
                                badge="required"
                    />
                    <flux:input wire:model="workshop_phone" label="Workshop phone number" badge="required"/>
                    <flux:input wire:model="workshop_email" label="Workshop email" badge="required"/>
                    <flux:input type="number" wire:model.number="current_mileage" label="Current mileage"
                                badge="required"
                    />
                    <flux:textarea wire:model="workshop_address" label="Workshop address" resize="none"
                                   badge="required"
                    />
                    <flux:textarea wire:model="work_required"
                                   label="Work required"
                                   placeholder="Enter details of parts and work required"
                                   badge="required"
                                   resize="none"
                    />
                </div>
            </flux:card>
        </form>

        <div
            x-data="{}"
            x-init="() => {
                    $nextTick(() => {
                        let el = $el.querySelector('.animate-fade-in')
                        setTimeout(() => {
                            el.classList.add('opacity-100');
                            el.classList.remove('opacity-0');
                        }, 200);
                    });
                }"
        >
            <flux:card class="space-y-6">

                <flux:heading size="lg" level="2">Line Items</flux:heading>
                <flux:subheading>
                    Add parts, labour, and consumables for the repair.
                </flux:subheading>
                <flux:button
                    wire:click="addLineItem"
                    icon-leading="plus"
                >
                    Add Item
                </flux:button>
                <div class="card-content">
                    @if (count($lineItems) === 0)
                        <div class="text-center p-6 border border-dashed border-gray-200 rounded-lg">
                            <p class="text-gray-500">No items added yet. Click 'Add Item' to begin.</p>
                        </div>
                    @else
                        <div class="space-y-4">
                            <div class="grid grid-cols-12 gap-2 text-xs font-medium text-gray-500 px-2">
                                <div class="col-span-1">Qty</div>
                                <div class="col-span-1">Type</div>
                                <div class="col-span-3">Description</div>
                                <div class="col-span-2 text-right">Net</div>
                                <div class="col-span-2 text-right">VAT</div>
                                <div class="col-span-2 text-right">Total</div>
                                <div class="col-span-1"></div>
                            </div>

                            @foreach ($lineItems as $index => $item)
                                <div
                                    class="grid grid-cols-12 gap-2 items-center"
                                    x-data="{}"
                                    x-init="() => {
                                                $el.classList.add('opacity-100', 'translate-y-0');
                                                $el.classList.remove('opacity-0', 'translate-y-4');
                                            }"
                                    {{--                                        class="transition-all duration-300 opacity-0 translate-y-4"--}}
                                >
                                    <div class="col-span-1">
                                        <flux:input
                                            type="number"
                                            min="1"
                                            wire:model="lineItems.{{ $index }}.quantity"
                                            wire:change="updateLineItem('{{ $item['id'] }}', 'quantity', $event.target.value)"
                                            class:input="text-right"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <flux:select
                                            wire:model="lineItems.{{ $index }}.type"
                                            wire:change="updateLineItem('{{ $item['id'] }}', 'type', $event.target.value)"
                                        >
                                            <option value="parts">Parts</option>
                                            <option value="labour">Labour</option>
                                            <option value="consumable">Consumable</option>
                                        </flux:select>
                                    </div>
                                    <div class="col-span-3">
                                        <flux:input
                                            type="text"
                                            placeholder="Description"
                                            wire:model="lineItems.{{ $index }}.description"
                                            wire:change="updateLineItem('{{ $item['id'] }}', 'description', $event.target.value)"
                                        />
                                    </div>
                                    <div class="col-span-2">
                                        <flux:input
                                            type="number"
                                            min="0"
                                            step="0.01"
                                            placeholder="Net"
                                            wire:model="lineItems.{{ $index }}.netCost"
                                            wire:change="updateLineItem('{{ $item['id'] }}', 'netCost', $event.target.value)"
                                            class:input="text-right"
                                        />
                                    </div>

                                    <div class="col-span-2">
                                        <flux:input
                                            type="number"
                                            min="0"
                                            step="0.01"
                                            placeholder="VAT"
                                            value="{{ number_format($item['vatAmount'], 2) }}"
                                            readonly
                                            class:input="text-right"
                                        />
                                    </div>
                                    <div class="col-span-2">
                                        <flux:input
                                            type="number"
                                            value="{{ number_format($item['totalAmount'], 2) }}"
                                            readonly
                                            class:input="text-right"
                                        />
                                    </div>
                                    <div class="col-span-1 text-center">
                                        <flux:button
                                            wire:click="removeLineItem('{{ $item['id'] }}')"
                                            variant="ghost"
                                        >
                                            <svg class="h-4 w-4 text-gray-400 hover:text-red-500" fill="none"
                                                 stroke="currentColor" viewBox="0 0 24 24"
                                                 xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                      stroke-width="2"
                                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                                ></path>
                                            </svg>
                                        </flux:button>
                                    </div>
                                </div>
                            @endforeach

                            <div class="flex justify-end pt-4 border-t">
                                <div class="w-64 grid grid-cols-2 gap-2 text-right">
                                    <div class="font-medium text-gray-600">Total:</div>
                                    <div class="font-bold text-gray-800">£{{ number_format($total, 2) }}</div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
                <div class="card-footer flex justify-between">
                    <a
                        href="#"
                        class="btn btn-outline shadow-subtle hover:shadow-md transition-all"
                    >
                        Back
                    </a>
                    <button
                        type="button"
                        wire:click="submit"
                        class="btn shadow-button hover:shadow-lg transition-all"
                        {{ count($lineItems) === 0 ? 'disabled' : '' }}
                    >
                        Next
                    </button>
                </div>
            </flux:card>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('livewire:load', function () {
                // Add Alpine.js transitions here if needed

                // Flash messages
                Livewire.on('flash-message', (type, message) => {
                    // Replace with your preferred toast library
                    // Example with Sweetalert2:
                    // Swal.fire({
                    //     icon: type,
                    //     title: message,
                    //     toast: true,
                    //     position: 'top-end',
                    //     showConfirmButton: false,
                    //     timer: 3000
                    // });
                })
            })
        </script>
    @endpush
</div>
