<div>
    <div class="min-h-screen flex flex-col justify-between items-center py-4 md:py-12">
        <div class="flex flex-col items-center w-full">
            <x-brand/>
            <div
                class="mt-6 bg-white dark:bg-white/10 p-4 sm:p-6 sm:rounded-2xl sm:border border-zinc-200 dark:border-white/10 w-full max-w-2xl"
            >
                @if($step === 'expired')
                    <div class="flex flex-col gap-5">
                        <flux:heading size="lg" class="text-center">This link has expired
                        </flux:heading>
                        <flux:subheading class="text-center text-sm text-zinc-500">
                            For your security, Direct Debit links are only valid for 7 days. We have sent you
                            an email with a new link, please check your inbox and follow the instructions.
                        </flux:subheading>
                    </div>
                @elseif($step === 'completed')
                    <div class="flex flex-col gap-5">
                        <flux:heading size="lg" class="text-center">Thank you for setting up your direct debit details.
                        </flux:heading>
                        <flux:subheading class="text-center text-sm text-zinc-500">
                            You Direct Debit Reference is:
                            <strong>{{ $this->billingRequest->direct_debit_reference }}</strong>.
                            You can now close this window.
                        </flux:subheading>
                    </div>
                @elseif($step === 'confirm')
                    <flux:heading size="xl" class="mb-4 text-center">Confirm your details</flux:heading>
                    <div class="rounded-2xl border overflow-hidden">
                        <div class="bg-gray-100 p-4 sm:px-8 sm:py-6 text-sm space-y-4">
                            <p>Your Direct Debit will be set up now. We'll confirm the amount and let you know before
                                future
                                payments are taken.</p>
                            <x-icons.direct-debit class="mx-auto"/>
                        </div>
                        <div class="flex items-start justify-between">
                            <div class="p-4 sm:px-8 sm:py-6 text-sm space-y-4">
                                <dl class="space-y-4">
                                    <x-definition-list.item label="Account Holder Name" :value="$accountHolderName"
                                                            variant="stacked"
                                    />
                                    <x-definition-list.item label="Sort code" :value="$sortCode" variant="stacked"/>
                                    <x-definition-list.item label="Account number" :value="$accountNumber"
                                                            variant="stacked"
                                    />
                                </dl>
                            </div>
                            <div>
                                <flux:button
                                    class="w-full mt-8" variant="subtle" icon="pencil"
                                    wire:click="$set('step', 'collect-details')"
                                >Edit
                                </flux:button>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 p-4 flex flex-col space-y-4">
                        <flux:checkbox
                            label="I confirm that I am the account holder and the only person required to authorise
                   debits from this account."
                            wire:model.live="canAuthorise"
                        />
                        <flux:button icon="lock-closed" variant="primary" class="mx-auto"
                                     wire:click="confirm"
                                     :disabled="!$canAuthorise"
                        >Set up this Direct Debit
                        </flux:button>
                    </div>
                @else
                    <flux:heading size="xl" class="mb-4 text-center">Set up a Direct Debit</flux:heading>

                    <div class="rounded-2xl bg-gray-100 p-4 sm:px-8 sm:py-6 text-sm space-y-4">
                        <p>Your Direct Debit will be set up now. We'll confirm the amount and let you know before future
                            payments are taken.</p>
                        <x-icons.direct-debit class="mx-auto"/>
                    </div>

                    <form class="mt-8 space-y-4" wire:submit.prevent="submitForm">
                        <flux:input label="Account Name" wire:model="accountHolderName"/>
                        <flux:card class="overflow-hidden">
                            <div class="bg-gray-100 px-6 py-4 sm:p-6 -mx-6 -mt-6">
                                <x-icons.direct-debit class="h-5 w-auto"/>
                                <div class="mt-4 flex items-center justify-between gap-1">
                                    <p class="text-sm font-semibold">
                                        Payments are protected by the Direct Debit Guarantee
                                    </p>
                                    <button type="button">
                                        <flux:modal.trigger name="direct-debit-guarantee">
                                            <flux:icon name="question-mark-circle" variant="micro"/>
                                        </flux:modal.trigger>
                                </div>
                            </div>
                            <div class="mt-4 grid sm:grid-cols-2 gap-4">
                                <div class="relative">
                                    <flux:input label="Your sort code" wire:model="sortCode"
                                                mask="99-99-99"
                                                description="Must be 6 digits long"
                                                placeholder="eg: 10-20-30"
                                    />
                                    <flux:icon name="lock-closed" variant="micro"
                                               class="text-zinc-400 absolute bottom-3 right-2"
                                    />
                                </div>
                                <div class="relative">
                                    <flux:input label="Your account number" wire:model="accountNumber"
                                                :maxlength="8"
                                                description="Must be 8 digits long"
                                                placeholder="eg: ********"
                                    />
                                    <flux:icon name="lock-closed" variant="micro"
                                               class="text-zinc-400 absolute bottom-3 right-2"
                                    />
                                </div>
                            </div>
                        </flux:card>


                        <div class="flex justify-end">
                            <flux:button type="submit" variant="primary"
                                         icon-trailing="chevron-right"
                            >Continue
                            </flux:button>
                        </div>
                    </form>
                @endif
            </div>
        </div>
        <footer class="max-w-xl p-4 sm:p-6 text-center text-zinc-500 space-y-4 text-sm">
            <div>
                {{ $this->companyName }} will appear on your bank statement. You may cancel this Direct Debit at any time by
                contacting {{ $this->companyName }} or your bank.
            </div>
            <div>
                Your payments are protected by the
                <flux:modal.trigger name="direct-debit-guarantee" class="font-semibold inline">Direct Debit Guarantee
                </flux:modal.trigger>
                .
            </div>
        </footer>
    </div>
    <flux:modal name="direct-debit-guarantee" class="w-full max-w-3xl space-y-6"
    >
        <div>
            <x-icons.direct-debit/>
            <flux:heading size="xl" class="mt-6">Direct Debit Guarantee</flux:heading>
        </div>

        <div class="space-y-4">
            <p>
                This Guarantee is offered by all banks and building societies that accept instructions to pay Direct
                Debits.
            </p>
            <p>
                If there are any changes to the amount, date or frequency of your Direct Debit, {{ $this->companyName }} will
                notify you 3 working days in advance of your account being debited or as otherwise agreed. If you
                request {{ $this->companyName }} to collect a payment, confirmation of the amount and date will be given to you
                at the time of the request.
            </p>
            <p>
                If an error is made in the payment of your Direct Debit, by {{ $this->companyName }} or your bank or building
                society, you are entitled to a full and immediate refund of the amount paid from your bank or building
                society.
            </p>
            <p>
                If you receive a refund you are not entitled to, you must pay it back when {{ $this->companyName }} asks you to.
            </p>
            <p>
                You can cancel a Direct Debit at any time by simply contacting your bank or building society. Written
                confirmation may be required. Please also notify us.
            </p>
            <p>
                Please note that the Direct Debit Guarantee does not cover business transactions.
            </p>
        </div>

        <div class="flex">
            <flux:spacer/>

            <flux:modal.close>
                <flux:button type="submit" variant="outline">Got it</flux:button>
            </flux:modal.close>
        </div>
    </flux:modal>
</div>
