<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    >
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Sale #{{ $sale->id }}</title>
    @if(app()->isLocal() && $isPreview)
        @vite('resources/css/app.css')
    @else
        <link rel="stylesheet" href="{{ Storage::disk('public')->url('assets/app.css') }}?v={{ filemtime(public_path('build/manifest.json')) }}">
    @endif
</head>
<body class="font-display">

@if($sale->warranty)
    @include('pdf.default._welcome', ['warranty' => $sale->warranty])

    @includeWhen($qrCode, 'pdf.default._qr_code')

    @include('pdf.default._validation_certificate', ['warranty' => $sale->warranty])

    @include('pdf.default._definitions', ['warranty' => $sale->warranty])

    @foreach($documents as $document)
        <div class="print-page prose max-w-max">
            {!! str($document)->markdown()->sanitizeHtml() !!}
        </div>
    @endforeach

    <x-markdown-section view="markdown/how_to_claim.md" />
    <x-markdown-section view="markdown/terms_and_conditions.md" />
    <x-markdown-section view="markdown/general_exclusions.md" />

@endif

@if($sale->breakdownPlan)
    @includeIf('pdf.default.breakdown_cover.' . str($sale->breakdownPlan->product->name)->snake())
@endif

@if($sale->servicePlan)
    @includeIf('pdf.default.service_plan.' . str($sale->servicePlan->product->name)->snake())
@endif

@foreach($sale->products as $accountProduct)
    @if(view()->exists('pdf.default.' . str($accountProduct->product->group->name)->snake() . '.' . str($accountProduct->product->name)->snake()))
        @include('pdf.default.' . str($accountProduct->product->group->name)->snake() . '.' . str($accountProduct->product->name)->snake())
    @else
        @includeIf('pdf.default.' . str($accountProduct->product->group->name)->snake() . '.default')
    @endif
@endforeach

</body>
</html>
