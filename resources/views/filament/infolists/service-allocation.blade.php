<x-filament-infolists::entry-wrapper class="bg-blue-50 -m-3 p-4 rounded-lg text-blue-800">
    <div class="w-full flex items-center justify-between gap-4 mb-5">
        <div class="font-semibold">{{ $getLabel() }}</div>
        <div class="text-xs text-blue-600">
            {{ $getRecord()->authorisedServices->where('status', '!=', 'cancelled')->count() }}
            of
            {{ $getRecord()->servicePlan->serviceTypes->sum('pivot.limit') }}
            services used
        </div>
    </div>
    <div class="flex flex-col gap-2 text-sm">
        @foreach($getRecord()->servicePlan->serviceTypes as $serviceType)
            @php(
                $totalUsed = $getRecord()->authorisedServices
                    ->where('status', '!=', 'cancelled')
                    ->where('service_type_id', $serviceType->id)
                    ->count()
            )
            <div class="flex gap-2 justify-between">
                <div class="">{{ $serviceType->name }}</div>
                <div class="flex gap-8 items-center">
                    <div class="bg-white h-2 w-32 rounded-full overflow-hidden">
                        <div class="bg-blue-500 h-2"
                             style="width: {{ $totalUsed / $serviceType->pivot->limit * 100 }}%"
                        ></div>
                    </div>
                    <div class=" flex flex-col items-end">
                        <div class="font-semibold">{{ $totalUsed }} of {{ $serviceType->pivot->limit }} used</div>
                        <div class="text-xs text-blue-600">{{ $serviceType->pivot->limit - $totalUsed }} remaining</div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</x-filament-infolists::entry-wrapper>
