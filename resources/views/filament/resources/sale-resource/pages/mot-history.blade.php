<x-filament-panels::page>
    <x-filament::section>
        <div class="flex flex-col gap-5">
            <div class="flex items-center justify-between gap-5">
                <div>
                    Last
                    checked: {{ $this->vehicle->mot_last_checked_at ? $this->vehicle->mot_last_checked_at->diffForHumans() : 'Never' }}
                </div>
                @unless($this->vehicle->mot_last_checked_at?->isAfter(today()))
                    <div class="flex items-center gap-5">
                        <x-heroicon-o-exclamation-circle class="text-red-500 size-6"/>
                        <x-filament::button wire:click="checkMotStatus"
                                            color="success"
                                            class="bg-gray-900 rounded-lg p-2 flex items-center gap-1"
                        >
                            <x-filament::loading-indicator wire:loading class="size-4"/>
                            Check now
                        </x-filament::button>
                    </div>
                @endunless
            </div>
            <div class="divide-y divide-gray-200">
                @foreach($this->vehicle->motTests as $motTest)
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-5 py-5 text-lg">
                        <div>
                            <dl>
                                <dt class="font-light dark:text-gray-400">Date tested</dt>
                                <dd class="font-black">{{ $motTest->completed_at->format('d M Y') }}</dd>
                            </dl>
                            <div
                                class="mt-2 text-5xl font-black {{ $motTest->result === 'PASSED' ? 'text-green-600' : 'text-red-500' }}"
                            >{{ substr($motTest->result, 0, -2) }}</div>
                        </div>
                        <div class="md:col-span-2 gap-5">
                            <div class="grid grid-cols-1 md:grid-cols-2">
                                <div>
                                    <dl>
                                        <dt class="font-light dark:text-gray-400">Mileage</dt>
                                        <dd class="font-black">{{ number_format($motTest->odometer_reading) ?: 'N/A' }}
                                            @if($motTest->odometer_unit === 'MI') miles @endif
                                            @if($motTest->odometer_unit === 'KM') Km @endif
                                        </dd>
                                    </dl>
                                </div>
                                <div>
                                    <dl class="flex flex-col gap-5">
                                        <div>
                                            <dt class="font-light dark:text-gray-400">MOT Test number</dt>
                                            <dd class="font-black">{{ implode(' ', str_split($motTest->test_number, 4)) }}</dd>
                                        </div>
                                        @if($motTest->expiry_date)
                                            <div>
                                                <dt class="font-light dark:text-gray-400">Expiry date</dt>
                                                <dd class="font-black">{{ $motTest->expiry_date->format('d M Y') }}</dd>
                                            </div>
                                        @endif
                                    </dl>
                                </div>
                            </div>
                            <div class="flex flex-col">
                                <x-mot-defects-list
                                    heading="Do not drive until repaired (dangerous defects):"
                                    :defects="$motTest->defects->where('dangerous', true)"
                                />

                                <x-mot-defects-list
                                    heading="Repair immediately (major defects):"
                                    :defects="$motTest->defects->where('dangerous', false)->where('type', 'MAJOR')"
                                />

                                <x-mot-defects-list
                                    heading="Monitor and repair if necessary (advisories):"
                                    :defects="$motTest->defects->where('dangerous', false)->where('type', '!=', 'MAJOR')"
                                />
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </x-filament::section>
</x-filament-panels::page>
