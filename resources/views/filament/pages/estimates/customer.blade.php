<x-filament-infolists::entry-wrapper :label="$getLabel()">
    <div class="flex flex-col gap-2 text-sm">
        <a href="{{ \App\Filament\Resources\CustomerResource::getUrl('view', [$getState()]) }}">{{ $getState()->full_name }}</a>
        <div class="flex items-center gap-2 text-gray-500 text-sm">
            <x-filament::icon icon="heroicon-o-envelope" class="size-5"/>
            <a href="mailto:{{ $getState()->email }}">{{ $getState()->email }}</a>
        </div>
        <div class="flex items-center gap-2 text-gray-500 text-sm">
            <x-filament::icon icon="heroicon-o-phone" class="size-5"/>
            <a href="tel:{{ $getState()->phone }}">{{ $getState()->phone }}</a>
        </div>
        <div class="flex items-center gap-2 text-gray-500 text-sm">
            <x-filament::icon icon="heroicon-o-map-pin" class="size-5"/>
            <span>{{ $getState()->postcode }}</span>
        </div>
    </div>
</x-filament-infolists::entry-wrapper>
