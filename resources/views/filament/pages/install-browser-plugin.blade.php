<x-filament-panels::page>
    <div class="p-6 bg-white rounded-xl shadow">
        <h2 class="text-2xl font-bold tracking-tight mb-4">Protego AutoCare Browser Plugin</h2>

        @if (session('error'))
            <div class="p-4 mb-4 rounded-lg bg-red-50 border border-red-200 text-red-700">
                <div class="flex items-center gap-3">
                    <x-filament::icon
                        icon="heroicon-o-exclamation-circle"
                        class="w-5 h-5 text-red-500"
                    />
                    {{ session('error') }}
                </div>
            </div>
        @endif

        <div class="prose max-w-none">
            <p class="text-lg mb-4">
                The Protego AutoCare Browser Plugin enhances your claims management experience by enabling direct integration with Prestige Fleet Servicing.
            </p>

            <div class="my-6 border-l-4 border-blue-500 pl-5 py-2 bg-blue-50">
                <p class="font-medium">With our browser plugin, you can:</p>
                <ul class="mt-2">
                    <li>Auto-fill claim information on Prestige Fleet Servicing website</li>
                    <li>Save hours of manual data entry</li>
                </ul>
            </div>

            <h3 class="text-xl font-bold mt-8 mb-4">Installation Instructions</h3>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="border rounded-lg p-5">
                    <div class="flex items-center gap-2 mb-4">
                        <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path>
                        </svg>
                        <h4 class="font-semibold text-lg my-2">Chrome/Edge</h4>
                    </div>
                    <ol class="list-decimal ml-5 space-y-2">
                        <li>Download the plugin by clicking the <a href="{{ route('browser-plugin.download') }}" class="text-blue-600 hover:text-blue-800 font-medium">download link</a></li>
                        <li>Open Chrome/Edge and navigate to Extensions > Manage Extensions <strong>or</strong> the following URL: <code class="bg-gray-100 px-1 rounded">chrome://extensions</code></li>
                        <li>Enable "Developer mode" using the toggle in the top-right corner</li>
                        <li>Drag and drop the downloaded plugin ZIP file into the Extensions page</li>
                        <li>The Protego AutoCare plugin icon should now appear in your browser toolbar</li>
                    </ol>
                </div>
            </div>

            <h3 class="text-xl font-bold mt-8 mb-4">Using the Plugin</h3>
            <p>After installation, the plugin works automatically when you visit the Prestige Fleet Servicing website. When viewing a claim in Protego AutoCare, you'll see enhanced options for interacting with the Prestige Fleet Servicing portal.</p>

            <div class="mt-6 p-5 bg-yellow-50 rounded-lg border border-yellow-200">
                <h4 class="flex items-center gap-2 font-medium">
                    <svg class="w-5 h-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M13 13H11V7H13M13 17H11V15H13M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2Z"></path>
                    </svg>
                    Important Note
                </h4>
                <p class="mt-2">The browser plugin requires permission to read and modify content on the Prestige Fleet Servicing website. These permissions are only used for automating claims management tasks and no data is collected for any other purpose.</p>
            </div>

            <h3 class="text-xl font-bold mt-8 mb-4">Need Help?</h3>
            <p>If you encounter any issues with installation or usage of the plugin, please contact our support team at <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800"><EMAIL></a></p>
        </div>
    </div>
</x-filament-panels::page>