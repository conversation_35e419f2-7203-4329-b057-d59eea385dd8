<x-filament-widgets::widget>
    <x-filament::section heading="Vehicle Information">
        <div class="grid grid-cols-2">
            @include('filament.widgets._vehicle-information')
            @if($sale->warranty)
                <div class="flex flex-col gap-1.5">
                    <h3 class="font-bold">Warranty Information</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400"
                    >Current: <span class="font-medium">
                        {{  $sale->warranty->product->coverLevel->name }}
                            @if($sale->warranty->isRecurring())
                                (Monthly)
                            @else
                                ({{  $sale->warranty->product->period }} {{ str('month')->plural($sale->warranty->product->period) }})
                            @endif
                </span></p>
                    <p class="text-sm text-gray-600 dark:text-gray-400"
                    >
                        @if($sale->warranty->end_date->isFuture())
                            Expires:
                        @else
                            Expired:
                        @endif
                        <span class="font-medium">{{ $sale->warranty->end_date->formatLocal() }}</span></p>
                    <div class="text-sm text-gray-600 dark:text-gray-400"
                    >Claims history:
                        @if($sale->warranty->claims->isEmpty())
                            No claims
                        @else
                            <ul class="flex flex-col gap-1">
                                @foreach($sale->warranty->claims as $claim)
                                    <li class="list-inside text-sm">
                                        <span class="inline-flex items-center gap-2">
                                            <span>- {{ $claim->failure_date->formatLocal() }}</span>
                                            <x-filament::badge :color="$claim->status->color()"
                                            >{{ $claim->status }}</x-filament::badge>
                                        </span>
                                    </li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
