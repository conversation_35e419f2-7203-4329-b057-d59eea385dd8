<div class="col-span-full">
    @if($this->requiresPaymentSetup)
        <x-filament-widgets::widget>
            <div class="rounded-xl border border-red-500 bg-red-50 p-4 max-w-7xl">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-2">
                        <x-filament::icon
                            icon="heroicon-s-exclamation-triangle"
                            class="size-6 text-red-500"
                        />
                        <div class="ml-3"><h3 class="text-lg font-bold text-red-500">Pending Action</h3>
                            <div class="mt-2 text-red-500"><p>This {{ $record->getBillableEntityLabel() }} has not completed their
                                {{ $this->paymentMethodName }} agreement.
                                </p></div>
                        </div>
                    </div>

                    <x-filament::button wire:click="openModal">
                        Setup {{ $this->paymentMethodName }} agreement
                    </x-filament::button>

                </div>
            </div>
            <x-filament::modal width="2xl" footer-actions-alignment="end" id="missing-payment-method-modal">
                <x-slot name="heading">
                    Setup {{ $this->paymentMethodName }} agreement
                </x-slot>

                {{ $this->infolist }}

                <x-slot name="footerActions">
                    <x-filament::button
                        wire:click="sendEmail"
                        icon="heroicon-c-envelope-open"
                    >Send Payment Link by Email
                    </x-filament::button>

                    <x-filament::link
                        :href="$this->paymentUrl"
                        icon="heroicon-c-arrow-top-right-on-square"
                        target="_blank"
                    >Open setup form
                    </x-filament::link>
                </x-slot>
            </x-filament::modal>
        </x-filament-widgets::widget>
    @endif
</div>
