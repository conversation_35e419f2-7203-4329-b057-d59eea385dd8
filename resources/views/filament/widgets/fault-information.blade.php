<x-filament-widgets::widget>
    <x-filament::section heading="Fault Information">
        <div class="flex items-start gap-3">
            <x-lucide-wrench class="h-5 w-5 text-gray-500 mt-0.5 shrink-0"/>
            <div>
                <h3 class="font-bold">Fault: {{ $claim->faultType->name }}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400"
                >
                    {{ $claim->fault_description }}
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400"
                >Date of Failure: <span class="font-medium">{{ $claim->failure_date->formatLocal() }}</span></p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Reported Mileage: <span class="font-medium">
                    {{ number_format($claim->current_mileage) }}
                </span>
                </p>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
