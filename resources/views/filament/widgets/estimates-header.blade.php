<x-filament-widgets::widget>
    <x-filament::section.heading>
        <div class="flex items-center gap-2 justify-between">
            <h3 class="text-xl font-bold">Estimates</h3>
            @can('create', [\App\Models\ClaimEstimate::class, $claim])
                <x-filament::button
                    tag="a"
                    href="{{ \App\Filament\Resources\ClaimResource::getUrl('create-estimate', ['claim' => $claim]) }}"
                    icon="heroicon-s-plus"
                >
                    New Estimate
                </x-filament::button>
            @endcan
        </div>
    </x-filament::section.heading>

    @if($claim->estimates->isEmpty())
        <x-filament::section class="mt-5">
            <x-filament-tables::empty-state
                heading="No estimates have been created for this claim."
                icon="heroicon-s-document"
            />
        </x-filament::section>
    @endif
</x-filament-widgets::widget>
