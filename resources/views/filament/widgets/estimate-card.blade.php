<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot:heading>
            <div class="flex items-center gap-2">
                <span>Estimate #{{ $index + 1 }}</span>
                <x-filament::badge :color="$claimEstimate->statusColor()">
                    {{ ucwords(str_replace("_", " ", $claimEstimate->status()->value)) }}
                </x-filament::badge>
            </div>
        </x-slot:heading>
        <x-slot:headerEnd>
            <x-filament::button
                tag="a"
                :href="\App\Filament\Resources\ClaimResource::getUrl('view-estimate', ['record' => $claimEstimate, 'claim' => $claimEstimate->claim])"
                color="gray"
                icon="heroicon-s-chevron-right"
                icon-position="after"
            >
                View Estimate
            </x-filament::button>
        </x-slot:headerEnd>

        <div class="grid grid-cols-3 gap-4">
            <x-filament-infolists::entry-wrapper label="Workshop">
                {{ $claimEstimate->repairer->name ?? $claimEstimate->workshop_name }}
            </x-filament-infolists::entry-wrapper>
            @if($claimEstimate->estimate_completed_at)
                <x-filament-infolists::entry-wrapper label="Completed Date">
                    {{ $claimEstimate->estimate_completed_at->formatLocalDateTime() }}
                </x-filament-infolists::entry-wrapper>
            @else
                <x-filament-infolists::entry-wrapper label="Booking Date" placeholder="Not booked">
                    @if($claimEstimate->estimate_booking_date)
                        {{ $claimEstimate->estimate_booking_date->formatLocal() }}
                    @else
                        <x-filament-infolists::entries.placeholder>Not booked
                        </x-filament-infolists::entries.placeholder>
                    @endif
                </x-filament-infolists::entry-wrapper>
            @endif
            <x-filament-infolists::entry-wrapper label="Work Description">
                {{ $claimEstimate->work_required }}
            </x-filament-infolists::entry-wrapper>
        </div>

        <div x-data="{ showDetails: false }">
            @if($claimEstimate->lineItems->isNotEmpty())
                <div x-show="showDetails" x-collapse class="mt-5">
                    <livewire:tables.claim-estimate-line-items-table :claimEstimate="$claimEstimate" :readonly="true"
                                                                     :show-totals="false"
                    />
                </div>
            @endif

            <div class="flex justify-end items-end gap-4 mt-2">
                <div>
                    @if($claimEstimate->lineItems->isNotEmpty())
                        <x-filament::button
                            color="gray"
                            size="xs"
                            @click="showDetails = !showDetails"
                        >
                            <div class="flex items-center gap-1">
                                Details
                                <x-filament::icon icon="heroicon-s-chevron-down" class="size-4 text-gray-400"
                                                  x-show="!showDetails"
                                />
                                <x-filament::icon icon="heroicon-s-chevron-up" class="size-4 text-gray-400"
                                                  x-show="showDetails"
                                />
                            </div>
                        </x-filament::button>
                    @endif
                </div>
                <div class="flex flex-col gap-1 items-end">
                    <div>
                        <span class="text-gray-500">Total:</span>
                        <strong class="text-gray-600">£{{ number_format($claimEstimate->totalAmount(), 2) }}</strong>
                    </div>
                    <div class="text-xs text-gray-500">
                        Ex VAT: £{{ number_format($claimEstimate->totalAmount() - $claimEstimate->vat(), 2) }}
                        |
                        VAT: £{{ number_format($claimEstimate->vat(), 2) }}
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
