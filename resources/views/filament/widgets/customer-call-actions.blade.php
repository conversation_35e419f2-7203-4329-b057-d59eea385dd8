<div>
<x-filament-widgets::widget>
    <x-filament::section heading="Quick Actions">
        <div class="flex flex-col gap-3">
{{--            <x-filament::button icon="heroicon-s-phone" wire:click="callCustomer">Call Customer</x-filament::button>--}}

            @if(auth()->user()->voipUser)
                {{ $this->callCustomer }}
            @endif
            {{ $this->scheduleCallback }}
            {{ $this->recordOutcome }}
            {{ $this->sendOffer }}

{{--            <x-filament::button icon="heroicon-s-calendar" color="gray">Schedule Callback</x-filament::button>--}}
{{--            <x-filament::button icon="heroicon-s-document-text" color="gray">Record Call Outcome</x-filament::button>--}}
        </div>
    </x-filament::section>


</x-filament-widgets::widget>

<x-filament-actions::modals />
</div>
