@props([
    'vehicle'
])

<div
    {{ $attributes->class(['flex items-center gap-2 fi-ta-text-item-label text-sm leading-6 text-gray-950 dark:text-white overflow-hidden']) }}
>
    <div class="flex flex-col gap-1">
        @if($vehicle->private_plate)
            <x-numberplate :vehicle="$vehicle" :private-plate="true"/>
        @endif
        <x-numberplate :vehicle="$vehicle"/>
    </div>
    @if($vehicle->manufacturer)
        <img src="{{ $vehicle->manufacturer->logoUrl() }}" alt="{{ $vehicle->manufacturer->name }}"
             class="w-12"
        >
    @else
        <div class="size-12 bg-gray-200 rounded flex items-center justify-center">
            <x-lucide-car class="size-8 text-gray-500 mt-0.5 shrink-0"/>
        </div>
    @endif
    <div class="flex flex-col leading-tight">
        <div class="font-semibold">
            {{ $vehicle->registration_date->year }} {{ $vehicle->make }}
        </div>
        <div>{{ $vehicle->model }}</div>
        <div class="text-xs text-gray-500">{{ $vehicle->derivative }}</div>
    </div>
</div>
