@props([
    'heading',
    'expired' => null,
    'key' => \Illuminate\Support\Str::random(10),
    'buttonText' => 'View',
    'buttonIcon' => null,
    'sale' => null,
    'href' => null,
])
<flux:card {{ $attributes->class(['bg-gray-50 pb-20 overflow-hidden']) }}>
    <div class="flex items-start justify-between">
        <div>
            <flux:heading accent size="xl" level="2" class="mb-2">{{ $heading }}</flux:heading>
            @if($sale->expiryDate()?->isPast())
                <div
                    class="-ml-6 bg-red-100 flex items-center gap-4 rounded-r-lg border-l-4 border-accent px-4 py-2 mb-2"
                >
                    <flux:icon name="exclamation-circle" class="text-accent"/>
                    <flux:subheading class="!text-accent font-semibold">
                        Expired {{ $sale->expiryDate()->format('d F Y') }}</flux:subheading>
                </div>
            @endif
            <flux:heading size="lg">{{ $sale->vehicle->vrm }}</flux:heading>
            <flux:subheading>{{ $sale->vehicle->details }} {{ $sale->vehicle->derivative }}</flux:subheading>
        </div>
        <div>
            @if($href)
                <flux:button variant="primary"
                             :href="$href"
                             :icon-trailing="$buttonIcon"
                             size="sm"
                >{{ $buttonText }}</flux:button>
            @else
                <flux:button variant="primary"
                             @click="visibleKey = (visibleKey === '{{ $key }}' ? null : '{{ $key }}')"
                             size="sm"
                >{{ $buttonText }}
                    <flux:icon name="chevron-right" variant="micro"
                               class="transition-transform duration-200"
                               ::class="{'transform rotate-90': visibleKey === '{{ $key }}'}"
                    />
                </flux:button>
            @endif
        </div>
    </div>
    @if($slot->isNotEmpty())
        <div x-cloak x-show="visibleKey === '{{ $key }}'" x-collapse>
            <flux:separator class="my-8"/>
            {{ $slot }}
        </div>
    @endif
</flux:card>
