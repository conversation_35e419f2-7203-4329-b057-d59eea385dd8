<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    >
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600&display=swap" rel="stylesheet"/>
    <title>{{ $title ?? 'Repairer Portal' }} - {{ config('app.name') }}</title>
    @vite('resources/css/app.css')
    @fluxStyles
</head>
<body class="bg-gray-100 dark:bg-zinc-800">
<div class="fixed top-0 left-0 right-0 py-2.5 px-6 bg-zinc-900 text-white text-accent font-semibold z-20">
    <p class="text-center">
        Please note: Customers are not able to access this portal yet. We are still working on
        it and have provided it for testing and feedback.
    </p>
</div>
<div class="mt-16 my-4 max-lg:mt-20 max-lg:mb-4 max-w-6xl mx-auto px-6">
    <flux:brand wire:navigate :href="route('customer-portal.home')"
                :current="request()->routeIs('customer-portal.documents')"
                logo="{{ asset('assets/protegoautocare.svg') }}" class="dark:hidden"
    />
    <flux:brand wire:navigate :href="route('customer-portal.home')"
                :current="request()->routeIs('customer-portal.documents')"
                logo="{{ asset('assets/protegoautocare-dark.svg') }}" class="hidden dark:flex"
    />
</div>
<div class="my-6 max-lg:my-0 bg-white lg:rounded-3xl shadow pb-10 max-w-6xl mx-auto overflow-hidden">
    <flux:main container>
        {{ $slot }}
    </flux:main>
</div>
<flux:toast position="top right" class="pt-24"/>
@vite('resources/js/app.js')
@fluxScripts
</body>
</html>
