@props([
    'name' => $attributes->whereStartsWith('wire:model')->first(),
    'label' => null,
    'prefix' => '£'
])
<flux:field>
    <div class="flex items-center gap-4">
        @if($label)
        <flux:label class="w-56">{{ $label }}</flux:label>
        @endif
        <flux:input.group>
            @if($prefix)
            <flux:input.group.prefix>{{ $prefix }}</flux:input.group.prefix>
            @endif
            <flux:input {{ $attributes }} />
        </flux:input.group>
    </div>
    <flux:error :name="$name"/>
</flux:field>
