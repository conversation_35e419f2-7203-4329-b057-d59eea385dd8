@props([
    'label' => null,
    'value' => null,
    'placeholder' => 'N/A',
    'variant' => 'inline'
])
@php
$classes = Flux::classes()
    ->add('flex gap-2 justify-between text-gray-600 text-sm py-2')
    ->add(['items-center' => $variant === 'inline'])
    ->add(['flex-col' => $variant === 'stacked'])
@endphp
<div {{ $attributes->class($classes) }}>
    <dd class="text-zinc-500">{{ $label }}</dd>
    @if ($slot->isNotEmpty())
        <dt>{{ $slot }}</dt>
    @elseif (isset($value))
        <dt class="font-semibold">{{ $value }}</dt>
    @else
        <dt class="text-gray-400">{{ $placeholder }}</dt>
    @endif
</div>
