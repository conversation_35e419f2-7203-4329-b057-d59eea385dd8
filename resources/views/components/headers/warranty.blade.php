<div class="flex items-center space-x-2">
    <span>Warranty {{ $warranty->product->getLabel() }}</span>
    <x-filament::badge :color="$warranty->status->color()">
        {{ ucwords(str_replace("_", " ", $warranty->status->label())) }}
    </x-filament::badge>
    @if($warranty->isRecurring())
        <x-filament::badge color="success">Subscription</x-filament::badge>
    @endif
    <x-headers.funding-type-badge :warranty="$warranty"/>
</div>
