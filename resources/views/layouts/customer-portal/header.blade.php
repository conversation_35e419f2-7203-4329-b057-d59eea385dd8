<flux:header container class="bg-white dark:bg-zinc-900">
    <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />


    <flux:navbar class="-mb-px max-lg:hidden">
        <flux:navbar.item wire:navigate :href="route('customer-portal.home')" :current="request()->routeIs('customer-portal.home')">My Products</flux:navbar.item>
        <flux:navbar.item wire:navigate :href="route('customer-portal.offers')" :current="request()->routeIs('customer-portal.offers*')">My Offers</flux:navbar.item>
        <flux:navbar.item wire:navigate :href="route('customer-portal.documents')" :current="request()->routeIs('customer-portal.documents')">Documents</flux:navbar.item>
        <flux:navbar.item wire:navigate :href="route('customer-portal.update-details')" :current="request()->routeIs('customer-portal.update-details')">Make a Change</flux:navbar.item>
        <flux:navbar.item wire:navigate :href="route('customer-portal.claims')" :current="request()->routeIs('customer-portal.claims*')">Claims</flux:navbar.item>
        <flux:navbar.item wire:navigate :href="route('customer-portal.payments')" :current="request()->routeIs('customer-portal.payments')">Payments</flux:navbar.item>
    </flux:navbar>

    <flux:spacer />

    <flux:navbar>
        <flux:navbar.item icon="arrow-right-start-on-rectangle" :href="route('customer-portal.logout')">Logout</flux:navbar.item>
    </flux:navbar>
</flux:header>

<flux:sidebar stashable sticky class="lg:hidden bg-zinc-50 dark:bg-zinc-900 border-r border-zinc-200 dark:border-zinc-700">
    <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

    <flux:brand wire:navigate :href="route('customer-portal.documents')" :current="request()->routeIs('customer-portal.documents')" logo="{{ asset('assets/protegoautocare.svg') }}" class="px-2 dark:hidden" />
    <flux:brand wire:navigate :href="route('customer-portal.documents')" :current="request()->routeIs('customer-portal.documents')" logo="{{ asset('assets/protegoautocare-dark.svg') }}" class="px-2 hidden dark:flex" />

    <flux:navlist variant="outline">
        <flux:navlist.item wire:navigate :href="route('customer-portal.home')" :current="request()->routeIs('customer-portal.home')">My Products</flux:navlist.item>
        <flux:navlist.item wire:navigate :href="route('customer-portal.documents')" :current="request()->routeIs('customer-portal.documents')">Documents</flux:navlist.item>
        <flux:navlist.item wire:navigate :href="route('customer-portal.update-details')" :current="request()->routeIs('customer-portal.update-details')">Make a Change</flux:navlist.item>
        <flux:navlist.item wire:navigate :href="route('customer-portal.claims')" :current="request()->routeIs('customer-portal.claims')">Claims</flux:navlist.item>
        <flux:navlist.item wire:navigate :href="route('customer-portal.payments')" :current="request()->routeIs('customer-portal.payments')">Payments</flux:navlist.item>
    </flux:navlist>

    <flux:spacer />

    <flux:navlist variant="outline">
        <flux:navlist.item wire:navigate :href="route('customer-portal.documents')" :current="request()->routeIs('customer-portal.payments')">Payments</flux:navlist.item>
    </flux:navlist>
</flux:sidebar>
