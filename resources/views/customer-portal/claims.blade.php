<x-layouts.app title="My Claims">
    <div class="max-w-3xl mx-auto">
        <flux:heading size="lg" level="1" class="mb-6 mx-6">This is where you manage your warranty claims</flux:heading>
        <div class="grid grid-cols-1 gap-4" x-data="{ visibleKey: 'first' }">
            @if($hasLiveWarranty && !$hasInProgressClaim)
                <flux:card class="flex items-start justify-between gap-4 bg-gray-50">
                    <div class="w-96">
                        <flux:heading size="lg" accent>Make a new claim</flux:heading>
                        <flux:subheading>
                            Need to register a new claim or report a fault to us?
                            We're here every step of the way.
                        </flux:subheading>
                    </div>
                    <div>
                        <flux:button variant="primary" size="sm"
                                     :href="route('customer-portal.claims.create')"
                                     wire:navigate
                        >Make a claim
                        </flux:button>
                    </div>
                </flux:card>
            @endif

            <div class="grid grid-cols-1 gap-4" x-data="{ visibleKey: '0' }">
                @foreach($claims as $claim)
                    <x-product-card :key="$loop->index"
                                    :sale="$claim->warranty->sale"
                                    :href="route('customer-portal.claims.show', $claim)"
                    >
                        <x-slot:heading>
                            <div class="flex items-center gap-2">
                                <span>Claim reference: {{ $claim->reference }}</span>
                                <flux:badge :color="$claim->status->colorCustomerPortal()" size="sm" inset="top bottom">
                                    {{ ucwords($claim->status->value) }}
                                </flux:badge>
                            </div>
                        </x-slot:heading>
                    </x-product-card>
                @endforeach
            </div>

            @if(!$hasLiveWarranty && $claims->isEmpty())
                <flux:card class="flex items-start justify-between gap-4 bg-gray-50">
                    <div class="w-96">
                        <flux:heading size="lg" accent>No live warranty</flux:heading>
                        <flux:subheading>
                            You have no mechanical warranty product to claim against.
                        </flux:subheading>
                    </div>
                </flux:card>
            @endif
        </div>
    </div>
</x-layouts.app>
