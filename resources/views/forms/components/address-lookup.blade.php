<div x-init="() => {
    IdealPostcodes.AddressFinder.setup({
        apiKey: window.filamentData.idealPostcodes.key,
        outputFields: {
            line_1: '#line_1',
            line_2: '#line_2',
            line_3: '#line_3',
            county: '#county',
            country: '#country',
            post_town: '#post_town',
            postcode: '#postcode',
        }
    });
    }"
>
    @include('filament-forms::components.text-input')
</div>
